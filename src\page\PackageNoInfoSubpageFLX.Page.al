page 60013 "Package No. Info. Subpage FLX"
{
    ApplicationArea = All;
    Caption = 'Package No. Info. Subpage';
    PageType = ListPart;
    SourceTable = "Package No. Information";
    Editable = false;
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    ToolTip = 'Specifies the customs declaration number.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the description associated with this line.';
                }
                field(Inventory; Rec.Inventory)
                {
                    ToolTip = 'Specifies the quantity on inventory with this line.';
                }
                field("Sales Order No. FLX"; Rec."Sales Order No. FLX")
                {
                }
            }
        }
    }
}
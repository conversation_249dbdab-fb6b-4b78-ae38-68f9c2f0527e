page 60029 "Shortage List Detail FLX"
{
    ApplicationArea = All;
    Caption = 'Shortage List Detail';
    PageType = List;
    Editable = false;
    SourceTable = "Shortage List Detail FLX";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Date"; Rec.Date)
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Quantity On Reject Location"; Rec."Quantity On Reject Location")
                {
                }
                field("Quantity On Subcontractors"; Rec."Quantity On Subcontractors")
                {
                }
                field("Quantity On UHD Location"; Rec."Quantity On UHD Location")
                {
                }
                field("Quantity On WHSE Location"; Rec."Quantity On WHSE Location")
                {
                }
                field("Register No."; Rec."Register No.")
                {
                }
                field("Register Type"; Rec."Register Type")
                {
                }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name/Description"; Rec."Source Name/Description")
                {
                }
                field("User Id"; Rec."User Id")
                {
                }
            }
        }
    }
}
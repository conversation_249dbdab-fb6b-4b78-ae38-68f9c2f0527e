pageextension 60017 "Location Card FLX" extends "Location Card"
{
    layout
    {
        addafter(Production)
        {
            group("Flexati FLX")
            {
                Caption = 'Flexati';
                field("Quality Control Location FLX"; Rec."Quality Control Location FLX")
                {
                    ApplicationArea = All;
                }
                field("Include In Calculation FLX"; Rec."Include In Calculation FLX")
                {
                    ApplicationArea = All;
                }
                field("Q. C. Accept Required FLX"; Rec."Q. C. Accept Required FLX")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}
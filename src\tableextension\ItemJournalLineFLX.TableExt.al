tableextension 60014 "Item Journal Line FLX" extends "Item Journal Line"
{
    fields
    {
        field(60000; "Production Line No. FLX"; Code[10])
        {
            Caption = 'Production Line No.';
            AllowInCustomizations = Always;
        }
        field(60001; "Starting DateTime FLX"; DateTime)
        {
            Caption = 'Starting DateTime';
            ToolTip = 'Specifies the value of the Starting DateTime field.';
            trigger OnValidate()
            begin
                FlexatiProductionMngt.CalculateRunTime(Rec);
            end;
        }
        field(60002; "Ending DateTime FLX"; DateTime)
        {
            Caption = 'Ending DateTime';
            ToolTip = 'Specifies the value of the Ending DateTime field.';
            trigger OnValidate()
            begin
                FlexatiProductionMngt.CalculateRunTime(Rec);
            end;
        }
    }
    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
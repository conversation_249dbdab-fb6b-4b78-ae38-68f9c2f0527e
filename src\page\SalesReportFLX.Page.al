page 60033 "Sales Report FLX"
{
    ApplicationArea = All;
    Caption = 'Sales Report';
    PageType = List;
    SourceTable = "Prod. Order Line";
    UsageCategory = ReportsAndAnalysis;
    Editable = false;
    //SourceTableView = where("Source Line No. FLX" = filter(<> 0));
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Sales Order Date FLX"; Rec."Sales Order Date FLX")
                {
                }
                field("ID (mm) FLX"; Rec."ID (mm) FLX")
                {
                }
                field("Flexati Shipment Date FLX"; Rec."Flexati Shipment Date FLX")
                {
                }
                field("Planned Shipment Date FLX"; Rec."Planned Shipment Date FLX")
                {
                }
                field("Prod. Order - Line No. FLX"; Rec."Prod. Order - Line No. FLX")
                {
                }
                field("Sales Order - Line No. FLX"; Rec."Sales Order - Line No. FLX")
                {
                }
                field("Stop FLX"; Rec."Stop FLX")
                {
                }
                field("Customer No. FLX"; Rec."Customer No. FLX")
                {
                }
                field("Customer Name FLX"; Rec."Customer Name FLX")
                {
                }
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the value of the Status field.';
                }

                field("Prod. Order No."; Rec."Prod. Order No.")
                {
                    ToolTip = 'Specifies the value of the Prod. Order No. field.';
                }
                field("Source Line No. FLX"; Rec."Source Line No. FLX")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the value of the Item No. field.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the value of the Description field.';
                }
                field("Sales Line Description FLX"; Rec."Sales Line Description FLX")
                {
                }
                field(Brand; Rec."Brand FLX")
                {
                    Editable = false;
                }
                field("Brand Description"; Rec."Brand Description FLX")
                {
                    Editable = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the value of the Quantity field.';
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ToolTip = 'Specifies the value of the Unit of Measure Code field.';
                }
                field("Sales Unit Price FLX"; Rec."Sales Unit Price FLX")
                {
                }
                field("Currency Code FLX"; Rec."Currency Code FLX")
                {
                }
                field("Sales Unit of Measure Code FLX"; Rec."Sales Unit of Measure Code FLX")
                {
                }
                //field("Unit Cost"; Rec."Unit Cost")
                //{
                //    ToolTip = 'Specifies the value of the Unit Cost field.';
                //}
                field("Hose Length FLX"; Rec."Hose Length FLX")
                {
                }
                field("Piece FLX"; Rec."Piece FLX")
                {
                }
                field("Your Reference FLX"; Rec."Your Reference FLX")
                {
                }
                field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
                {
                }
                field("Inventory FLX"; Rec."Inventory FLX")
                {
                }

                field("FG Qty. FLX"; Rec."FG Qty. FLX")
                {
                }
                field("KALITE Qty. FLX"; Rec."KALITE Qty. FLX")
                {
                }
                field("YURUMEYEN Qty. FLX"; Rec."YURUMEYEN Qty. FLX")
                {
                }
                field("UHD Qty. FLX"; Rec."UHD Qty. FLX")
                {
                }
                field("ISKARTA Qty. FLX"; Rec."ISKARTA Qty. FLX")
                {
                }
                field("SUPHELI Qty. FLX"; Rec."SUPHELI Qty. FLX")
                {
                }
                field("Sales Order Qty. FLX"; Rec."Sales Order Qty. FLX")
                {
                }
                field("Shipped Qty. FLX"; Rec."Shipped Qty. FLX")
                {
                }

                field("Qty. to Ship FLX"; Rec."Sales Order Qty. FLX" - Abs(Rec."Shipped Qty. FLX"))
                {
                    Caption = 'Qty. to Ship';
                    ToolTip = 'Specifies the value of the Qty. to Ship FLX field.';
                }
                field(PositiveRemaningQty; Rec.Quantity - FlexatiProductionMngt.CalculatePositiveFinishedQtyFromProdOrderLine(Rec))
                {
                    Caption = 'Positive Remaining Quantity';
                    ToolTip = 'Specifies the value of the Positive Remaining Quantity field.';
                }

                field("Line Note FLX"; Rec."Line Note FLX")
                {
                }
                field("Note FLX"; Rec."Note FLX")
                {
                }
                //field("Completely Shipped New FLX"; Rec."Completely Shipped New FLX")
                //{
                //}
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(UpdateBrandCodes)
            {
                ApplicationArea = All;
                Caption = 'Update Brand Codes';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateDescription;
                PromotedOnly = true;
                ToolTip = 'Executes the Update Brand Codes action.';
                trigger OnAction()
                begin
                    UpdateBrandCode();
                end;
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetFilter(Status, '%1|%2', Rec.Status::Released, Rec.Status::"Firm Planned");
        Rec.SetFilter("Prod. Order No.", 'STSP*');
    end;

    procedure UpdateBrandCode()
    var
        UpdateMsg: Label 'Brand Codes Updated';
    begin
        ProdOrderLine.Reset();
        ProdOrderLine.SetRange(Status, ProdOrderLine.Status::Released);
        if ProdOrderLine.FindSet() then
            repeat
                ProdOrderLine."Brand FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'LL');
                ProdOrderLine."Brand Description FLX" := FlexatiProductionMngt.GetBomItemNoDesc_ByItemCategoryCode(ProdOrderLine, 'LL');
                ProdOrderLine."Cloth FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'TR');
                ProdOrderLine."Paste FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'RC');
                ProdOrderLine."Wire FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'WR');
                ProdOrderLine.Modify(true);
            until ProdOrderLine.Next() = 0;
        Message(UpdateMsg);
    end;

    var
        ProdOrderLine: Record "Prod. Order Line";
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
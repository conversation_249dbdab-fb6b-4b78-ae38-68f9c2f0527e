codeunit 60004 "Package Trans. Mgt. FLX"
{
    procedure ProcessBarcode(var PackageTransferHeader: Record "Package Transfer Header FLX")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageTransferHeader.Barcode = '' then
            exit;

        // PackageNoInformation.SetRange("Parent Package No. FLX", PackageTransferHeader.Barcode);
        // if PackageNoInformation.FindSet(false) then
        //     repeat
        //         CreatePackageTransferLine(PackageTransferHeader, PackageNoInformation);
        //     until PackageNoInformation.Next() = 0
        // else begin
        //PackageNoInformation.Reset();
        PackageNoInformation.SetRange("Package No.", PackageTransferHeader.Barcode);
        PackageNoInformation.FindFirst();
        CreatePackageTransferLine(PackageTransferHeader, PackageNoInformation);
        // end;
        PackageTransferHeader.Barcode := '';
    end;

    procedure CreateItemReclassRecords(PackageTransferHeader: Record "Package Transfer Header FLX")
    var
        ItemJournalBatch: Record "Item Journal Batch";
        ItemJournalLine: Record "Item Journal Line";
        PackageTransferLine: Record "Package Transfer Line FLX";
        NoSeries: Codeunit "No. Series";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        DocumentNo: Code[20];
        ItemJournalLineNo: Integer;
        NoLinesFoundErr: Label 'Satır Yok';
    begin
        // PackageTransferHeader.CalcFields("Transferring to Prod. Location");
        // if PackageTransferHeader."Transferring to Prod. Location" then
        //     PackageTransferHeader.TestField("Production Order No.");

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        if not PackageTransferLine.FindSet() then
            Error(NoLinesFoundErr);

        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Package Transfer Template Name");

        UserSetup.Get(CopyStr(UserId(), 1, 50));
        UserSetup.TestField("Package Trns. Batch Name FLX");

        //FlexatiSetup.TestField("Package Transfer Batch Name");

        ItemJournalBatch.Get(FlexatiSetup."Package Transfer Template Name", UserSetup."Package Trns. Batch Name FLX");
        if ItemJournalBatch."No. Series" <> '' then begin
            Clear(NoSeries);
            //DocumentNo := NoSeriesManagement.TryGetNextNo(ItemJournalBatch."No. Series", WorkDate());
            DocumentNo := NoSeries.PeekNextNo(ItemJournalBatch."No. Series", WorkDate());
        end;

        ItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Package Transfer Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", UserSetup."Package Trns. Batch Name FLX");
        if ItemJournalLine.FindLast() then
            ItemJournalLineNo := ItemJournalLine."Line No." + 10000
        else
            ItemJournalLineNo := 10000;

        repeat
            PackageTransferOrderReadingChecks(PackageTransferHeader, PackageTransferLine."Package No.");

            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := FlexatiSetup."Package Transfer Template Name";
            ItemJournalLine."Journal Batch Name" := UserSetup."Package Trns. Batch Name FLX";
            ItemJournalLine."Line No." := ItemJournalLineNo;
            //ItemJournalLine.SetUpNewLine(ItemJournalLine);
            ItemJournalLine.Insert(true);
            //Commit();

            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Transfer);
            ItemJournalLine."Posting Date" := PackageTransferHeader."Posting Date";
            ItemJournalLine."Document Date" := PackageTransferHeader."Posting Date";
            ItemJournalLine."Document No." := DocumentNo;
            ItemJournalLine."Posting No. Series" := ItemJournalBatch."Posting No. Series";
            ItemJournalLine.Validate("Item No.", PackageTransferLine."Item No.");
            ItemJournalLine.Validate("Variant Code", PackageTransferLine."Variant Code");
            ItemJournalLine.Validate("Location Code", PackageTransferLine."Transfer-from Code");
            ItemJournalLine.Validate("Bin Code", PackageTransferLine."Transfer-from Bin Code");
            ItemJournalLine.Validate("New Location Code", PackageTransferLine."Transfer-to Code");
            ItemJournalLine.Validate("New Bin Code", PackageTransferLine."Transfer-To Bin Code");
            ItemJournalLine.Validate(Quantity, PackageTransferLine.Quantity);

            ItemJournalLine.Validate("Lot No.", PackageTransferLine."Lot No.");
            ItemJournalLine.Validate("Package No.", PackageTransferLine."Package No.");

            ItemJournalLine.Modify(true);

            AssignLotAndPackageNoToItemJournalLine(ItemJournalLine, PackageTransferLine."Lot No.", PackageTransferLine."Package No.");
            ItemJournalLineNo += 10000;

        // PackageNoInformation.Get(PackageTransferLine."Item No.", PackageTransferLine."Variant Code", PackageTransferLine."Package No.");
        // //PackageNoInformation.Validate("Production Order No. FLX", PackageTransferHeader."Production Order No.");
        // PackageNoInformation.Modify(true);
        until PackageTransferLine.Next() = 0;

        //Codeunit.Run(Codeunit::"Item Jnl.-Post Batch", ItemJournalLine); //Post item journal line
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

        PackageTransferHeader.Validate(Posted, true);
        PackageTransferHeader.Modify(true);
    end;

    local procedure AssignLotAndPackageNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; LotNo: Code[50]; PackageNo: Code[50])
    var
        TempReservEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservStatus: Enum "Reservation Status";
    begin
        TempReservEntry.Init();
        TempReservEntry."Entry No." := 1;
        TempReservEntry."Lot No." := LotNo; //use Serial No. for SN
        TempReservEntry."Package No." := PackageNo;
        TempReservEntry.Quantity := ItemJournalLine.Quantity;

        // TempReservEntry."New Lot No." := LotNo;
        // TempReservEntry."New Package No." := PackageNo;

        //TempReservEntry."Expiration Date" := Today();
        TempReservEntry.Insert(false);

        //CreateReservEntry.SetDates(0D, TempReservEntry."Expiration Date");

        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer then //movement
            CreateReservEntry.SetNewTrackingFromItemJnlLine(ItemJournalLine);

        ItemJournalLine."Lot No." := '';
        ItemJournalLine."Package No." := '';
        ItemJournalLine.Modify(false);

        CreateReservEntry.CreateReservEntryFor(
          Database::"Item Journal Line", ItemJournalLine."Entry Type".AsInteger(),
          ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name", 0, ItemJournalLine."Line No.", ItemJournalLine."Qty. per Unit of Measure",
          TempReservEntry.Quantity, TempReservEntry.Quantity * ItemJournalLine."Qty. per Unit of Measure", TempReservEntry);

        CreateReservEntry.CreateEntry(
          ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Location Code", '', 0D, 0D, 0, ReservStatus::Surplus);
    end;

    local procedure CreatePackageTransferLine(var PackageTransferHeader: Record "Package Transfer Header FLX"; var PackageNoInformation: Record "Package No. Information")
    var
        PackageTransferLine: Record "Package Transfer Line FLX";
    begin
        PackageTransferOrderReadingChecks(PackageTransferHeader, PackageNoInformation."Package No.");

        PackageNoInformation.CalcFields(Inventory, "Location Code FLX");

        PackageTransferLine.Init();
        PackageTransferLine."Document No." := PackageTransferHeader."No.";
        PackageTransferLine.Insert(true);
        PackageTransferLine.Validate("Package No.", PackageNoInformation."Package No.");
        PackageTransferLine.Validate("Item No.", PackageNoInformation."Item No.");
        PackageTransferLine.Validate("Variant Code", PackageNoInformation."Variant Code");
        PackageTransferLine.Validate(Description, PackageNoInformation.Description);
        PackageTransferLine.Validate(Quantity, PackageNoInformation.Inventory);
        PackageTransferLine.Validate("Lot No.", PackageNoInformation."Lot No. FLX");
        PackageTransferLine.Validate("Transfer-from Code", PackageNoInformation."Location Code FLX");
        PackageTransferLine.Validate("Transfer-from Bin Code", GetBinCodeFromPackageNoInformation(PackageNoInformation));
        PackageTransferLine.Validate("Transfer-to Code", PackageTransferHeader."Transfer-to Code");
        PackageTransferLine.Validate("Transfer-To Bin Code", PackageTransferHeader."Transfer-To Bin Code");
        PackageTransferLine.Modify(true);
    end;

    procedure GetBinCodeFromPackageNoInformation(PackageNoInformation: Record "Package No. Information"): Code[20]
    var
        WarehouseEntry: Record "Warehouse Entry";
    begin
        PackageNoInformation.CalcFields("Location Code FLX");

        WarehouseEntry.SetRange("Item No.", PackageNoInformation."Item No.");
        WarehouseEntry.SetRange("Variant Code", PackageNoInformation."Variant Code");
        WarehouseEntry.SetRange("Package No.", PackageNoInformation."Package No.");
        WarehouseEntry.SetRange("Location Code", PackageNoInformation."Location Code FLX");
        WarehouseEntry.SetFilter(Quantity, '>0');
        if WarehouseEntry.FindLast() then
            exit(WarehouseEntry."Bin Code");

        exit('');
    end;

    procedure CreatePackageTransferOrderForSelectedPackageNoInformationRecords(var PackageNoInformation: Record "Package No. Information"; TransferToLocationCode: Code[10])
    var
        PackageTransferHeader: Record "Package Transfer Header FLX";
    //SuccesMsg: Label '%1 Packages has been transferred succesfully.', Comment = '%1="Package No. Information".Count';
    begin
        PackageTransferHeader.Init();
        PackageTransferHeader.Insert(true);
        PackageTransferHeader.Validate("Transfer-to Code", TransferToLocationCode);
        PackageTransferHeader.Modify(true);

        PackageNoInformation.FindSet();
        repeat
            PackageTransferHeader.Validate(Barcode, PackageNoInformation."Package No.");
        until PackageNoInformation.Next() = 0;

        //Page.Run(Page::"Package Transfer Order FLX", PackageTransferHeader);
        PageManagement.PageRun(PackageTransferHeader);

        //CreateItemReclassRecords(PackageTransferHeader);

        //Message(SuccesMsg, PackageNoInformation.Count);
    end;

    procedure PackageTransferOrderReadingChecks(PackageTransferHeader: Record "Package Transfer Header FLX"; CoilPackageNo: Code[50])
    var
        Location: Record Location;
        PackageNoInformation: Record "Package No. Information";
        TransferRoute: Record "Transfer Route";
        InventoryErr: Label 'Inventory can not be zero. Package No.: %1', Comment = '%1="Package No. Information"."Package No."';
        SameBinCodeErr: Label 'You can not transfer to same Bin Code. Package No.: %1', Comment = '%1="Package No. Information"."Package No."';
        TransferRouteNotFoundErr: Label 'You can not transfer a package from %1 to %2. Package No.: %3', Comment = '%1="Package No. Information"."Location Code FLX"; %2="Package Transfer Header FLX"."Transfer-to Code"; %3="Package No. Information"."Package No."';
    //PackageTypeErr: Label 'You can not transfer a package with package type Bulk or Head. Package No.: %1', Comment = '%1="Package No. Information"."Package No."';
    begin
        PackageNoInformation.SetRange("Package No.", CoilPackageNo);
        PackageNoInformation.FindFirst();
        PackageNoInformation.CalcFields(Inventory);
        if PackageNoInformation.Inventory = 0 then
            Error(InventoryErr, PackageNoInformation."Package No.");

        if GetBinCodeFromPackageNoInformation(PackageNoInformation) = PackageTransferHeader."Transfer-To Bin Code" then
            Error(SameBinCodeErr, PackageNoInformation."Package No.");

        Location.Get(PackageTransferHeader."Transfer-to Code");
        if Location."Q. C. Accept Required FLX" then
            PackageNoInformation.TestField("Quality Control Status FLX", PackageNoInformation."Quality Control Status FLX"::Accept);

        // if (PackageNoInformation."Package Type FLX" = PackageNoInformation."Package Type FLX"::Head) or (PackageNoInformation."Package Type FLX" = PackageNoInformation."Package Type FLX"::Bulk) then
        //     Error(PackageTypeErr, PackageNoInformation."Package No.");
        PackageNoInformation.TestField("Package Type FLX", PackageNoInformation."Package Type FLX"::Coil);

        PackageNoInformation.TestField("Parent Package No. FLX", '');

        PackageNoInformation.CalcFields("Location Code FLX");
        if not TransferRoute.Get(PackageNoInformation."Location Code FLX", PackageTransferHeader."Transfer-to Code") then
            Error(TransferRouteNotFoundErr, PackageNoInformation."Location Code FLX", PackageTransferHeader."Transfer-to Code", PackageNoInformation."Package No.");
    end;

    var
        FlexatiSetup: Record "Flexati Setup FLX";
        UserSetup: Record "User Setup";
        PageManagement: Codeunit "Page Management";
}
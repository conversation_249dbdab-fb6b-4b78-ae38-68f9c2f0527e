report 60002 "PostedSalesInvoice FLX"
{
    Caption = 'Sales - Invoice';
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/PostedSalesInvoice.rdlc';
    ApplicationArea = Basic, Suite;
    EnableHyperlinks = true;
    UsageCategory = ReportsAndAnalysis;
    Permissions = tabledata "Sales Shipment Buffer" = rimd;
    //PreviewMode = PrintLayout;
    //WordMergeDataItem = Header;
    //DefaultRenderingLayout = "StandardSalesInvoice.docx";

    dataset
    {
        dataitem(Header; "Sales Invoice Header")
        {
            DataItemTableView = sorting("No.");
            RequestFilterFields = "No.", "Sell-to Customer No.", "No. Printed";
            RequestFilterHeading = 'Posted Sales Invoice';
            column(Description_Line_Lbl; Description_Line_Lbl)
            {
            }
            column(SupplierNumber; SupplierNumber)
            {
            }
            column(Quantity_Line_Lbl; Quantity_Line_Lbl)
            {
            }
            column(ItemNo_Line_Lbl; ItemNo_Line_Lbl)
            {
            }
            column(LineAmount_Line_Lbl; LineAmount_Line_Lbl)
            {
            }
            column(LineNoLbl; LineNoLbl)
            {
            }
            column(CustPoLbl; CustPoLbl)
            {
            }
            column(OurPOLbl; OurPOLbl)
            {
            }
            column(UnitPrice_Lbl; UnitPrice_Lbl)
            {
            }
            column(UnitOfMeasure_Lbl; UnitOfMeasure_Lbl)
            {
            }
            column(TotalPackage; TotalPackage)
            {
            }
            column(PackageDesc; PackageDesc)
            {
            }
            column(GrandPallettotaltxt; GrandPallettotaltxt)
            {
            }
            column(TotalGrossWeight; TotalGrossWeight)
            {
            }
            column(TotalNetWeight; TotalNetWeight)
            {
            }
            column(TotalTareWeight; TotalTareWeight)
            {
            }
            column(GrossWeightLbl; GrossWeightLbl)
            {
            }
            column(TareWeightLbl; TareWeightLbl)
            {
            }
            column(NetWeightLbl; NetWeightLbl)
            {
            }
            column(ContainerNumberLbl; ContainerNumberLbl)
            {
            }
            column(CountryOfOriginLbl; CountryOfOriginLbl)
            {
            }
            column(ContainerNumber; ContainerNumber)
            {
            }
            column(CountryOfOrigin; CountryOfOrigin)
            {
            }
            column(plasticwooddesc; plasticwooddesc)
            {
            }
            column(CurrencyCodeHD; CurrencyCode)
            {
            }
            column(PackagingLbl; PackagingLbl)
            {
            }
            column(TotalChargesLbl; TotalChargesLbl)
            {
            }
            column(NetGoodsLbl; NetGoodsLbl)
            {
            }
            column(TotalAmountLbl; TotalAmountLbl)
            {
            }
            column(CompInfoName; CompanyInfo.Name)
            {
            }
            column(CompanyAddress1; CompanyAddr[1])
            {
            }
            column(CompanyAddress2; CompanyAddr[2])
            {
            }
            column(CompanyAddress3; CompanyAddr[3])
            {
            }
            column(CompanyAddress4; CompanyAddr[4])
            {
            }
            column(CompanyAddress5; CompanyAddr[5])
            {
            }
            column(CompanyAddress6; CompanyAddr[6])
            {
            }
            column(CompanyAddress7; CompanyAddr[7])
            {
            }
            column(CompanyAddress8; CompanyAddr[8])
            {
            }
#pragma warning disable AL0432
            column(CompanyHomePage; CompanyInfo."Home Page")
#pragma warning restore AL0432
            {
            }
            column(CompanyEMail; CompanyInfo."E-Mail")
            {
            }
            column(CompanyPicture; CompanyInfo.Picture)
            {
            }
            column(CompanyPhoneNo; CompanyInfo."Phone No.")
            {
            }
            column(CompanyPhoneNo_Lbl; CompanyInfoPhoneNoLbl)
            {
            }
            column(CompanyGiroNo; CompanyInfo."Giro No.")
            {
            }
            column(CompanyGiroNo_Lbl; CompanyInfoGiroNoLbl)
            {
            }
            column(CompanyBankName; CompanyBankAccount.Name)
            {
            }
            column(CompanyBankName_Lbl; CompanyInfoBankNameLbl)
            {
            }
            column(CompanyBankBranchNo; CompanyBankAccount."Bank Branch No.")
            {
            }
            column(CompanyBankBranchNo_Lbl; CompanyBankAccount.FieldCaption("Bank Branch No."))
            {
            }
            column(CompanyBankAccountNo; CompanyBankAccount."Bank Account No.")
            {
            }
            column(CompanyBankAccountNo_Lbl; CompanyInfoBankAccNoLbl)
            {
            }
            column(CompanyIBAN; CompanyBankAccount.IBAN)
            {
            }
            column(CompanyIBAN_Lbl; CompanyBankAccount.FieldCaption(IBAN))
            {
            }
            column(CompanySWIFT; CompanyBankAccount."SWIFT Code")
            {
            }
            column(CompanySWIFT_Lbl; CompanyBankAccount.FieldCaption("SWIFT Code"))
            {
            }
            column(CompanyLogoPosition; CompanyLogoPosition)
            {
            }
            column(CompanyRegistrationNumber; CompanyInfo.GetRegistrationNumber())
            {
            }
            column(CompanyRegistrationNumber_Lbl; CompanyInfo.GetRegistrationNumberLbl())
            {
            }
            column(CompanyVATRegNo; CompanyInfo.GetVATRegistrationNumber())
            {
            }
            column(CompanyVATRegNo_Lbl; CompanyInfo.GetVATRegistrationNumberLbl())
            {
            }
            column(CompanyVATRegistrationNo; CompanyInfo.GetVATRegistrationNumber())
            {
            }
            column(CompanyVATRegistrationNo_Lbl; CompanyInfo.GetVATRegistrationNumberLbl())
            {
            }
            column(CompanyLegalOffice; LegalOfficeTxt)
            {
            }
            column(CompanyLegalOffice_Lbl; LegalOfficeLbl)
            {
            }
            column(CompanyCustomGiro; CustomGiroTxt)
            {
            }
            column(CompanyCustomGiro_Lbl; CustomGiroLbl)
            {
            }
            column(CompanyLegalStatement; LegalStatementLbl)
            {
            }
            column(DisplayAdditionalFeeNote; DisplayAdditionalFeeNote)
            {
            }
            column(CustomerAddress1; CustAddr[1])
            {
            }
            column(CustomerAddress2; CustAddr[2])
            {
            }
            column(CustomerAddress3; CustAddr[3])
            {
            }
            column(CustomerAddress4; CustAddr[4])
            {
            }
            column(CustomerAddress5; CustAddr[5])
            {
            }
            column(CustomerAddress6; CustAddr[6])
            {
            }
            column(CustomerAddress7; CustAddr[7])
            {
            }
            column(CustomerAddress8; CustAddr[8])
            {
            }
            column(CustomerPostalBarCode; FormatAddr.PostalBarCode(1))
            {
            }
            column(YourReference; "Your Reference")
            {
            }
            column(YourReference_Lbl; FieldCaption("Your Reference"))
            {
            }
            column(ShipmentMethodDescription; ShipmentMethod.Description)
            {
            }
            column(ShipmentMethodDescription_Lbl; ShptMethodDescLbl)
            {
            }
            column(ShipmentDate; Format("Shipment Date"))
            {
            }
            column(ShipmentDate_Lbl; FieldCaption("Shipment Date"))
            {
            }
            column(Shipment_Lbl; ShipmentLbl)
            {
            }
            column(ShowShippingAddress; ShowShippingAddr)
            {
            }
            column(ShipToAddress_Lbl; ShiptoAddrLbl)
            {
            }
            column(ShipToAddress1; ShipToAddr[1])
            {
            }
            column(ShipToAddress2; ShipToAddr[2])
            {
            }
            column(ShipToAddress3; ShipToAddr[3])
            {
            }
            column(ShipToAddress4; ShipToAddr[4])
            {
            }
            column(ShipToAddress5; ShipToAddr[5])
            {
            }
            column(ShipToAddress6; ShipToAddr[6])
            {
            }
            column(ShipToAddress7; ShipToAddr[7])
            {
            }
            column(ShipToAddress8; ShipToAddr[8])
            {
            }
            column(SellToContactPhoneNoLbl; SellToContactPhoneNoLbl)
            {
            }
            column(SellToContactMobilePhoneNoLbl; SellToContactMobilePhoneNoLbl)
            {
            }
            column(SellToContactEmailLbl; SellToContactEmailLbl)
            {
            }
            column(BillToContactPhoneNoLbl; BillToContactPhoneNoLbl)
            {
            }
            column(BillToContactMobilePhoneNoLbl; BillToContactMobilePhoneNoLbl)
            {
            }
            column(BillToContactEmailLbl; BillToContactEmailLbl)
            {
            }
            column(SellToContactPhoneNo; SellToContact."Phone No.")
            {
            }
            column(SellToContactMobilePhoneNo; SellToContact."Mobile Phone No.")
            {
            }
            column(SellToContactEmail; SellToContact."E-Mail")
            {
            }
            column(BillToContactPhoneNo; BillToContact."Phone No.")
            {
            }
            column(BillToContactMobilePhoneNo; BillToContact."Mobile Phone No.")
            {
            }
            column(BillToContactEmail; BillToContact."E-Mail")
            {
            }
            column(PaymentTermsDescription; PaymentTerms.Description)
            {
            }
            column(PaymentTermsDescription_Lbl; PaymentTermsDescLbl)
            {
            }
            column(PaymentMethodDescription; PaymentMethod.Description)
            {
            }
            column(PaymentMethodDescription_Lbl; PaymentMethodDescLbl)
            {
            }
            column(BilltoCustumerNo; "Bill-to Customer No.")
            {
            }
            column(BilltoCustomerNo_Lbl; FieldCaption("Bill-to Customer No."))
            {
            }
            column(PostingDate; Format("Posting Date"))
            {
            }
            column(DateText; DateText)
            {

            }
            column(DocumentDate_Lbl; FieldCaption("Document Date"))
            {
            }
            column(DueDate; Format("Due Date", 0, 4))
            {
            }
            column(DueDate_Lbl; FieldCaption("Due Date"))
            {
            }
            column(DocumentNo; "No.")
            {
            }
            column(DocumentNo_Lbl; InvNoLbl)
            {
            }
            column(OurOrderNo; OrderNo)
            {
            }
            column(OrderNo_Lbl; FieldCaption("Order No."))
            {
            }
            column(CustOrderNo; CustOrderNo)
            {
            }
            column(PricesIncludingVAT; "Prices Including VAT")
            {
            }
            column(PricesIncludingVAT_Lbl; FieldCaption("Prices Including VAT"))
            {
            }
            column(PricesIncludingVATYesNo; Format("Prices Including VAT"))
            {
            }
            column(SalesPerson_Lbl; SalespersonLbl)
            {
            }
            column(SalesPersonBlank_Lbl; SalesPersonText)
            {
            }
            column(SalesPersonName; SalespersonPurchaser.Name)
            {
            }
            column(SelltoCustomerNo; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomerNo_Lbl; FieldCaption("Sell-to Customer No."))
            {
            }
            column(VATRegistrationNo; GetCustomerVATRegistrationNumber())
            {
            }
            column(VATRegistrationNo_Lbl; GetCustomerVATRegistrationNumberLbl())
            {
            }
            column(GlobalLocationNumber; GetCustomerGlobalLocationNumber())
            {
            }
            column(GlobalLocationNumber_Lbl; GetCustomerGlobalLocationNumberLbl())
            {
            }
            column(SellToFaxNo; GetSellToCustomerFaxNo())
            {
            }
            column(SellToPhoneNo; SellToPhoneNo)
            {
            }
            column(SellToEmail; SellToEmail)
            {

            }
            column(ShipToPhoneNo; ShipToPhoneNo)
            {
            }
            column(ShipToEmail; ShipToEmail)
            {

            }
            column(PaymentReference; GetPaymentReference())
            {
            }
            column(From_Lbl; FromLbl)
            {
            }
            column(BilledTo_Lbl; BilledToLbl)
            {
            }
            column(ChecksPayable_Lbl; ChecksPayableText)
            {
            }
            column(PaymentReference_Lbl; GetPaymentReferenceLbl())
            {
            }
            column(LegalEntityType; Cust.GetLegalEntityType())
            {
            }
            column(LegalEntityType_Lbl; Cust.GetLegalEntityTypeLbl())
            {
            }
            column(Copy_Lbl; CopyLbl)
            {
            }
            column(EMail_Header_Lbl; EMailLbl)
            {
            }
            column(HomePage_Header_Lbl; HomePageLbl)
            {
            }
            column(InvoiceDiscountBaseAmount_Lbl; InvDiscBaseAmtLbl)
            {
            }
            column(InvoiceDiscountAmount_Lbl; InvDiscountAmtLbl)
            {
            }
            column(LineAmountAfterInvoiceDiscount_Lbl; LineAmtAfterInvDiscLbl)
            {
            }
            column(LocalCurrency_Lbl; LocalCurrencyLbl)
            {
            }
            column(ExchangeRateAsText; ExchangeRateText)
            {
            }
            column(Page_Lbl; PageLbl)
            {
            }
            column(SalesInvoiceLineDiscount_Lbl; SalesInvLineDiscLbl)
            {
            }
            column(Questions_Lbl; QuestionsLbl)
            {
            }
            column(Contact_Lbl; CompanyInfo.GetContactUsText())
            {
            }
            column(DocumentTitle_Lbl; DocumentCaption())
            {
            }
            column(YourDocumentTitle_Lbl; YourSalesInvoiceLbl)
            {
            }
            column(Thanks_Lbl; ThanksLbl)
            {
            }
            column(ShowWorkDescription; ShowWorkDescription)
            {
            }
            column(RemainingAmount; RemainingAmount)
            {
            }
            column(RemainingAmountText; RemainingAmountTxt)
            {
            }
            column(Subtotal_Lbl; SubtotalLbl)
            {
            }
            column(Total_Lbl; TotalLbl)
            {
            }
            column(VATAmount_Lbl; VATAmtLbl)
            {
            }
            column(VATBase_Lbl; VATBaseLbl)
            {
            }
            column(VATAmountSpecification_Lbl; VATAmtSpecificationLbl)
            {
            }
            column(VATClauses_Lbl; VATClausesLbl)
            {
            }
            column(VATIdentifier_Lbl; VATIdentifierLbl)
            {
            }
            column(VATPercentage_Lbl; VATPercentageLbl)
            {
            }
            column(VATClause_Lbl; VATClause.TableCaption())
            {
            }
#pragma warning disable AL0432
            column(PackageTrackingNo; "Package Tracking No.")
            {
            }
            column(PackageTrackingNo_Lbl; FieldCaption("Package Tracking No."))
            {
            }
#pragma warning restore AL0432
            column(ShippingAgentCode; "Shipping Agent Code")
            {
            }
            column(ShippingAgentCode_Lbl; FieldCaption("Shipping Agent Code"))
            {
            }
            column(PaymentInstructions_Txt; PaymentInstructionsTxt)
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(ExternalDocumentNo_Lbl; FieldCaption("External Document No."))
            {
            }
            dataitem("Sales Comment Line"; "Sales Comment Line")
            {
                DataItemLink = "No." = field("No.");
                DataItemLinkReference = Header;
                DataItemTableView = sorting("No.", "Line No.");
                column(Comment_SalesCommentLine; "Sales Comment Line".Comment)
                {
                }
            }
            dataitem(Line; "Sales Invoice Line")
            {
                DataItemLink = "Document No." = field("No.");
                DataItemLinkReference = Header;
                DataItemTableView = sorting("Document No.", "Line No.");
                column(LineNo_Line; "Line No.")
                {
                }
                column(LineNumberNo_Line; LineNumberNo)
                {
                }
                column(AmountExcludingVAT_Line; Amount)
                {
                    AutoFormatExpression = GetCurrencyCode();
                    AutoFormatType = 1;
                }
                column(AmountExcludingVAT_Line_Lbl; FieldCaption(Amount))
                {
                }
                column(AmountIncludingVAT_Line; "Amount Including VAT")
                {
                    AutoFormatExpression = GetCurrencyCode();
                    AutoFormatType = 1;
                }
                column(AmountIncludingVAT_Line_Lbl; FieldCaption("Amount Including VAT"))
                {
                    AutoFormatExpression = GetCurrencyCode();
                    AutoFormatType = 1;
                }
                column(Description_Line; Description)
                {
                }
                column(LineDiscountPercent_Line; "Line Discount %")
                {
                }
                column(LineDiscountPercentText_Line; LineDiscountPctText)
                {
                }
                column(LineAmount_Line; FormattedLineAmount)
                {
                    AutoFormatExpression = GetCurrencyCode();
                    AutoFormatType = 1;
                }
                column(ItemNo_Line; "No.")
                {
                }
                column(ItemReferenceNo_Line; "Item Reference No.")
                {
                }
                column(ItemReferenceNo_Line_Lbl; FieldCaption("Item Reference No."))
                {
                }
                column(ShipmentDate_Line; Format("Shipment Date"))
                {
                }
                column(ShipmentDate_Line_Lbl; PostedShipmentDateLbl)
                {
                }
                column(Quantity_Line; FormattedQuantity)
                {
                }
                column(Type_Line; Format(Type))
                {
                }
                column(UnitPrice; FormattedUnitPrice)
                {
                    AutoFormatExpression = GetCurrencyCode();
                    AutoFormatType = 2;
                }
                column(UnitOfMeasure; "Unit of Measure")
                {
                }
                column(VATIdentifier_Line; "VAT Identifier")
                {
                }
                column(VATIdentifier_Line_Lbl; FieldCaption("VAT Identifier"))
                {
                }
                column(VATPct_Line; FormattedVATPct)
                {
                }
                column(VATPct_Line_Lbl; FieldCaption("VAT %"))
                {
                }
                column(TransHeaderAmount; TransHeaderAmount)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(JobTaskNo_Lbl; JobTaskNoLbl)
                {
                }
                column(JobTaskNo; JobTaskNo)
                {
                }
                column(JobTaskDescription; JobTaskDescription)
                {
                }
                column(JobTaskDesc_Lbl; JobTaskDescLbl)
                {
                }
                column(JobNo_Lbl; JobNoLbl)
                {
                }
                column(JobNo; JobNo)
                {
                }
                column(Unit_Lbl; UnitLbl)
                {
                }
                column(Qty_Lbl; QtyLbl)
                {
                }
                column(Price_Lbl; PriceLbl)
                {
                }
                column(PricePer_Lbl; PricePerLbl)
                {
                }
                dataitem(ShipmentLine; "Sales Shipment Buffer")
                {
                    DataItemTableView = sorting("Document No.", "Line No.", "Entry No.");
                    UseTemporary = true;
                    column(DocumentNo_ShipmentLine; "Document No.")
                    {
                    }
                    column(PostingDate_ShipmentLine; "Posting Date")
                    {
                    }
                    column(PostingDate_ShipmentLine_Lbl; FieldCaption("Posting Date"))
                    {
                    }
                    column(Quantity_ShipmentLine; Quantity)
                    {
                        DecimalPlaces = 0 : 5;
                    }
                    column(Quantity_ShipmentLine_Lbl; FieldCaption(Quantity))
                    {
                    }

                    trigger OnPreDataItem()
                    begin
                        SetRange("Line No.", Line."Line No.");
                    end;
                }
                dataitem(AssemblyLine; "Posted Assembly Line")
                {
                    DataItemTableView = sorting("Document No.", "Line No.");
                    UseTemporary = true;
                    column(LineNo_AssemblyLine; "No.")
                    {
                    }
                    column(Description_AssemblyLine; Description)
                    {
                    }
                    column(Quantity_AssemblyLine; Quantity)
                    {
                        DecimalPlaces = 0 : 5;
                    }
                    column(UnitOfMeasure_AssemblyLine; GetUOMText("Unit of Measure Code"))
                    {
                    }
                    column(VariantCode_AssemblyLine; "Variant Code")
                    {
                    }

                    trigger OnPreDataItem()
                    var
                        ValueEntry: Record "Value Entry";
                    begin
                        Clear(AssemblyLine);
                        if not DisplayAssemblyInformation then
                            CurrReport.Break();
                        GetAssemblyLinesForDocument(
                          AssemblyLine, ValueEntry."Document Type"::"Sales Invoice", Line."Document No.", Line."Line No.");
                    end;
                }

                trigger OnAfterGetRecord()
                begin
                    InitializeShipmentLine();
                    if Type = Type::"G/L Account" then
                        "No." := '';
                    OrderNo := '';
                    CustOrderNo := '';
                    SalesShipmentHD.Reset();
                    if (Line."Shipment No." <> '') and (SalesShipmentHD.Get(Line."Shipment No.")) then begin
                        OrderNo := SalesShipmentHD."Order No.";
                        CustOrderNo := SalesShipmentHD."Your Reference";
                    end;
                    if Line.Quantity > 0 then
                        LineNumberNo += 1;

                    OnBeforeLineOnAfterGetRecord(Header, Line);

                    if "Line Discount %" = 0 then
                        LineDiscountPctText := ''
                    else
                        //LineDiscountPctText := StrSubstNo('%1%', -Round("Line Discount %", 0.1));
                        LineDiscountPctText := Format(-Round("Line Discount %", 0.1));

                    //InsertVATAmountLine(VATAmountLine, Line); //mark removal

                    TransHeaderAmount += PrevLineAmount;
                    PrevLineAmount := "Line Amount";
                    TotalSubTotal += "Line Amount";
                    TotalInvDiscAmount -= "Inv. Discount Amount";
                    TotalAmount += Amount;
                    TotalAmountVAT += "Amount Including VAT" - Amount;
                    TotalAmountInclVAT += "Amount Including VAT";
                    TotalPaymentDiscOnVAT += -("Line Amount" - "Inv. Discount Amount" - "Amount Including VAT");

                    if FirstLineHasBeenOutput then
                        Clear(DummyCompanyInfo.Picture);
                    FirstLineHasBeenOutput := true;

                    JobNo := "Job No.";
                    JobTaskNo := "Job Task No.";

                    if JobTaskNo <> '' then begin
                        JobTaskNoLbl := JobTaskNo2Lbl;
                        //JobTaskDescription := GetJobTaskDescription(JobNo, JobTaskNo);
                        JobTaskDescription := GetJobTaskDescription();
                    end else begin
                        JobTaskDescription := '';
                        JobTaskNoLbl := '';
                    end;

                    if JobNo <> '' then
                        JobNoLbl := JobNo2Lbl
                    else
                        JobNoLbl := '';

                    FormatLineValues(Line);
                end;

                trigger OnPreDataItem()
                begin
                    //VATAmountLine.DeleteAll(true); //mark removal
                    //VATClauseLine.DeleteAll(true); //mark removal
                    ShipmentLine.Reset();
                    ShipmentLine.DeleteAll(false);
                    MoreLines := Find('+');
                    while MoreLines and (Description = '') and ("No." = '') and (Quantity = 0) and (Amount = 0) do
                        MoreLines := Next(-1) <> 0;
                    if not MoreLines then
                        CurrReport.Break();
                    SetRange("Line No.", 0, "Line No.");
                    TransHeaderAmount := 0;
                    PrevLineAmount := 0;
                    FirstLineHasBeenOutput := false;
                    DummyCompanyInfo.Picture := CompanyInfo.Picture;

                    OnAfterLineOnPreDataItem(Header, Line);
                end;
            }
            dataitem(GTIPLoop; Integer)
            {
                DataItemTableView = sorting(Number);
                column(TmpSalesLineGTIP_Tariff_No; TempSalesLineGTIP."Description 2")
                {
                }
                column(TmpSalesLineGTIP_Quantity; TempSalesLineGTIP.Quantity)
                {
                }
                column(TmpSalesLineGTIP_QtytoShip; TempSalesLineGTIP."Quantity (Base)")
                {
                }
                column(TmpSalesLineGTIP_NetWeight; TempSalesLineGTIP."Net Weight")
                {
                }
                column(TmpSalesLineGTIP_GrossWeight; TempSalesLineGTIP."Gross Weight")
                {
                }
                column(TmpSalesLineGTIP_LineAmount; TempSalesLineGTIP."Line Amount")
                {
                }
                trigger OnPreDataItem()
                begin
                    TempSalesLineGTIP.Reset();
                    if TempSalesLineGTIP.FindFirst() then
                        repeat
                            if (farkmiktar_th <> 0) and (toplamKG_th > 0) then begin
                                //MESSAGE('fark : '+FORMAT(farkmiktar_th)+' toplam hesaplanan :'+FORMAT(toplamKG_th)+' tartılan : '+FORMAT(TotalNetWeight));
                                toplammiktar := TempSalesLineGTIP."Net Weight";
                                farkoran_th := toplammiktar / toplamKG_th;
                                hesaplananfark_th := Round(farkmiktar_th * farkoran_th, 0.01);
                                farkdahiltoplam := toplammiktar + hesaplananfark_th;
                                //MESSAGE(TempSalesLineGTIP."Tariff No"+' net weight:'+FORMAT(TempSalesLineGTIP."Net Weight")+' fark dahil:'+FORMAT(farkdahiltoplam));
                                TempSalesLineGTIP."Net Weight" := farkdahiltoplam;
                                TempSalesLineGTIP.Modify(false);
                            end;
                        until TempSalesLineGTIP.Next() = 0;

                    GTIPLoop.SetRange(Number, 1, TempSalesLineGTIP.Count());
                end;

                trigger OnAfterGetRecord()
                begin
                    if GTIPLoop.Number = 1 then
                        TempSalesLineGTIP.FindFirst()
                    else
                        TempSalesLineGTIP.Next();
                end;
            }
            dataitem(WorkDescriptionLines; Integer)
            {
                DataItemTableView = sorting(Number) where(Number = filter(1 .. 99999));
                column(WorkDescriptionLineNumber; Number)
                {
                }
                column(WorkDescriptionLine; WorkDescriptionLine)
                {
                }

                trigger OnAfterGetRecord()
                var
                    TypeHelper: Codeunit "Type Helper";
                begin
                    if (WorkDescriptionInstream.EOS()) then
                        CurrReport.Break();
                    WorkDescriptionLine := TypeHelper.ReadAsTextWithSeparator(WorkDescriptionInstream, TypeHelper.LFSeparator());
                end;

                trigger OnPostDataItem()
                begin
                    Clear(WorkDescriptionInstream)
                end;

                trigger OnPreDataItem()
                begin
                    if not ShowWorkDescription then
                        CurrReport.Break();
                    Header."Work Description".CreateInStream(WorkDescriptionInstream, TextEncoding::UTF8);
                end;
            }
            /*dataitem(VATAmountLine; "VAT Amount Line") //mark removal
            {
                DataItemTableView = sorting("VAT Identifier", "VAT Calculation Type", "Tax Group Code", "Use Tax", Positive);
                UseTemporary = true;
                column(InvoiceDiscountAmount_VATAmountLine; "Invoice Discount Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(InvoiceDiscountAmount_VATAmountLine_Lbl; FieldCaption("Invoice Discount Amount"))
                {
                }
                column(InvoiceDiscountBaseAmount_VATAmountLine; "Inv. Disc. Base Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(InvoiceDiscountBaseAmount_VATAmountLine_Lbl; FieldCaption("Inv. Disc. Base Amount"))
                {
                }
                column(LineAmount_VatAmountLine; "Line Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(LineAmount_VatAmountLine_Lbl; FieldCaption("Line Amount"))
                {
                }
                column(VATAmount_VatAmountLine; "VAT Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(VATAmount_VatAmountLine_Lbl; FieldCaption("VAT Amount"))
                {
                }
                column(VATAmountLCY_VATAmountLine; VATAmountLCY)
                {
                }
                column(VATAmountLCY_VATAmountLine_Lbl; VATAmountLCYLbl)
                {
                }
                column(VATBase_VatAmountLine; "VAT Base")
                {
                    AutoFormatExpression = Line.GetCurrencyCode();
                    AutoFormatType = 1;
                }
                column(VATBase_VatAmountLine_Lbl; FieldCaption("VAT Base"))
                {
                }
                column(VATBaseLCY_VATAmountLine; VATBaseLCY)
                {
                }
                column(VATBaseLCY_VATAmountLine_Lbl; VATBaseLCYLbl)
                {
                }
                column(VATIdentifier_VatAmountLine; "VAT Identifier")
                {
                }
                column(VATIdentifier_VatAmountLine_Lbl; FieldCaption("VAT Identifier"))
                {
                }
                column(VATPct_VatAmountLine; "VAT %")
                {
                    DecimalPlaces = 0 : 5;
                }
                column(VATPct_VatAmountLine_Lbl; FieldCaption("VAT %"))
                {
                }
                column(NoOfVATIdentifiers; Count())
                {
                }

                trigger OnAfterGetRecord()
                begin
                    VATBaseLCY :=
                      GetBaseLCY(
                        Header."Posting Date", Header."Currency Code",
                        Header."Currency Factor");
                    VATAmountLCY :=
                      GetAmountLCY(
                        Header."Posting Date", Header."Currency Code",
                        Header."Currency Factor");

                    TotalVATBaseLCY += VATBaseLCY;
                    TotalVATAmountLCY += VATAmountLCY;
                    TotalVATBaseOnVATAmtLine += "VAT Base";
                    TotalVATAmountOnVATAmtLine += "VAT Amount";

                    if ShowVATClause("VAT Clause Code") then begin
                        VATClauseLine := VATAmountLine;
                        //if VATClauseLine.Insert(true) then;
                        VATClauseLine.Insert(true);
                    end;
                end;

                trigger OnPreDataItem()
                begin
                    Clear(VATBaseLCY);
                    Clear(VATAmountLCY);

                    TotalVATBaseLCY := 0;
                    TotalVATAmountLCY := 0;
                    TotalVATBaseOnVATAmtLine := 0;
                    TotalVATAmountOnVATAmtLine := 0;
                end;
            }
            dataitem(VATClauseLine; "VAT Amount Line") //mark removal
            {
                DataItemTableView = sorting("VAT Identifier", "VAT Calculation Type", "Tax Group Code", "Use Tax", Positive);
                UseTemporary = true;
                column(VATClausesHeader; VATClausesText)
                {
                }
                column(VATIdentifier_VATClauseLine; "VAT Identifier")
                {
                }
                column(Code_VATClauseLine; VATClause.Code)
                {
                }
                column(Code_VATClauseLine_Lbl; VATClause.FieldCaption(Code))
                {
                }
                column(Description_VATClauseLine; VATClauseText)
                {
                }
                column(Description2_VATClauseLine; VATClause."Description 2")
                {
                }
                column(VATAmount_VATClauseLine; "VAT Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(NoOfVATClauses; Count())
                {
                }

                trigger OnAfterGetRecord()
                begin
                    if "VAT Clause Code" = '' then
                        CurrReport.Skip();
                    if not VATClause.Get("VAT Clause Code") then
                        CurrReport.Skip();
                    VATClauseText := VATClause.GetDescriptionText(Header);
                end;

                trigger OnPreDataItem()
                begin
                    if Count = 0 then
                        VATClausesText := ''
                    else
                        VATClausesText := VATClausesLbl;
                end;
            }
            */
            dataitem(ReportTotalsLine; "Report Totals Buffer")
            {
                DataItemTableView = sorting("Line No.");
                UseTemporary = true;
                column(Description_ReportTotalsLine; Description)
                {
                }
                column(Amount_ReportTotalsLine; Amount)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(AmountFormatted_ReportTotalsLine; "Amount Formatted")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(FontBold_ReportTotalsLine; "Font Bold")
                {
                }
                column(FontUnderline_ReportTotalsLine; "Font Underline")
                {
                }

                trigger OnPreDataItem()
                begin
                    CreateReportTotalLines();
                end;
            }
            dataitem(LineFee; Integer)
            {
                DataItemTableView = sorting(Number) order(ascending) where(Number = filter(1 ..));
                column(LineFeeCaptionText; TempLineFeeNoteOnReportHist.ReportText)
                {
                }

                trigger OnAfterGetRecord()
                begin
                    if not DisplayAdditionalFeeNote then
                        CurrReport.Break();

                    if Number = 1 then begin
                        if not TempLineFeeNoteOnReportHist.FindSet() then
                            CurrReport.Break()
                    end else
                        if TempLineFeeNoteOnReportHist.Next() = 0 then
                            CurrReport.Break();
                end;
            }
            /*dataitem(PaymentReportingArgument; "Payment Reporting Argument")
            {
                DataItemTableView = sorting(Key);
                UseTemporary = true;
                column(PaymentServiceLogo; Logo)
                {
                }
                column(PaymentServiceLogo_UrlText; "URL Caption")
                {
                }
                column(PaymentServiceLogo_Url; GetTargetURL())
                {
                }
                column(PaymentServiceText_UrlText; "URL Caption")
                {
                }
                column(PaymentServiceText_Url; GetTargetURL())
                {
                }
            }
            */
            dataitem(LeftHeader; "Name/Value Buffer")
            {
                DataItemTableView = sorting(ID);
                UseTemporary = true;
                column(LeftHeaderName; Name)
                {
                }
                column(LeftHeaderValue; Value)
                {
                }
            }
            dataitem(RightHeader; "Name/Value Buffer")
            {
                DataItemTableView = sorting(ID);
                UseTemporary = true;
                column(RightHeaderName; Name)
                {
                }
                column(RightHeaderValue; Value)
                {
                }
            }
            dataitem(LetterText; Integer)
            {
                DataItemTableView = sorting(Number) where(Number = const(1));
                column(GreetingText; GreetingLbl)
                {
                }
                column(BodyText; BodyLbl)
                {
                }
                column(ClosingText; ClosingLbl)
                {
                }
                column(PmtDiscText; PmtDiscText)
                {
                }

                trigger OnPreDataItem()
                begin
                    PmtDiscText := '';
                    if Header."Payment Discount %" <> 0 then
                        PmtDiscText := StrSubstNo(PmtDiscTxt, Header."Pmt. Discount Date", Header."Payment Discount %");
                end;
            }
            dataitem(Totals; Integer)
            {
                DataItemTableView = sorting(Number) where(Number = const(1));
                column(TotalNetAmount; Format(TotalAmount, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalVATBaseLCY; TotalVATBaseLCY)
                {
                }
                column(TotalAmountIncludingVAT; Format(TotalAmountInclVAT, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalVATAmount; Format(TotalAmountVAT, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalVATAmountLCY; TotalVATAmountLCY)
                {
                }
                column(TotalInvoiceDiscountAmount; Format(TotalInvDiscAmount, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalPaymentDiscountOnVAT; TotalPaymentDiscOnVAT)
                {
                }
                //column(TotalVATAmountText; VATAmountLine.VATAmountText()) //mark removal
                //{
                //}
                column(TotalExcludingVATText; TotalExclVATText)
                {
                }
                column(TotalIncludingVATText; TotalInclVATText)
                {
                }
                column(TotalSubTotal; Format(TotalSubTotal, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalSubTotalMinusInvoiceDiscount; Format(TotalSubTotal + TotalInvDiscAmount, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalText; TotalText)
                {
                }
                column(TotalAmountExclInclVAT; Format(TotalAmountExclInclVATValue, 0, AutoFormat.ResolveAutoFormat(Enum::"Auto Format"::AmountFormat, Header."Currency Code")))
                {
                }
                column(TotalAmountExclInclVATText; TotalAmountExclInclVATTextValue)
                {
                }
                column(TotalVATBaseOnVATAmtLine; TotalVATBaseOnVATAmtLine)
                {
                }
                column(TotalVATAmountOnVATAmtLine; TotalVATAmountOnVATAmtLine)
                {
                }
                column(CurrencyCode; CurrCode)
                {
                }
                column(CurrencySymbol; CurrSymbol)
                {
                }

                trigger OnPreDataItem()
                begin
                    if Header."Prices Including VAT" then begin
                        TotalAmountExclInclVATTextValue := TotalExclVATText;
                        TotalAmountExclInclVATValue := TotalAmount;
                    end else begin
                        TotalAmountExclInclVATTextValue := TotalInclVATText;
                        TotalAmountExclInclVATValue := TotalAmountInclVAT;
                    end;
                end;
            }

            trigger OnAfterGetRecord()
            var
                //TempSalesInvLineGTIP: Record "Sales Invoice Line" temporary;
                //PaymentServiceSetup: Record "Payment Service Setup";
                Currency: Record Currency;
                CurrencyExchangeRate: Record "Currency Exchange Rate";
                GeneralLedgerSetup: Record "General Ledger Setup";
                Country: Record "Country/Region";
                ShipToAddrRec: Record "Ship-to Address";
                //NAV17 var
                //lcSalesInvLine: Record "Sales Invoice Line";
                Item: Record Item;
                //PackageNotFoundErr: Label 'Package Not Found';
                paletcode: Code[20];
            begin
                CurrReport.Language := Languagex.GetLanguageIdOrDefault("Language Code");
                CurrReport.FormatRegion := Languagex.GetFormatRegionOrDefault("Format Region");
                FormatAddr.SetLanguageCode("Language Code");

                if not IsReportInPreviewMode() then
                    Codeunit.Run(Codeunit::"Sales Inv.-Printed", Header);

                OnHeaderOnAfterGetRecordOnAfterUpdateNoPrinted(IsReportInPreviewMode(), Header);

                CalcFields("Work Description");
                ShowWorkDescription := "Work Description".HasValue();

                ChecksPayableText := StrSubstNo(ChecksPayableLbl, CompanyInfo.Name);
                SupplierNumber := '';
                if Cust.Get("Sell-to Customer No.") then
                    SupplierNumber := Cust."Supplier Number FLX";

                FormatAddressFields(Header);
                FormatDocumentFields(Header);
                if not Country.Get(Cust."Country/Region Code") then
                    Country.Init();
                DateText := GetDateText("Posting Date");
                SellToPhoneNo := Cust."Phone No.";
                SellToEmail := Cust."E-Mail";
                CustAddr[1] := Cust.Name;
                CustAddr[2] := Cust."Name 2";
                CustAddr[3] := Cust.Address;
                CustAddr[4] := Cust."Address 2";
                CustAddr[5] := CopyStr(Cust.City + ' ' + Cust.County + ' ' + Country.Name + ' ' + Cust."Post Code", 1, 100);
                CompressArray(CustAddr);
                if not Country.Get("Ship-to Country/Region Code") then
                    Country.Init();
                ShipToPhoneNo := "Ship-to Phone No.";
                ShipToEmail := '';
                if ("Ship-to Code" <> '') and (ShipToAddrRec.Get("Sell-to Customer No.", "Ship-to Code")) then begin
                    ShipToEmail := ShipToAddrRec."E-Mail";
                    ShipToPhoneNo := ShipToAddrRec."Phone No.";
                end;
                ShipToAddr[1] := "Ship-to Name";
                ShipToAddr[2] := "Ship-to Name 2";
                ShipToAddr[3] := "Ship-to Address";
                ShipToAddr[4] := "Ship-to Address 2";
                ShipToAddr[5] := CopyStr("Ship-to City" + ' ' + "Ship-to County" + ' ' + Country.Name + ' ' + "Ship-to Post Code", 1, 100);
                CompressArray(ShipToAddr);
                if not SellToContact.Get("Sell-to Contact No.") then
                    SellToContact.Init();
                if not BillToContact.Get("Bill-to Contact No.") then
                    BillToContact.Init();

                if not CompanyBankAccount.Get(Header."Company Bank Account Code") then
                    CompanyBankAccount.CopyBankFieldsFromCompanyInfo(CompanyInfo);

                FillLeftHeader();
                FillRightHeader();

                if not Cust.Get("Bill-to Customer No.") then
                    Clear(Cust);

                if "Currency Code" <> '' then begin
                    CurrencyExchangeRate.FindCurrency("Posting Date", "Currency Code", 1);
                    CalculatedExchRate :=
                      Round(1 / "Currency Factor" * CurrencyExchangeRate."Exchange Rate Amount", 0.000001);
                    ExchangeRateText := StrSubstNo(ExchangeRateTxt, CalculatedExchRate, CurrencyExchangeRate."Exchange Rate Amount");
                    CurrCode := "Currency Code";
                    if Currency.Get("Currency Code") then
                        CurrSymbol := Currency.GetCurrencySymbol();
                end else
                    if GeneralLedgerSetup.Get() then begin
                        CurrCode := GeneralLedgerSetup."LCY Code";
                        CurrSymbol := GeneralLedgerSetup.GetCurrencySymbol();
                    end;

                GetLineFeeNoteOnReportHist("No.");

                //PaymentServiceSetup.CreateReportingArgs(PaymentReportingArgument, Header);

                CalcFields("Amount Including VAT");
                RemainingAmount := GetRemainingAmount();
                if RemainingAmount = 0 then
                    RemainingAmountTxt := AlreadyPaidLbl
                else
                    if RemainingAmount <> "Amount Including VAT" then
                        RemainingAmountTxt := StrSubstNo(PartiallyPaidLbl, Format(RemainingAmount, 0, '<Precision,2><Standard Format,0>'))
                    else
                        RemainingAmountTxt := '';

                OnAfterGetSalesHeader(Header);

                TotalSubTotal := 0;
                TotalInvDiscAmount := 0;
                TotalAmount := 0;
                TotalAmountVAT := 0;
                TotalAmountInclVAT := 0;
                TotalPaymentDiscOnVAT := 0;
                if ("Order No." = '') and "Prepayment Invoice" then
                    "Order No." := "Prepayment Order No.";

                //NAV17 den gelen
                GLSetup.Get();
                if Header."Currency Code" = '' then
                    CurrencyCode := GLSetup."LCY Code"
                else
                    CurrencyCode := Header."Currency Code";

                ContainerNumber := '';
                plasticwooddesc := WoodenPalletsLbl;
                if plasticpallet then
                    plasticwooddesc := PlasticPalletsLbl;

                lcSalesLine.Reset();
                lcSalesLine.SetRange("Document No.", Header."No.");
                lcSalesLine.SetFilter("Piece FLX", '>0');
                if lcSalesLine.FindFirst() then
                    repeat
                        if Item.Get(lcSalesLine."No.") then //begin
                            if (Item."Tariff No." <> '') then begin
                                if StrPos(CustomTariffNumber, Item."Tariff No.") = 0 then begin
                                    TempSalesLineGTIP.Reset();
                                    TempSalesLineGTIP := lcSalesLine;
                                    TempSalesLineGTIP.Quantity := Round(lcSalesLine.Quantity * lcSalesLine."Qty. per Unit of Measure", 0.01);
                                    TempSalesLineGTIP."Net Weight" := GetWeight(lcSalesLine);
                                    TempSalesLineGTIP."Description 2" := Item."Tariff No.";
                                    TempSalesLineGTIP.Insert(false);
                                    if CustomTariffNumber <> '' then
                                        CustomTariffNumber += ', ';
                                    CustomTariffNumber += Item."Tariff No.";
                                end else begin
                                    TempSalesLineGTIP.Reset();
                                    //TempSalesLineGTIP.SetRange("Tariff No", lcSalesLine."Tariff No");
                                    TempSalesLineGTIP.SetRange("Description 2", Item."Tariff No.");
                                    if TempSalesLineGTIP.FindFirst() then begin
                                        TempSalesLineGTIP.Quantity += Round(lcSalesLine.Quantity * lcSalesLine."Qty. per Unit of Measure", 0.01);
                                        TempSalesLineGTIP."Net Weight" += GetWeight(lcSalesLine);
                                        TempSalesLineGTIP."Line Amount" += lcSalesLine."Line Amount";
                                        TempSalesLineGTIP.Modify(false);
                                    end;
                                end;
                                TempSalesLineGTIP."Quantity (Base)" := Round(TempSalesLineGTIP.Quantity / 0.305, 0.01);
                                TempSalesLineGTIP.Modify(false);
                            end;
                    //end;
                    until lcSalesLine.Next() = 0;
                CountryOfOrigin := 'TR';

                EntryNo := 0;
                LineNumberNo := 0;
                PackageDesc := '';
                BulkPieces := 0;
                PalletQty := 0;
                TotalTareWeight := 0;
                TotalGrossWeight := 0;
                TotalNetWeight := 0;
                TotalVolume := 0;
                pallettotalmetre := 0;
                pallettotalfeet := 0;
                farkmiktar_th := 0;
                paletcode := '';
                CombinedShipmentHeader.Reset();
                CombinedShipmentHeader.SetRange("Posted Sls. Invoice No.", Header."No.");
                if CombinedShipmentHeader.FindLast() then begin
                    ContainerNumber := CombinedShipmentHeader."Container No.";
                    CombinedShipmentHeader.CalcFields("Calcualted Shipment Weight");
                    if CombinedShipmentHeader."Scaled Shipment Weight" > 0 then
                        farkmiktar_th := CombinedShipmentHeader."Scaled Shipment Weight" - CombinedShipmentHeader."Calcualted Shipment Weight";

                    CombineShipPackDetail.Reset();
                    CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeader."No.");
                    if CombineShipPackDetail.FindFirst() then
                        CombineShipPackDetail.DeleteAll(true);
                    CombineShipLineDetail.Reset();
                    CombineShipLineDetail.SetCurrentKey("Parent Package No.");
                    CombineShipLineDetail.SetRange("Document No.", CombinedShipmentHeader."No.");
                    if CombineShipLineDetail.FindFirst() then
                        repeat
                            CombineShipLineDetail.CalcFields("Parent Package Type");
                            if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) or
                               (CombineShipLineDetail."Parent Package No." = '') then begin
                                TareDesc := '';
                                TareWeight := 0;
                                BulkPieces += 1;
                            end else begin
                                CombineShipPackDetail.Reset();
                                CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeader."No.");
                                CombineShipPackDetail.SetRange("Transfer-from Package No.", CombineShipLineDetail."Parent Package No.");
                                if not CombineShipPackDetail.FindFirst() then begin
                                    PalletQty += 1;
                                    PackageTransferOut.Reset();
                                    PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                                    if PackageTransferOut.FindFirst() then
                                        if PackageTransferOut."Palette Item No. FLX" <> '' then
                                            if Item.Get(PackageTransferOut."Palette Item No. FLX") then begin
                                                TareDesc := Item.Description;
                                                TareWeight := Item."Net Weight";
                                                TotalTareWeight += Item."Net Weight";
                                                TotalGrossWeight += Item."Net Weight";
                                                paletcode := Item."No.";
                                            end;
                                end;
                            end;

                            /*if CombineShipLineDetail."Item No." <> '' then
                                if Item.Get(CombineShipLineDetail."Item No.") then begin
                                    TareDesc := Item.Description;
                                    TareWeight := Item."Net Weight";
                                    TotalTareWeight += Item."Net Weight";
                                    TotalGrossWeight += Item."Net Weight";
                                end;
                            */
                            Package.Reset();
                            Package.SetRange(Package."Package No.", CombineShipLineDetail."Package No.");
                            Package.SetRange(Package."Combined Shipment No. FLX", CombinedShipmentHeader."No.");
                            Package.FindFirst();
                            //FLEX-230 BEGIN
                            OrderNo := Package."Sales Order No. FLX";
                            CustOrderNo := Package."Your Reference FLX";
                            if CombineShipLineDetail."Parent Package No." <> '' then begin
                                PackageTransferOut.Reset();
                                PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                                if PackageTransferOut.FindFirst() then begin
                                    if PackageTransferOut."Sales Order No. FLX" <> '' then
                                        OrderNo := PackageTransferOut."Sales Order No. FLX";
                                    if PackageTransferOut."Your Reference FLX" <> '' then
                                        CustOrderNo := PackageTransferOut."Your Reference FLX";
                                end;
                            end;
                            //FLEX-230 END
                            EntryNo += 1;
                            CombineShipPackDetail.Init();
                            CombineShipPackDetail."Combined Shipment No." := CombinedShipmentHeader."No.";
                            CombineShipPackDetail."Package Type" := CombineShipLineDetail."Parent Package Type";
                            CombineShipPackDetail."Package No." := CombineShipLineDetail."Package No.";
                            CombineShipPackDetail."Transfer-from Package No." := CombineShipLineDetail."Parent Package No.";
                            CombineShipPackDetail."Source Document No." := OrderNo;//FLEX-230 added line
                            CombineShipPackDetail."Your Reference" := CustOrderNo; //FLEX-230 added line
                            CombineShipPackDetail."Entry No" := EntryNo;
                            CombineShipPackDetail.Type := CombineShipPackDetail.Type::Item;
                            CombineShipPackDetail."No." := CombineShipLineDetail."Item No.";
                            CombineShipPackDetail.Description := CombineShipLineDetail."Item Description";
                            CombineShipPackDetail.Quantity := CombineShipLineDetail.Quantity;
                            CombineShipPackDetail."Coil Lenght" := CombineShipLineDetail.Quantity;
                            CombineShipPackDetail."Hose Length" := CombineShipLineDetail.Quantity;
                            //CombineShipPackDetail."Serial No." := CombineShipLineDetail."Serial No.";
                            CombineShipPackDetail."Lot No." := CombineShipLineDetail."Lot No.";
                            //LotNoInf.Reset();
                            //LotNoInf.SetRange("Item No.", CombineShipLineDetail."Item No.");
                            //LotNoInf.SetRange("Lot No.", CombineShipLineDetail."Lot No.");
                            //if LotNoInf.FindFirst() then
                            //CombineShipPackDetail."Coil Weight" := ROUND(LotNoInf."Weight per Qty." * CombineShipPackDetail.Quantity, 0.01);
                            CombineShipPackDetail."Coil Weight" := CombineShipLineDetail."Package Weight (KG)";
                            //CombineShipPackDetail."Expiration Date" := PackageContentTreeView."Expiration Date";
                            //CombineShipPackDetail."Pallet Code" := Package."Package Item No.";
                            CombineShipPackDetail.Piece := 1;
                            if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or
                               (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) or
                               (CombineShipLineDetail."Parent Package No." = '') then
                                CombineShipPackDetail."Pallet Line Number" := 'BULK'
                            else begin
                                CombineShipPackDetail."Pallet Line Number" := Format(PalletQty);
                                if StrLen(CombineShipPackDetail."Pallet Line Number") = 1 then
                                    CombineShipPackDetail."Pallet Line Number" := CopyStr('0' + CombineShipPackDetail."Pallet Line Number", 1, 100);
                                CombineShipPackDetail."Pallet Line Number" += CopyStr('-' + CombineShipLineDetail."Parent Package No.", 1, 100);
                                CombineShipPackDetail."Pallet Description" := TareDesc;
                                CombineShipPackDetail."Tare Weight" := TareWeight;
                                CombineShipPackDetail."Pallet Code" := paletcode;//CombineShipLineDetail."Item No.";
                            end;
                            CombineShipPackDetail.Insert(true);
                            pallettotalmetre += CombineShipPackDetail.Quantity;
                            pallettotalfeet += Round(CombineShipPackDetail.Quantity / 0.305, 0.01);
                            TotalGrossWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                            TotalNetWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                        //if Item.Get(CombineShipPackDetail."No.") then
                        //    TotalVolume += Round(Item."Unit Volume" * CombineShipPackDetail.Piece, 0.01);
                        until CombineShipLineDetail.Next() = 0;

                    //CalcTotalNetKG := TotalNetWeight;
                    if farkmiktar_th <> 0 then begin
                        TotalNetWeight := CombinedShipmentHeader."Scaled Shipment Weight";
                        TotalGrossWeight := CombinedShipmentHeader."Scaled Shipment Weight" + TotalTareWeight;
                    end;

                    TotalPackage := Format(PalletQty + BulkPieces) + ' PACKAGES';
                    if PalletQty > 0 then
                        PackageDesc := '( ' + Format(PalletQty) + ' PALLETS';
                    if BulkPieces > 0 then begin
                        if PackageDesc = '' then
                            PackageDesc += '( '
                        else
                            PackageDesc += ' / ';
                        PackageDesc += Format(BulkPieces) + ' BULK )';
                    end else
                        PackageDesc += ' )';

                    GrandPallettotaltxt := 'MT ' + Format(pallettotalmetre) + ' (FT ' + Format(pallettotalfeet) + ')';
                end;
            end;

            trigger OnPreDataItem()
            begin
                FirstLineHasBeenOutput := false;
            end;
        }
    }

    requestpage
    {
        SaveValues = true;

        layout
        {
            area(Content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(LogInteractionx; LogInteraction)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Log Interaction';
                        Enabled = LogInteractionEnable;
                        ToolTip = 'Specifies that interactions with the contact are logged.';
                    }
                    field(DisplayAsmInformation; DisplayAssemblyInformation)
                    {
                        ApplicationArea = Assembly;
                        Caption = 'Show Assembly Components';
                        ToolTip = 'Specifies if you want the report to include information about components that were used in linked assembly orders that supplied the item(s) being sold. (Only possible for RDLC report layout).';
                    }
                    field(DisplayShipmentInformationx; DisplayShipmentInformation)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Show Shipments';
                        ToolTip = 'Specifies that shipments are shown on the document.';
                    }
                    field(DisplayAdditionalFeeNotex; DisplayAdditionalFeeNote)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Show Additional Fee Note';
                        ToolTip = 'Specifies if you want notes about additional fees to be shown on the document.';
                    }
                }
            }
        }

        actions
        {
        }

        trigger OnInit()
        begin
            LogInteractionEnable := true;
        end;

        trigger OnOpenPage()
        begin
            InitLogInteraction();
            LogInteractionEnable := LogInteraction;
        end;
    }

    labels
    {
    }

    trigger OnInitReport()
    var
        SalesInvoiceHeader: Record "Sales Invoice Header";
        IsHandled: Boolean;
    begin
        GLSetup.Get();
        CompanyInfo.SetAutoCalcFields(Picture);
        CompanyInfo.Get();
        CompanyInfo.CalcFields(Picture);
        SalesSetup.Get();
        CompanyInfo.VerifyAndSetPaymentInfo();

        if SalesInvoiceHeader.GetLegalStatement() <> '' then
            LegalStatementLbl := SalesInvoiceHeader.GetLegalStatement();

        IsHandled := false;
        OnInitReportForGlobalVariable(IsHandled, LegalOfficeTxt, LegalOfficeLbl, CustomGiroTxt, CustomGiroLbl, LegalStatementLbl);
#if not CLEAN23
        /*if not IsHandled then begin
            LegalOfficeTxt := CompanyInfo.GetLegalOffice();
            LegalOfficeLbl := CompanyInfo.GetLegalOfficeLbl();
            CustomGiroTxt := CompanyInfo.GetCustomGiro();
            CustomGiroLbl := CompanyInfo.GetCustomGiroLbl();
        end;
        */
#endif
    end;

    trigger OnPostReport()
    begin
        if LogInteraction and not IsReportInPreviewMode() then
            if Header.FindSet() then
                repeat
                    if Header."Bill-to Contact No." <> '' then
                        SegManagement.LogDocument(
                          4, Header."No.", 0, 0, Database::Contact, Header."Bill-to Contact No.", Header."Salesperson Code",
                          Header."Campaign No.", Header."Posting Description", '')
                    else
                        SegManagement.LogDocument(
                          4, Header."No.", 0, 0, Database::Customer, Header."Bill-to Customer No.", Header."Salesperson Code",
                          Header."Campaign No.", Header."Posting Description", '');
                until Header.Next() = 0;
    end;

    trigger OnPreReport()
    begin
        if Header.GetFilters() = '' then
            Error(NoFilterSetErr);

        if not CurrReport.UseRequestPage() then
            InitLogInteraction();

        CompanyLogoPosition := SalesSetup."Logo Position on Documents";
    end;

    var
        DummyCompanyInfo: Record "Company Information";
        BillToContact: Record Contact;
        SellToContact: Record Contact;
        Cust: Record Customer;
        GLSetup: Record "General Ledger Setup";
        RespCenter: Record "Responsibility Center";
        VATClause: Record "VAT Clause";
        AutoFormat: Codeunit "Auto Format";
        FormatAddr: Codeunit "Format Address";
        FormatDocument: Codeunit "Format Document";
        Languagex: Codeunit Language;
        SegManagement: Codeunit SegManagement;
        LogInteractionEnable: Boolean;
        MoreLines: Boolean;
        ShowWorkDescription: Boolean;
        JobNo: Code[20];
        JobTaskNo: Code[20];
        CalculatedExchRate: Decimal;
        PrevLineAmount: Decimal;
        TransHeaderAmount: Decimal;
        WorkDescriptionInstream: InStream;
        CompanyLogoPosition: Integer;
        AlreadyPaidLbl: Label 'The invoice has been paid.';
        BilledToLbl: Label 'Billed to';
        BillToContactEmailLbl: Label 'Bill-to Contact E-Mail';
        BillToContactMobilePhoneNoLbl: Label 'Bill-to Contact Mobile Phone No.';
        BillToContactPhoneNoLbl: Label 'Bill-to Contact Phone No.';
        BodyLbl: Label 'Thank you for your business. Your invoice is attached to this message.';
        ChecksPayableLbl: Label 'Please make checks payable to %1', Comment = '%1 = company name';
        ClosingLbl: Label 'Sincerely';
        CompanyInfoBankAccNoLbl: Label 'Account No.';
        CompanyInfoBankNameLbl: Label 'Bank';
        CompanyInfoGiroNoLbl: Label 'Giro No.';
        CompanyInfoPhoneNoLbl: Label 'Phone No.';
        CopyLbl: Label 'Copy';
        EMailLbl: Label 'Email';
        ExchangeRateTxt: Label 'Exchange rate: %1/%2', Comment = '%1 and %2 are both amounts.';
        FromLbl: Label 'From';
        GreetingLbl: Label 'Hello';
        HomePageLbl: Label 'Home Page';
        InvDiscBaseAmtLbl: Label 'Invoice Discount Base Amount';
        InvDiscountAmtLbl: Label 'Invoice Discount';
        InvNoLbl: Label 'Invoice No.';
        JobNo2Lbl: Label 'Job No.';
        JobTaskDescLbl: Label 'Job Task Description';
        JobTaskNo2Lbl: Label 'Job Task No.';
        //LCYTxt: Label ' (LCY)'; //mark removal
        LineAmtAfterInvDiscLbl: Label 'Payment Discount on VAT';
        LocalCurrencyLbl: Label 'Local Currency';
        NoFilterSetErr: Label 'You must specify one or more filters to avoid accidently printing all documents.';
        PageLbl: Label 'Page';
        PartiallyPaidLbl: Label 'The invoice has been partially paid. The remaining amount is %1', Comment = '%1=an amount';
        PaymentMethodDescLbl: Label 'Payment Method';
        PmtDiscTxt: Label 'If we receive the payment before %1, you are eligible for a %2% payment discount.', Comment = '%1 Discount Due Date %2 = value of Payment Discount % ';
        PostedShipmentDateLbl: Label 'Shipment Date';
        PriceLbl: Label 'Price';
        PricePerLbl: Label 'Price per';
        QtyLbl: Label 'Qty', Comment = 'Short form of Quantity';
        QuestionsLbl: Label 'Questions?';
        SalesInvLineDiscLbl: Label 'Discount %';
        SalesInvoiceLbl: Label 'Invoice';
        SalespersonLbl: Label 'Salesperson';
        SellToContactEmailLbl: Label 'Sell-to Contact E-Mail';
        SellToContactMobilePhoneNoLbl: Label 'Sell-to Contact Mobile Phone No.';
        SellToContactPhoneNoLbl: Label 'Sell-to Contact Phone No.';
        ShipmentLbl: Label 'Shipment';
        SubtotalLbl: Label 'Subtotal';
        ThanksLbl: Label 'Thank You!';
        TotalLbl: Label 'Total';
        UnitLbl: Label 'Unit';
        //VATAmountLCYLbl: Label 'VAT Amount (LCY)'; //mark removal
        VATAmtLbl: Label 'VAT Amount';
        VATAmtSpecificationLbl: Label 'VAT Amount Specification';
        VATBaseLbl: Label 'VAT Base';
        //VATBaseLCYLbl: Label 'VAT Base (LCY)'; //mark removal
        VATClausesLbl: Label 'VAT Clause';
        VATIdentifierLbl: Label 'VAT Identifier';
        VATPercentageLbl: Label 'VAT %';
        YourSalesInvoiceLbl: Label 'Your Invoice';
        ChecksPayableText: Text;
        CustomGiroLbl, CustomGiroTxt, LegalOfficeLbl, LegalOfficeTxt, LegalStatementLbl : Text;
        ExchangeRateText: Text;
        SellToPhoneNo: Text[100];
        SellToEmail: Text[100];
        ShipToPhoneNo: Text[100];
        ShipToEmail: Text[100];
        DateText: Text[30];
        JobNoLbl: Text;
        JobTaskNoLbl: Text;
        PaymentInstructionsTxt: Text;
        RemainingAmountTxt: Text;
        TotalAmountExclInclVATTextValue: Text;
        //VATClausesText: Text; //mark removal
        //VATClauseText: Text; //mark removal
        WorkDescriptionLine: Text;
        SalesPersonText: Text[50];
        JobTaskDescription: Text[100];

    protected var
        CompanyBankAccount: Record "Bank Account";
        CombinedShipmentHeader: Record "Combined Shipment Header FLX";
        CombineShipLineDetail: Record "CombinedShipmentLineDtl FLX";
        CombineShipPackDetail: Record "Combined Ship Package Det. FLX";
        CompanyInfo: Record "Company Information";
        //Item: Record Item;
        TempLineFeeNoteOnReportHist: Record "Line Fee Note on Report Hist." temporary;
        //LotNoInf: Record "Lot No. Information";
        Package: Record "Package No. Information";
        PackageTransferOut: Record "Package No. Information";
        PaymentMethod: Record "Payment Method";
        PaymentTerms: Record "Payment Terms";
        SalesSetup: Record "Sales & Receivables Setup";
        //SalesHeader: Record "Sales Invoice Header";
        lcSalesLine: Record "Sales Invoice Line";
        //TempSalesLine: Record "Sales Invoice Line" temporary;
        TempSalesLineGTIP: Record "Sales Invoice Line" temporary;
        SalespersonPurchaser: Record "Salesperson/Purchaser";
        SalesShipmentHD: Record "Sales Shipment Header";
        ShipmentMethod: Record "Shipment Method";
        DisplayAdditionalFeeNote: Boolean;
        DisplayAssemblyInformation: Boolean;
        DisplayShipmentInformation: Boolean;
        //ekle: Boolean;
        FirstLineHasBeenOutput: Boolean;
        LogInteraction: Boolean;
        plasticpallet: Boolean;
        //ShowNotShip: Boolean;
        ShowShippingAddr: Boolean;
        CurrencyCode: Code[10];
        //PalletLineNo: Code[20];
        BulkPieces: Decimal;
        //CalcTotalNetKG: Decimal;
        farkdahiltoplam: Decimal;
        farkmiktar_th: Decimal;
        farkoran_th: Decimal;
        //farksonuc_th: Decimal;
        //GrandPallettotalfeet: Decimal;
        //GrandPallettotalmetre: Decimal;
        //GrossWeight: Decimal;
        hesaplananfark_th: Decimal;
        //NetWeight: Decimal;
        //palletgrossweight: Decimal;
        //palletnetweight: Decimal;
        PalletQty: Decimal;
        //PalletTare: Decimal;
        pallettotalfeet: Decimal;
        pallettotalmetre: Decimal;
        RemainingAmount: Decimal;
        TareWeight: Decimal;
        toplamKG_th: Decimal;
        toplammiktar: Decimal;
        TotalAmount: Decimal;
        TotalAmountExclInclVATValue: Decimal;
        TotalAmountInclVAT: Decimal;
        TotalAmountVAT: Decimal;
        TotalGrossWeight: Decimal;
        TotalInvDiscAmount: Decimal;
        TotalNetWeight: Decimal;
        TotalPaymentDiscOnVAT: Decimal;
        TotalSubTotal: Decimal;
        TotalTareWeight: Decimal;
        TotalVATAmountLCY: Decimal;
        TotalVATAmountOnVATAmtLine: Decimal;
        TotalVATBaseLCY: Decimal;
        TotalVATBaseOnVATAmtLine: Decimal;
        TotalVolume: Decimal;
        //VATAmountLCY: Decimal; //mark removal
        //VATBaseLCY: Decimal; //mark removal
        EntryNo: Integer;

        LineNumberNo: Integer;
        ContainerNumberLbl: Label 'Container Number :';
        CountryOfOriginLbl: Label 'Country Of Origin :';
        CustPoLbl: Label 'Cust. PO.';
        Description_Line_Lbl: Label 'Article Description';
        GrossWeightLbl: Label 'Gross Weight :';
        ItemNo_Line_Lbl: Label 'Article No';
        LineAmount_Line_Lbl: Label 'Net Amount';
        LineNoLbl: Label 'No';
        NetGoodsLbl: Label 'Net Goods';
        NetWeightLbl: Label 'Net Weight :';
        OurPOLbl: Label 'Our PO.';
        PackagingLbl: Label 'Packaging :';
        PaymentTermsDescLbl: Label 'Payment Terms';
        PlasticPalletsLbl: Label 'Goods loaded on plastic pallets';
        Quantity_Line_Lbl: Label 'Qty.';
        ShiptoAddrLbl: Label 'Ship-to Address';
        ShptMethodDescLbl: Label 'Shipment Method';
        TareWeightLbl: Label 'Tare Weight :';
        TotalAmountLbl: Label 'Total Amount';
        TotalChargesLbl: Label 'Total Charges';
        UnitOfMeasure_Lbl: Label 'U.M';
        UnitPrice_Lbl: Label 'Unit Price';
        //NAV17 var
        WoodenPalletsLbl: Label 'WOODEN PALLETS ACCORDING TO ISPM-15 STANDARD NORM / HEAT TREATED';
        FormattedLineAmount: Text;
        FormattedQuantity: Text;
        FormattedUnitPrice: Text;
        FormattedVATPct: Text;
        LineDiscountPctText: Text;
        PmtDiscText: Text;
        SupplierNumber: Text[100];
        CurrCode: Text[10];
        CurrSymbol: Text[10];
        ContainerNumber: Text[50];
        CustOrderNo: Text[50];
        OrderNo: Text[50];
        TotalExclVATText: Text[50];
        TotalInclVATText: Text[50];
        TotalPackage: Text[50];
        TotalText: Text[50];
        CompanyAddr: array[8] of Text[100];
        CustAddr: array[8] of Text[100];
        ShipToAddr: array[8] of Text[100];
        //PalletDesc: Text[50];
        TareDesc: Text[100];
        CountryOfOrigin: Text[250];
        CustomTariffNumber: Text[250];
        //numtext: Text[250];
        plasticwooddesc: Text[250];
        //feetvaluetxt: Text[1024];
        GrandPallettotaltxt: Text[1024];
        //metrevaluetxt: Text[1024];
        PackageDesc: Text[1024];
    //palletfeettxt: Text[1024];
    //palletmetretxt: Text[1024];
    //pallettotaltxt: Text[1024];

    local procedure InitLogInteraction()
    begin
        LogInteraction := SegManagement.FindInteractionTemplateCode(Enum::"Interaction Log Entry Document Type"::"Sales Inv.") <> '';
    end;

    local procedure InitializeShipmentLine()
    var
        SalesShipmentBuffer2: Record "Sales Shipment Buffer";
        SalesShipmentHeader: Record "Sales Shipment Header";
    begin
        if Line.Type = Line.Type::" " then
            exit;

        if Line."Shipment No." <> '' then
            if SalesShipmentHeader.Get(Line."Shipment No.") then
                exit;

        ShipmentLine.GetLinesForSalesInvoiceLine(Line, Header);

        ShipmentLine.Reset();
        ShipmentLine.SetRange("Line No.", Line."Line No.");
        if ShipmentLine.FindFirst() then begin
            SalesShipmentBuffer2 := ShipmentLine;
            if not DisplayShipmentInformation then
                if ShipmentLine.Next() = 0 then begin
                    ShipmentLine.Get(SalesShipmentBuffer2."Document No.", SalesShipmentBuffer2."Line No.", SalesShipmentBuffer2."Entry No.");
                    ShipmentLine.Delete(false);
                    exit;
                end;
            ShipmentLine.CalcSums(Quantity);
            if ShipmentLine.Quantity <> Line.Quantity then begin
                ShipmentLine.DeleteAll(false);
                exit;
            end;
        end;
    end;

    local procedure DocumentCaption(): Text
    var
        DocCaption: Text[250];
    begin
        OnBeforeGetDocumentCaption(Header, DocCaption);
        if DocCaption <> '' then
            exit(DocCaption);
        exit(SalesInvoiceLbl);
    end;

    procedure InitializeRequest(NewLogInteraction: Boolean; DisplayAsmInfo: Boolean)
    begin
        LogInteraction := NewLogInteraction;
        DisplayAssemblyInformation := DisplayAsmInfo;
    end;

    protected procedure IsReportInPreviewMode(): Boolean
    var
        MailManagement: Codeunit "Mail Management";
    begin
        exit(CurrReport.Preview() or MailManagement.IsHandlingGetEmailBody());
    end;

    local procedure GetUOMText(UOMCode: Code[10]): Text[50]
    var
        UnitOfMeasure: Record "Unit of Measure";
        UOMDescription: Text[50];
    begin
        if not UnitOfMeasure.Get(UOMCode) then
            exit(UOMCode);

        UOMDescription := UnitOfMeasure.Description;
        OnAfterGetUOMText(UOMCode, UOMDescription);
        exit(UOMDescription);
    end;

    local procedure CreateReportTotalLines()
    begin
        ReportTotalsLine.DeleteAll(false);
        if (TotalInvDiscAmount <> 0) or (TotalAmountVAT <> 0) then
            ReportTotalsLine.Add(SubtotalLbl, TotalSubTotal, true, false, false, Header."Currency Code");
        if TotalInvDiscAmount <> 0 then begin
            ReportTotalsLine.Add(InvDiscountAmtLbl, TotalInvDiscAmount, false, false, false, Header."Currency Code");
            if TotalAmountVAT <> 0 then
                if Header."Prices Including VAT" then
                    ReportTotalsLine.Add(TotalInclVATText, TotalAmountInclVAT, true, false, false, Header."Currency Code")
                else
                    ReportTotalsLine.Add(TotalExclVATText, TotalAmount, true, false, false, Header."Currency Code");
        end;
        //if TotalAmountVAT <> 0 then begin //mark removal
        //ReportTotalsLine.Add(VATAmountLine.VATAmountText(), TotalAmountVAT, false, true, false, Header."Currency Code");
        //if TotalVATAmountLCY <> TotalAmountVAT then
        //    ReportTotalsLine.Add(VATAmountLine.VATAmountText() + LCYTxt, TotalVATAmountLCY, false, true, false);
        //end;
    end;

    local procedure GetLineFeeNoteOnReportHist(SalesInvoiceHeaderNo: Code[20])
    var
        CustLedgerEntry: Record "Cust. Ledger Entry";
        Customer: Record Customer;
        LineFeeNoteOnReportHist: Record "Line Fee Note on Report Hist.";
    begin
        TempLineFeeNoteOnReportHist.DeleteAll(false);
        CustLedgerEntry.SetRange("Document Type", CustLedgerEntry."Document Type"::Invoice);
        CustLedgerEntry.SetRange("Document No.", SalesInvoiceHeaderNo);
        if not CustLedgerEntry.FindFirst() then
            exit;

        if not Customer.Get(CustLedgerEntry."Customer No.") then
            exit;

        LineFeeNoteOnReportHist.SetRange("Cust. Ledger Entry No", CustLedgerEntry."Entry No.");
        LineFeeNoteOnReportHist.SetRange("Language Code", Customer."Language Code");
        if LineFeeNoteOnReportHist.FindSet() then
            repeat
                TempLineFeeNoteOnReportHist.Init();
                TempLineFeeNoteOnReportHist.Copy(LineFeeNoteOnReportHist);
                TempLineFeeNoteOnReportHist.Insert(false);
            until LineFeeNoteOnReportHist.Next() = 0
        else begin
            LineFeeNoteOnReportHist.SetRange("Language Code", Languagex.GetUserLanguageCode());
            if LineFeeNoteOnReportHist.FindSet() then
                repeat
                    TempLineFeeNoteOnReportHist.Init();
                    TempLineFeeNoteOnReportHist.Copy(LineFeeNoteOnReportHist);
                    TempLineFeeNoteOnReportHist.Insert(false);
                until LineFeeNoteOnReportHist.Next() = 0;
        end;
    end;

    local procedure FillLeftHeader()
    begin
        LeftHeader.DeleteAll(false);

        FillNameValueTable(LeftHeader, Header.FieldCaption("External Document No."), Header."External Document No.");
        FillNameValueTable(LeftHeader, Header.FieldCaption("Bill-to Customer No."), Header."Bill-to Customer No.");
        FillNameValueTable(LeftHeader, Header.GetCustomerVATRegistrationNumberLbl(), Header.GetCustomerVATRegistrationNumber());
        FillNameValueTable(LeftHeader, Header.GetCustomerGlobalLocationNumberLbl(), Header.GetCustomerGlobalLocationNumber());
        FillNameValueTable(LeftHeader, InvNoLbl, Header."No.");
        FillNameValueTable(LeftHeader, Header.FieldCaption("Order No."), Header."Order No.");
        FillNameValueTable(LeftHeader, Header.FieldCaption("Document Date"), Format(Header."Document Date", 0, 4));
        FillNameValueTable(LeftHeader, Header.FieldCaption("Due Date"), Format(Header."Due Date", 0, 4));
        FillNameValueTable(LeftHeader, PaymentTermsDescLbl, PaymentTerms.Description);
        FillNameValueTable(LeftHeader, PaymentMethodDescLbl, PaymentMethod.Description);
        FillNameValueTable(LeftHeader, Cust.GetLegalEntityTypeLbl(), Cust.GetLegalEntityType());
        FillNameValueTable(LeftHeader, ShptMethodDescLbl, ShipmentMethod.Description);

        OnAfterFillLeftHeader(LeftHeader, Header);
    end;

    local procedure FillRightHeader()
    var
        IsHandled: Boolean;
    begin
        IsHandled := false;
        OnBeforeFillRightHeader(Header, SalespersonPurchaser, SalesPersonText, RightHeader, IsHandled);
        if not IsHandled then begin
            RightHeader.DeleteAll(false);

            FillNameValueTable(RightHeader, EMailLbl, CompanyInfo."E-Mail");
#pragma warning disable AL0432
            FillNameValueTable(RightHeader, HomePageLbl, CompanyInfo."Home Page");
#pragma warning restore AL0432
            FillNameValueTable(RightHeader, CompanyInfoPhoneNoLbl, CompanyInfo."Phone No.");
            FillNameValueTable(RightHeader, CompanyInfo.GetRegistrationNumberLbl(), CompanyInfo.GetRegistrationNumber());
            FillNameValueTable(RightHeader, CompanyInfo.GetVATRegistrationNumberLbl(), CompanyInfo.GetVATRegistrationNumber());
            FillNameValueTable(RightHeader, CompanyInfoBankNameLbl, CompanyBankAccount.Name);
            FillNameValueTable(RightHeader, CompanyInfoGiroNoLbl, CompanyInfo."Giro No.");
            FillNameValueTable(RightHeader, CompanyBankAccount.FieldCaption(IBAN), CompanyBankAccount.IBAN);
            FillNameValueTable(RightHeader, CompanyBankAccount.FieldCaption("SWIFT Code"), CompanyBankAccount."SWIFT Code");
            FillNameValueTable(RightHeader, Header.GetPaymentReferenceLbl(), Header.GetPaymentReference());

            OnAfterFillRightHeader(RightHeader, Header);
        end;
    end;

    local procedure FillNameValueTable(var NameValueBuffer: Record "Name/Value Buffer"; Name: Text; Value: Text)
    var
        KeyIndex: Integer;
    begin
        if Value <> '' then begin
            Clear(NameValueBuffer);
            if NameValueBuffer.FindLast() then
                KeyIndex := NameValueBuffer.ID + 1;

            NameValueBuffer.Init();
            NameValueBuffer.ID := KeyIndex;
            NameValueBuffer.Name := CopyStr(Name, 1, MaxStrLen(NameValueBuffer.Name));
            NameValueBuffer.Value := CopyStr(Value, 1, MaxStrLen(NameValueBuffer.Value));
            NameValueBuffer.Insert(true);
        end;
    end;

    local procedure FormatAddressFields(var SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
        FormatAddr.GetCompanyAddr(SalesInvoiceHeader."Responsibility Center", RespCenter, CompanyInfo, CompanyAddr);
        FormatAddr.SalesInvBillTo(CustAddr, SalesInvoiceHeader);
        ShowShippingAddr := FormatAddr.SalesInvShipTo(ShipToAddr, CustAddr, SalesInvoiceHeader);
    end;

    local procedure FormatDocumentFields(SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
        //with SalesInvoiceHeader do begin
        FormatDocument.SetTotalLabels(SalesInvoiceHeader.GetCurrencySymbol(), TotalText, TotalInclVATText, TotalExclVATText);
        FormatDocument.SetSalesPerson(SalespersonPurchaser, SalesInvoiceHeader."Salesperson Code", SalesPersonText);
        FormatDocument.SetPaymentTerms(PaymentTerms, SalesInvoiceHeader."Payment Terms Code", SalesInvoiceHeader."Language Code");
        FormatDocument.SetPaymentMethod(PaymentMethod, SalesInvoiceHeader."Payment Method Code", SalesInvoiceHeader."Language Code");
        FormatDocument.SetShipmentMethod(ShipmentMethod, SalesInvoiceHeader."Shipment Method Code", SalesInvoiceHeader."Language Code");

        OnAfterFormatDocumentFields(SalesInvoiceHeader);
        //end;
    end;

    //local procedure GetJobTaskDescription(JobNo: Code[20]; JobTaskNo: Code[20]): Text[100]
    local procedure GetJobTaskDescription(): Text[100]
    var
        JobTask: Record "Job Task";
    begin
        JobTask.SetRange("Job No.", JobNo);
        JobTask.SetRange("Job Task No.", JobTaskNo);
        if JobTask.FindFirst() then
            exit(JobTask.Description);

        exit('');
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterLineOnPreDataItem(var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesInvoiceLine: Record "Sales Invoice Line")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterFillLeftHeader(var LeftHeader: Record "Name/Value Buffer"; SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterFillRightHeader(var RightHeader: Record "Name/Value Buffer"; SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeLineOnAfterGetRecord(var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesInvoiceLine: Record "Sales Invoice Line")
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeGetDocumentCaption(SalesInvoiceHeader: Record "Sales Invoice Header"; var DocCaption: Text[250])
    begin
    end;

    [IntegrationEvent(true, false)]
    local procedure OnAfterGetSalesHeader(SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
    end;

    /*local procedure ShowVATClause(VATClauseCode: Code[20]): Boolean
    begin //mark removal
        if VATClauseCode = '' then
            exit(false);

        exit(true);
    end;
    */

    /*local procedure InsertVATAmountLine(var VATAmountLine2: Record "VAT Amount Line"; SalesInvoiceLine: Record "Sales Invoice Line")
    var //mark removal
        IsHandled: Boolean;
    begin
        IsHandled := false;
        OnBeforeVATAmountLineInsertLine(VATAmountLine2, SalesInvoiceLine, IsHandled);
        if IsHandled then
            exit;

        VATAmountLine2.Init();
        VATAmountLine2."VAT Identifier" := SalesInvoiceLine."VAT Identifier";
        VATAmountLine2."VAT Calculation Type" := SalesInvoiceLine."VAT Calculation Type";
        VATAmountLine2."Tax Group Code" := SalesInvoiceLine."Tax Group Code";
        VATAmountLine2."VAT %" := SalesInvoiceLine."VAT %";
        VATAmountLine2."VAT Base" := SalesInvoiceLine.Amount;
        VATAmountLine2."Amount Including VAT" := SalesInvoiceLine."Amount Including VAT";
        VATAmountLine2."Line Amount" := SalesInvoiceLine."Line Amount";
        if SalesInvoiceLine."Allow Invoice Disc." then
            VATAmountLine2."Inv. Disc. Base Amount" := SalesInvoiceLine."Line Amount";
        VATAmountLine2."Invoice Discount Amount" := SalesInvoiceLine."Inv. Discount Amount";
        VATAmountLine2."VAT Clause Code" := SalesInvoiceLine."VAT Clause Code";
        VATAmountLine2.InsertLine();
    end;
*/
    local procedure FormatLineValues(CurrLine: Record "Sales Invoice Line")
    var
        IsHandled: Boolean;
    begin
        IsHandled := false;
        OnBeforeFormatLineValues(CurrLine, FormattedQuantity, FormattedUnitPrice, FormattedVATPct, FormattedLineAmount, IsHandled);
        if not IsHandled then
            FormatDocument.SetSalesInvoiceLine(CurrLine, FormattedQuantity, FormattedUnitPrice, FormattedVATPct, FormattedLineAmount);
    end;

    local procedure GetDateText(ComingDate: Date) result: Text[30]
    var
        month: Integer;
        monthtext: Text[3];
    begin
        result := Format(Date2DMY(ComingDate, 1)) + '-';
        month := Date2DMY(ComingDate, 2);
        case month of
            1:
                monthtext := 'Jan';
            2:
                monthtext := 'Feb';
            3:
                monthtext := 'Mar';
            4:
                monthtext := 'Apr';
            5:
                monthtext := 'May';
            6:
                monthtext := 'Jun';
            7:
                monthtext := 'Jul';
            8:
                monthtext := 'Aug';
            9:
                monthtext := 'Sep';
            10:
                monthtext := 'Oct';
            11:
                monthtext := 'Nov';
            12:
                monthtext := 'Dec';
        end;
        result += monthtext + '-' + Format(Date2DMY(ComingDate, 3));
    end;

    local procedure GetWeight(var wSalesLine: Record "Sales Invoice Line"): Decimal
    var
        Item: Record Item;
        HamProdBomLine: Record "Production BOM Line";
        ProductionBOMLine: Record "Production BOM Line";
        VersionMgt: Codeunit VersionManagement;
        ActiveVersionCode: Code[20];
        Gtip: Code[20];
        hamprodbomno: Code[20];
        prodbomno: Code[20];
        //firemiktar: Decimal;
        lctoplammiktar: Decimal;
        toplamkg: Decimal;
    begin

        toplamkg := 0;
        Gtip := '';
        prodbomno := '';
        if Item.Get(wSalesLine."No.") then begin
            Gtip := Item."Tariff No.";
            prodbomno := Item."Production BOM No.";
        end;
        if Gtip = '' then
            Gtip := wSalesLine."No.";
        ActiveVersionCode := VersionMgt.GetBOMVersion(prodbomno, WorkDate(), true);
        ProductionBOMLine.Reset();
        ProductionBOMLine.SetRange("Production BOM No.", prodbomno);
        ProductionBOMLine.SetRange("Version Code", ActiveVersionCode);
        ProductionBOMLine.SetRange("Unit of Measure Code", 'KG');
        if ProductionBOMLine.FindSet() then
            repeat
                hamprodbomno := '';
                if Item.Get(ProductionBOMLine."No.") then
                    hamprodbomno := Item."Production BOM No.";

                if hamprodbomno <> '' then begin
                    lctoplammiktar := Round(ProductionBOMLine."Quantity per" * wSalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                    //firemiktar := Round(lctoplammiktar * ProductionBOMLine."Scrap %" / 100, 0.01);
                    ActiveVersionCode := VersionMgt.GetBOMVersion(hamprodbomno, WorkDate(), true);
                    HamProdBomLine.Reset();
                    HamProdBomLine.SetRange("Production BOM No.", hamprodbomno);
                    HamProdBomLine.SetRange("Version Code", ActiveVersionCode);
                    HamProdBomLine.SetRange("Unit of Measure Code", 'KG');
                    if HamProdBomLine.FindSet() then
                        repeat
                            toplamkg += Round(HamProdBomLine."Quantity per" * lctoplammiktar, 0.01);
                        until HamProdBomLine.Next() = 0;
                end else //begin
                    toplamkg += Round(ProductionBOMLine."Quantity per" * wSalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                                                                                                              //end;
            until ProductionBOMLine.Next() = 0;
        toplamKG_th += toplamkg;
        exit(toplamkg);
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeFormatLineValues(SalesInvoiceLine: Record "Sales Invoice Line"; var FormattedQuantity: Text; var FormattedUnitPrice: Text; var FormattedVATPercentage: Text; var FormattedLineAmount: Text; var IsHandled: Boolean)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterFormatDocumentFields(var SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
    end;

    //[IntegrationEvent(false, false)] //mark removal
    //local procedure OnBeforeVATAmountLineInsertLine(var VATAmountLine: Record "VAT Amount Line"; SalesInvoiceLine: Record "Sales Invoice Line"; var IsHandled: Boolean)
    //begin
    //end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeFillRightHeader(var SalesInvoiceHeader: Record "Sales Invoice Header"; SalespersonPurchaser: Record "Salesperson/Purchaser"; var SalesPersonText: Text[50]; var RightHeader: Record "Name/Value Buffer"; var IsHandled: Boolean)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterGetUOMText(UOMCode: Code[10]; var UOMDescription: Text[50])
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnInitReportForGlobalVariable(var IsHandled: Boolean; var LegalOfficeTxt: Text; var LegalOfficeLbl: Text; var CustomGiroTxt: Text; var CustomGiroLbl: Text; var LegalStatementLbl: Text)
    begin
    end;

    [IntegrationEvent(true, false)]
    local procedure OnHeaderOnAfterGetRecordOnAfterUpdateNoPrinted(ReportInPreviewMode: Boolean; var SalesInvoiceHeader: Record "Sales Invoice Header")
    begin
    end;
}

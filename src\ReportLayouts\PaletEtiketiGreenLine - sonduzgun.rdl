﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-11-13T11:33:07.5197870Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>bc508610-c9c1-4ad1-a1b1-a57eaa153929</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
      <Fields>
        <Field Name="IDmmFLX_PalettePackageNoInformation">
          <DataField>IDmmFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PalettePackageNoInformationFormat">
          <DataField>IDmmFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ODmmFLX_PalettePackageNoInformation">
          <DataField>ODmmFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ODmmFLX_PalettePackageNoInformationFormat">
          <DataField>ODmmFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ProductionOrderNoFLX_PalettePackageNoInformation">
          <DataField>ProductionOrderNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PalettePackageNoInformation">
          <DataField>BPbarFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PalettePackageNoInformationFormat">
          <DataField>BPbarFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="WPbarFLX_PalettePackageNoInformation">
          <DataField>WPbarFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="WPbarFLX_PalettePackageNoInformationFormat">
          <DataField>WPbarFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SelltoCustomerNameFLX_PalettePackageNoInformation">
          <DataField>SelltoCustomerNameFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Description_PalettePackageNoInformation">
          <DataField>Description_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="HoseLenghtFLX_PalettePackageNoInformation">
          <DataField>HoseLenghtFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="HoseLenghtFLX_PalettePackageNoInformationFormat">
          <DataField>HoseLenghtFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="LabelLenghtFLX_PalettePackageNoInformation">
          <DataField>LabelLenghtFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="LabelLenghtFLX_PalettePackageNoInformationFormat">
          <DataField>LabelLenghtFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="PackageNo_PalettePackageNoInformation">
          <DataField>PackageNo_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="YourReferenceFLX_PalettePackageNoInformation">
          <DataField>YourReferenceFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ItemNo_PalettePackageNoInformation">
          <DataField>ItemNo_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PalettePackageNoInformation">
          <DataField>Inventory_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PalettePackageNoInformationFormat">
          <DataField>Inventory_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SalesOrderLineNoFLX_PalettePackageNoInformation">
          <DataField>SalesOrderLineNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="QrCode_PalettePackageNoInformation">
          <DataField>QrCode_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ProductionOrderLineNoFLX_PalettePackageNoInformation">
          <DataField>ProductionOrderLineNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Ship_to_Code_FLX_PalettePackageNoInformation">
          <DataField>Ship_to_Code_FLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PackageNoInformation">
          <DataField>IDmmFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PackageNoInformationFormat">
          <DataField>IDmmFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ODmmFLX_PackageNoInformation">
          <DataField>ODmmFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ODmmFLX_PackageNoInformationFormat">
          <DataField>ODmmFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ProductionOrderNoFLX">
          <DataField>ProductionOrderNoFLX</DataField>
        </Field>
        <Field Name="BPbarFLX_PackageNoInformation">
          <DataField>BPbarFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PackageNoInformationFormat">
          <DataField>BPbarFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="WPbarFLX_PackageNoInformation">
          <DataField>WPbarFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="WPbarFLX_PackageNoInformationFormat">
          <DataField>WPbarFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SelltoCustomerNameFLX">
          <DataField>SelltoCustomerNameFLX</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="HoseLenghtFLX">
          <DataField>HoseLenghtFLX</DataField>
        </Field>
        <Field Name="HoseLenghtFLXFormat">
          <DataField>HoseLenghtFLXFormat</DataField>
        </Field>
        <Field Name="LabelLenghtFLX">
          <DataField>LabelLenghtFLX</DataField>
        </Field>
        <Field Name="LabelLenghtFLXFormat">
          <DataField>LabelLenghtFLXFormat</DataField>
        </Field>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="YourReferenceFLX_PackageNoInformation">
          <DataField>YourReferenceFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ItemNo_PackageNoInformation">
          <DataField>ItemNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformation">
          <DataField>Inventory_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformationFormat">
          <DataField>Inventory_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SalesOrderLineNoFLX_PackageNoInformation">
          <DataField>SalesOrderLineNoFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ProductionOrderLineNoFLX_PackageNoInformation">
          <DataField>ProductionOrderLineNoFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="Ship_to_Code_FLX">
          <DataField>Ship_to_Code_FLX</DataField>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="Rectangle4">
            <ReportItems>
              <Tablix Name="Tablix2">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>4.90771cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.83325cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>8.03982cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>2.18071cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Description5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Description.Value</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Description</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>None</Style>
                                  <Width>2pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ItemNo_PalettePackageNoInformation4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>="P.O.# "+Fields!YourReferenceFLX_PackageNoInformation.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ItemNo_PalettePackageNoInformation</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Tablix Name="Tablix8">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>8.03982cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>2.18071cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="QrCode6">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!BarcodePO_PackageNoInformation.Value</Value>
                                                    <Style>
                                                      <FontFamily>IDAutomationHC39M</FontFamily>
                                                      <FontSize>14pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>QrCode</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="Details7">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!PackageNo_PalettePackageNoInformation.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>2.21777cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox17">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox16</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Style>Solid</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Tablix Name="Tablix6">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>9.87307cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>2.21777cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="QrCode4">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!BarcodeItemSearchDescCode39_PackageNoInformation.Value</Value>
                                                    <Style>
                                                      <FontFamily>IDAutomationHC39M</FontFamily>
                                                      <FontSize>14pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>QrCode</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="Details5">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!PackageNo_PalettePackageNoInformation.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                            <ColSpan>2</ColSpan>
                          </CellContents>
                        </TablixCell>
                        <TablixCell />
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>2.03404cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Description4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Description2.Value</Value>
                                      <Style>
                                        <FontSize>12pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Description</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>None</Style>
                                  <Width>2pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox11">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!BarcodeQtyLabel_PackageNoInformation.Value</Value>
                                      <Style>
                                        <FontSize>11pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox11</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>2pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Tablix Name="Tablix7">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>8.03982cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>2.03404cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="QrCode5">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!BarcodeMadeinturkey.Value</Value>
                                                    <Style>
                                                      <FontFamily>IDAutomationHC39M</FontFamily>
                                                      <FontSize>14pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>QrCode</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>2pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="Details6">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!PackageNo_PalettePackageNoInformation.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details2">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PackageNo.Value</GroupExpression>
                          <GroupExpression>=Fields!SalesOrderLineNoFLX_PackageNoInformation.Value</GroupExpression>
                          <GroupExpression>=Fields!SalesOrderNoFLX_SubPalet.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                        <TablixMember />
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>DataSet_Result</DataSetName>
                <Top>0.48683cm</Top>
                <Height>6.43252cm</Height>
                <Width>14.78078cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                    <Width>2pt</Width>
                  </Border>
                </Style>
              </Tablix>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>0.28259cm</Top>
            <Height>6.91935cm</Height>
            <Width>14.78078cm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </RightBorder>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>7.20194cm</Height>
        <Style />
      </Body>
      <Width>418.98274pt</Width>
      <Page>
        <PageHeader>
          <Height>2.29307cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Image Name="Image1">
              <Source>Embedded</Source>
              <Value>greenlinenew</Value>
              <Sizing>FitProportional</Sizing>
              <Left>0.09525cm</Left>
              <Height>2.15196cm</Height>
              <Width>14.68553cm</Width>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </Border>
                <TopBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </TopBorder>
                <BottomBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </BottomBorder>
                <LeftBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </LeftBorder>
                <RightBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>2pt</Width>
                </RightBorder>
              </Style>
            </Image>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>10cm</PageHeight>
        <PageWidth>15cm</PageWidth>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>0.1cm</LeftMargin>
        <TopMargin>0.1cm</TopMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public RowColor As Boolean = 0

Public Function SetRowColor(ByVal val As Boolean) As Boolean
    RowColor = val
    Return val
End Function

Public Function GetRowColor() As Boolean
    Return RowColor
End Function
</Code>
  <EmbeddedImages>
    <EmbeddedImage Name="RDMLogo">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAkGBxQSEhQUERQVEBUWFxQYGBUUFxcUFhcZHBQXFhgUGBcYHCggGBolHBcUITEhJisrLi4uGB8zODMsQygtLiv/2wBDAQoKCg4NDhsQEBosJh8kLCwtMCwuLCw0LCw3LC0sLSw3MDQsNCwvOC03LDcsLTcvLC4sLCwsNy0sNCwsLCwsLCz/wAARCAB8ASIDASIAAhEBAxEB/8QAHAABAAICAwEAAAAAAAAAAAAAAAYHBQgCAwQB/8QARxAAAQMCAQcFCg0EAQUAAAAAAQACAwQRBQYHEiExQVETYXGBkSI1U2Jzg6Gxs9EUFyMyNEJScpKTssHCJDOCovAVJUN00v/EABoBAQEBAQEBAQAAAAAAAAAAAAABBAUCAwb/xAAoEQEAAQMDAwMEAwAAAAAAAAAAAQIDEQQhURIxQQUikRNhgaFxsdH/2gAMAwEAAhEDEQA/ALxREQEREBERAREQEREBERAReLFMUipmGSd7Y2je47TwA2k8wUIq8tZai/wZpgh3SO1yv52jYxvObnoXum3VVGfD43b9FqPdKdV1fHCLyPDebeegbSo1XZZboWf5PP8AEe9RiCF8rwG6Uj3cSSTzklTXBMmGRWdLaR+231W+886YiO7HTfvX5xb2jl4KJtbVazIYWHeBo3+6BrPapBRYSyPWdKR325CXu6r7F77L6vMy127EU7zOZ5kCIij7iIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiLw4xi0VLGZJ3iNo47SeDRtJ5lYiZnEJMxEZl7VBMsM48VNeOnAqJthN/k2dJHzjzDtUIyxzhzVd44bwQ6xYHu3jxiNg8UdqjuA4Xy79epjbaXP4oXRtaOKY67vw51/W+LfyzNIyatk+E1jzJ9lp2dAGxrfWpTh1C+Z4ZGLnedzRf5x5l00NK6WRsMIGkRfxWN2abrbBwG86lZODYWynj0Gaztc8
7XHieHMNyz3rvVv8QzafT1X6uqrt/Zg+Esp2WbrcfnOO0+4cyyCIsrs00xTGIEREehERAREQEREBERAREQEREBERAREQEREBERAREQEREBEXy6D6l1hMocqaaiHy8g0rXEbe6kPQ3d0lVHlVnDqKu7I/6aI3Gi03e4eM79h6Vos6Wu727cs93U0W+/fhP8r84kNJeOC1RNrFge4YfGI2nxR6FTuM4xNVScpUPMjt3Bo+y0bAF4Ai7FjTUWo278uVe1Fd2d+3Duo6Z0rwxu0+jiVNqdhjMdLTN5SZ+oDcOMj+A3rAU8wpI9mlPIBq+w3dfn32VtZusljSxGabuqiaxcTtY3aGdO89m5ZtVc2zPbxHM/4mnsTdqx4ZrJnAm0kWiDpvd3Ukh2vdx5gNgG4LMIi5MzMzmXdppimMQIiKKIiICIiAoFnBy/NC8QQMEkxaHOc75rAb2Fh85xtfmFlPVr7nSP8A3Oo837JqDuOc7EL35RnRybbKa5u8vZ62f4POyO/JufyjLtPclosW6wb6W3Vs3qmVOszXfE+Ql/XGqLzREUBERAREQEREBERAREQEREGMZlBTl5jMrI5BtZIeTcOp1r9SyLXX1jWOI1rEZR5NwVzNCdusX0XtsHs6D+2xUrlPkhUYc7SuXxE6po7tHQ8A9yeu3OtVmzbu7dWJ4Zrt6u3v05hsEvLWYnDD/dlji++4N9BK1nfVyEWMjyOBc4j1roDRwWqPTeav0zT6hxT+17YtnMoobhjnVDuEY1fidYKA47nOq57titStP2DpP/GQPQAoQi029Hao8Z/lmuau5X5x/Dk95cSXEuJNySbkniSdpXFEWtlFyil0SCLEjZfjxXAld2HUbppWRMF3SOa0dJNuxSZ2eohO81GTnwic1Uw0mRO7nS16cu2/Po6j0kK6F4MBwplLBHCzYxtr8Ttc7rNysdlDlnSURLZZLv8ABs7p/WB83rsvz+ou/Vrz48O7YtfTowkKKqKzPJr+
RpCRuMkuietrWEeleZueOXfSRnolcPToFfB9lwIq6wfO3TyENqIn0xP1gRKwdJADh2KfUlUyVgfE5sjHaw5puD1oO9EUayiy4pKMlsj9OQf+OMaTug7m9ZQSVFU1VnjN/kqQW4vl1n/FrLDtK4QZ5H37ukaRxbMQR1Fhv2hBbi18zo986jzfs2q2smsvKSsIY1xikOyOWzSTwab2d1FVLnR751Hm/ZtQRVTrM13xPkJf1xqCqdZmu+J8hL+uNUXmiLBZQZW0tFqnlAfb+23un83cjZ1qDOoqrrc8bQfkaVzhxkkDD+FrXeteNueOW+ukjI4CVw9OgUFwIq4wrO7TvIFRDJT33tIlaOkgB3+qnmGYlFUMEkEjZWne03tzHgeYoPWi655msaXPIa0C5c42AHEkqEYznTo4SWxadU4fYsGfjd+wKCdoqhlzxyX7mkYBzzFx9DAu2mzyG/ylILbyybX1NLNfagtlFFMn84FHVkNa8wyHYyazSeYG5aeoqVoCIiAuE0YcC1wDmkWIIuCOBB2rmiCqMtM2Vry0A4l0B9cZ/iergquc0gkEEEaiDqIO8EbitpyFDsucho60GSO0VQBqdbU/xX/s7culptdMe2525c/UaOJ91HwohF6K+ikhkdHK0se02LT/AM1heddWJzvDlzGNpF8JX1cChAArKzM4NpzSVLhqiGgz77hrPU39SrdbC5B4eKXD4gdRLTK887hpG/QLDqWPXXOi1jnZs0dHXczwwGdDLV1KBTUxtM9t3vG2Np2AeMfQOkKlibkkm5Osk6yTxJ3levGMRdUzyTu2yOLugHYOoWHUvIuK7Ai7qOlfK9scTTI9xsGtFyf+cVNaXNTWubdxhjPBziT/AKgoIIrGzK4o9tTJT3Jjex0ltwc0tFxzkG3UsJiubuvgGlyXLNG+I6Z/Dt7F7sFppMNoZqx4MU045CBrhZ7QdbpbEajYGw5kGdzkZwHNc+lo3aNtUkzTrvvYzhbe7qCq
lEQEXrwrC5ql/JwRulfwbuHEnYB0qVDNdX2voxX4coL+qyCErvrKt8rtOVxe6zQXONyQAALnfqAXoxfB5qV+hURuidtF9jhxaRqK8KAp1ma74nyEv641BVOMzjrYgTwp5f1RoJrnNy2NIBT05+Xe27neCadhHjHXbht4Kk5HlxLnEucSSSTcknaSd5Xtx7ETU1E0zjfTe4jo2NH4QF4UBF2UtO+R7Y42l73GzWtFyTzBS2PNliBbpcmxvimQaXZxQQ5ZHAcbmo5RLA4tOq7fqvH2XDeF04phktNIY543RPGuzt44g7COcLyIJLllllNiDgHfJQi1ogbi/wBpx+sb7OCjSLnBC57mtY0vc42a1ouSeAA2oOCKYU+bPEHt0uTYzxXyAO7NyjmL4RNSycnURuida4vsI4tI1EIPCQrNzYZcuY9lJUu0mO7mKRx1sO5jidrTsHDV1VmgPDVz/ug2tRQLCs4UXIRcoe75OPS1/W0Rpem6KCeoiICIiCHZw8kRWxacYtURg6B+2NvJn9uB6VQ5HUtqFROdbCBT1pc0WbM3lNWzSvZ47df+S6mgvzn6c/hzddZjHXH5Qxy+AK5mZtopMPijNoqkN0+Utr0nayx3Fuwc1lUmJUElPK+KZug9hsR+4O8Hittm/RdmYjwx3bFVuIz5MLpeVmii8JJGz8Tw391sdjrdGknA1AQSgW3WjNlRWbyLSxKlB+2T+FjnD0gK98oPotR5Gb2blz/UavdTH2bvT6fbM/drCF9XwL6ue6C38yOGNEM1QR3bpOTaeDWta426S7/VWcoLma73+el/ip0oCprPfWk1MEO5kRf1veW+gR+lXKqKzx98fMxetyCDrsp4S97WN1ue5rWjiXEADtK61mcirf8AUKTS2cvF26Q0f9rKi/Mlcn46GnbFGBfUXv3vfbW4/sNwWYRFBjcfwWKshfDMLhwNjvY7c9p3ELW3FKF0E0kL/nRuLTz2O3r2raRa9Zzbf9SqLcWX
6eTbf0oIupjmrP8AVy/+rUetihymWapn9VMeFLP6dD3KiFs2DoC5LizYOgLkgtnMhhjdGeoIu7SETTwFg51um7exWoq7zI/Q5vLu9nGrEUEPzpYO2eglfYacDTK128But46C2/YFQS2YyqH9FVeQm9m5azBB9VtZk8GboS1TgC/SMbCfqgAFxHOdIDqVSq8szXe/z0n8VRO1Ds6+Gtlw6VxHdQlsjTw7oB3+pPYFMVgMvu91X5GT1KDXFERUEREG1qIigIiICiOWeCCpqcPJFwyZ+l93QEljzExtHWpcuqWEOc0/ZJI62kfuvdFc0TmPu8V0xVGJdgUUy/yRbXxXZZs8YOg77Q28m48DuO49algRSiuaKoqp7rXRFcdMtfs3bSzFKdrwWuD5GkHUQeTeCD1q8cofotR5Gb2blDMucDENXTYjGLBs0QnAH1S4N5XsJB6lMsoPotR5Gb2blp1dyLnTXHDPpbc2+qieWsQX1fAvqytS88zXe/z0v8VOlBczXe/z0v8AFTpQFTufCgIngn3PjMZ5ixxcOsh5/CriUey7wH4bSSRttyg7uM+O3YOa+sdaDXNc4JixzXtNnMc1zTwLSHA9oC4yMLSWuBaQSCDqIINiDz3XxUbI5I5Rx10DZGEBwsJGb2OtrFuB3FZxatUFdJA8SQvdE8bHMNj0HiOY6lJG5x8QDdHlgfG0G6XbZQXflBjcVHC6aZ1gBqH1nnc1o3krW7Fa91RNJM/50ji4jhfd1CwTE8TmqH6c8jpnbLuN7DgBsA5gvKqCsfNBRXbWzbhFoDpIc4+gBVw1pJAGskgADeTqAWwOSeA/AsNMbv7jmSSSffc3Z1ANb1INfGbB0BclxZsHQFyQXTmR+hy+Xd7ONWIq7zI/Q5fLu9nGrEUGLyp+hVXkJvZuWsoWzWVP0Kq8hN7Ny1lCD6ryzNd7/PSfxVGq8szXe/z0n8UE7WAy973VfkZPUs+sBl73uq/IyepBriiIqCIiDa1E
RQEREBERAREQdFZStlY+N40mvaWuHEEWKxuIX+Azhxu5sErSeJEbhfr1HrWZXRUUjXte1w1SNLXWNrgt0T0GyudsJjfLVgL6r5+K7DvByfmye9Piuw7wcn5snvUV05mu9/npf4qdLG4DgkNHFyVOC1mkXWLi43O3Wde5ZJAREQV5nCzffCiails2a3dMOpstt9/qv59hVNVdM+J7o5Wuje3a1wsR1futqFjsYwOnqm6NREyXgSO6HQ7aEGsaKystsg6amaXwulb4pc1zR2t0vSq1VBc4IXPc1jGl73GzWtBLieAA2qxcishKaqbpzOlOzuQ5rWnsbf0q0cFyepqQWp4mx8XWu49LjrKCFZu83pp3NqasAyjXHFtEfjOO9/q9VgYl/Zk+4/8ASV6VwljDmlp2EEHoIsVBqmzYOgLkr5Ga3DvByfmyf/SfFdh3g5PzZPegx+ZH6HL5d3s41YixWT2AQUUbo6dpa1zi4hznP12A2uPABZVBi8qfoVV5Cb2blrKFtRWUzZY3xv1te1zXAG2pwIOsbNRUR+K7DvByfmye9BQyvLM13v8APSfxXf8AFdh3g5PzZPepFgOCQ0cXJU4LWaRdYuLjc7dZ17kGSWAy973VfkZPUs+vNidCyeJ8MoJZI0tcASCQdusawg1bRXz8V2HeDk/Nk96fFdh3g5PzZPegoZFfPxXYd4OT82T3og//2Q==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="greenlinepalet">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAApwAAAA/CAYAAABNXBjMAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAADZJSURBVHhe7Z0HeFVF08dBUBBBKTZQQVEBFbCBXUTUj1d5RTqCAtLtSkdAQRREilSlhpqQ0AmETgKE0AJpEHovgSQkIYRUCMy3/7lnL3tPboq+IkLm/zzznDZnz569Kb87uztbgEQikUgkEolEomsoAU6RSCQSiUQi0TWVAKdIJBKJRCKR6JpKgFMkEolEIpFIdE0lwCkSiUQikUgkuqYS4BSJRCKRSCQSXVMJcIpEIpFIJBKJrqkEOEUikUgkEolE11QCnCKRSCQSiUSiayoBTpFIJBKJRCLRNZUAp+i6KCn1Ii0PPUnT1x8kj4ADtC8q0boiEolEIpHoZpMAp+gf155T52j40l00ZHEE/b5qj7K91H1msECnSCQSXWNdsbbZ6cqVrB444+4+d77Z6cKFCxQcvI28vLzot5G/0aDBg2jwL4NdbfBgPs/X3FzH+SG/DiFfX19KT0+nNGWXMjOtJ+Qu1DenOud03d353Pyzu5Zf9a8EzoxLmbTrRAKtDD9FS0NOKjthGfazM8f1JdtPUNC+GErNyPsPoSh3pV/MpFURUbRkh/1zOKHOnSS/0JMUnZhqeWev2PNpDJp+6r6B88Jo1oZD1M8nhCas2UdzNh+xvEQikUj0d+vy5cuUkZHBkGaHIRxnqvPp6jr8tHDu4sWLdOnSJeuMwxdlZKjzZjl6/8iRI7Rs2TKa4uFBH7ZoQc/XqEGPP/4Y3XPP3VS4cGEqUKDA/2R33HEHVXmiClWqUolq1KxBrdq0pskeU/h5QZs2UZKCW3dCffEu9nfXwjty+xjvCqE90C5oCy1uS1UWzJ1ye1Z+1L8OONftPkONRgRQxa/m0j0dvKlMe68/ZSXbzKIGw/wpNjHNKlH0dyj+QjpV776Y
Srf1pDLtXNu81CeeVGfgCjoVn2J5Z6+AyNMc0Vy9M4o6T9pM3WcFU9jReBq5LJLW7462vEQikUj0dwqwBAgyYZLBUcEVzH6eQdMEL3VOQxbOa3/47t+/n0aNGkUvv/IyVateje4vez8VvvUqWBYsWJBuKXQL3VbkNnr2uWepcZPG1L5De2rbri1bm0/asHXs1JG69+hOffv2pW7du7HPJ20/YR9sW7RsQf959z8Mm4UKF+IyUbYdRvGMb779ln7//XcKDAyks2fPugU/vKP5/qYPjvGeJnya7eLON9NqE5F7/auAE9GyR76cSwWaTKECjZU18bhqOG6UB6s/ier/upbHCIr+PiEyeX9H76yfC6ypB/Xx3pFrVw0E4By0KIJ8Nh2htTtP029+kTR3y1EavXw3JcpnJhKJRP+oAEt22MK+Pu9OiNxt37GDuvfsQS+9/BKVKl3KBfpgd955J9X9T13q/Gln8vDwoJCQENqzZw8lJiZmW25ehGcDIHfv3k2bN2+mX4b8Qs2aN6OGjRrSAw8+QIUKFXKpR9Hbi9Izzz5D/fv3p3379lmlOGS+p/n+EI4Bl2ZdcU7fY8qdr12ICNvBPr/pXwOcR2IuUI3eS6hAw8lUoNnULHZ3ey+q8Plcw+ZkOX7wUx8q2mI6fTx2g/qmkRf8EeVViELe2WYWw6XLZ6OAs3Q7L45M50VnFbj+sngnj+HcsCeaRirgHLtiD0Ul5B4dFYlEItGfE8OUtQ8w4kicgh/suxP8dRQTMv1w7eDBgzR6zGh6vdbrLmBXpEgReuihh6h8+fJU+83a9Ouvv1JsbGyW56AMbVoMbOrY7OrHdcCl6Ze9rtD584m0ePFiatO2DT3y6CNU7I5iWaKfderUIW9vb0rPSLfuu6rL6rl4tr2+OHZXlys4p9pIt1Nuwv15f5+bU/8K4MTnO2pZJBVEtMyEGQtoXu7nR4uDj1PwoThlZ7OxWNq4N5omrd1PC7Yes0oW/V2avv4A
3apgvkBT47PBfmMPqt5jMZ0+l/v4Ta2ktIsMsJv3x9DhmCT1C5t/fwFFIpHoWgmwBHgEzAGjcAyocwc9uAYQdU7GMUDq9JkztNh3MXXq3IkqPlqRbr31VgY4dJsjgvhG7Tdo+IjhtHPnTjp56hTtioykNWvW0MKFC2nO3Lk023s2rVq1iqObeAaUkpJC69atc/jMmcPm7eND8+bNo/DwcPbRsIc6Ywwl3iVZ3ZeSmv3/G9T/8OHDqixv7oKv8HAF7nqHoc6VKlfmyCzGmMbExDjfWUcoXXHTIV0Pvq723Z0zr2mhTLP987v+FcCZmJJBdX5cQQUaTrEBjYeCnGkcAfsrwrjDY7EX6PjZrJac7vhlSs24xGMP9Xn4xydd/faTmJpBJ+OSXe7NzeCfYpVvF971xFmUl/cyT8aluEyCOqvqd8yNH+p+PlV9i8q87PJOrpbM7fJn9aXHZseQBf3ZGJ9R63GBPKkoJ6Wodna8t6N9UL9T8dg6zl1Q9U5TZeCa/d1wHKX88Q00Idn9u8NQZnoO8Bqn3vvEn/oskykqIZXbUyQSiW50AYgAkoA3ROYASTrypscfmuCE/VMKIH/++Wd68sknnZHCgrcUpLLlynI3NiKGJ06ccIGt+Qvm06uvvUrFixen24vdzvegWztSgagGztOnT1Pz5s2pbNmyHB29o/gdHJUsU6YMj+HUuqz8UT9M5kEdOfqqLC/CPWFhYfTjwIH08CMP0623OUAZVrxEcerWoztNmz6dPL28nF39qJ+uoynddiaIa+Eat6O6hjoyYKpzMN2e2Jptmx/1rwDO0KPxdH8na3ygCZxNPOjeDrNp0/4Yy/PPCZNTKn89n57suvCqdVlAz/ZaTMtCT1Lg3mhqPz6Inu3pq847rlf+Zj6NXb5HAdRlmrHhEDUZ4U/Vuy9yLcNu1r28VfZ0j8XUdvxG2rw/1qqJI++kR8B+ajTMn6p2y6Y8XY7tHMpr98dG2nH4LJc1ZPFOt76V
v1lAw5bspAHzwtQ7LVY+C1x9rHveHbyaJq3dx4CXF11Iu0Rv8hcC23AH67Ma4RdpeWavHYfj6JV+fvSEmzpV/nYBLd1xgg5Fn6f/DFpFT6hj0wfH/x2yhrw3HaH3f12jynC9X2+f7+WrwHgLRRyLt57qEGB1xNJdXHbVbtY99jLMY8Ne+G4JdZsZzJFYkUgkutFkAo7eN+EnOxhKTUujiJ07qWfPnlShQgWGNEQ0MVkH8PZdn+841ZEpfT+ilc/XeN4RVSzoADxM9Nm1a5cLcDZs2JBKlizJ13kCkALZYsWLUa9evdgHQolXa+UQ6grAM+ubk+IT4mnc7+MYevEOOuJZ4s4SCnLvoIqPPkozZ82iM9GOiatmudjT3e1a5r6uC3wAnGg3na7J9AOosp/yz6/6VwCnR8BBuq3lDI6WuQBNoykMgzHn/9qM8zErdjsgCSCrTR2XbudJXygwefiLuVSggXFdPa/wh9M4EXlfnxAqjDrY70eUD+e0NbJd19ZgEtUesJzrjujkN9O3UqEmRnn6vizlWfebhnP1J9I7P62kxJSLnFaIYc+Nb8Uv5znaDu+VXXnq/C2qrUct252nMP/uU+eobGcfa4KQKlt/KVDHpdp60oqwU5Zn9kIXOsbYcp3Muqh6lmg9izbuieZoJ+Aebefio96h5Cee6suH+lLygbqG69m9myoPPzNnrC5+THYCpLKv/dn6nDZ35eGc+qw6TQxyRgBEIpHoRhCAR3fpwjgCZwMhCOcASsmpqc7/CQDCiRMn0p133ekEQsBa2QfKMrwBNgFVXL7aAqSwD/nM8aGaL9R0wh3uf+LJJxg4dYQwKiqKGjVq5JxwpGEWkceevXpyHTFeFBHK0NBQ7maPVPej2/7YsWN/CdwmT5lCr9eqxcMAnHVTQIx9zKzH2NRoBZ2AR9QTz0C76e58vCdMgyPqyD7qnL1NIe3LEU/rXH7WdQdOfEhdpm9z/GPXoKmt4RT65PeNPKEEUDNv
y1Gav9WN4bx1DbkcA3ad5i5eRAK5XDNqqkCr2Mcz6M42ng6gAERpU8eYJf/Logi6o9VMx3WzPqqcW5pPowcUOD2iwA6+iMAyKDufofaxbexBjyqfXccTOHdlkZbTHc9wKc+DCinANcvD5KiCzQ0fNnWfeo+HPpvDXc6YEV6Q7zd8sI96qGeU6zyHy4IBCF3LskyB2Ws/+HEXdW5asuO4oy34GUYZqk7P9FxMh6Jzj/5tORCj3k21FdpAl4GtAr1qCjLRzY+E8IjSOp+lnwNT5/CZON5rHj2o2gLDLVx8YKoNCqrPyCvoMD/3V1/1M6A/X5vfbeozKf+F1VZfzaO7FNRmeS5MPfuFPkvzHBEWiUSiG0kAozQFnHpsJLqXZ3l60ksvv+zoEldQBhhEd3fzZs1p69at7If/37hXw5fWnLlzOD+mCZzokgdwplrPOHHypFvgvKvkXdS1a1faGbGTXlbPR5f7gw8+yJORHn74YapSpQr16dOHy/grmjpV/c99oBx3r+N5eC4iq6jDDz/8QKcUCEP2qKZdgEhEMgGj2cmEUXftlN903YHzbFIa1R20yhU49T99dW786r3c/X1fR28GhKIKFrPYR8rUNUAdImFjVuzhbuC2f2x0RK50uWb5qmxAW0kFZAAhbDFpCRNgPhqzPut96h7AYS+v7bT75DnaG5VI+08n0pqIKHoEkVIFmC7+ClLQFQ7gbD8hiOHZ5boCHkzC+WFOKEWeTHCWt3ZXFD30+RwHdJn+6rha98V0NPYCNR4R4NpelqH+P84Poz3O+p1nSMcMfidwGW2L+gFgc1M/n1AGVPNZjjImU6Ph/pSRh0k/SPBexB7Fxr5q56a/BbAP0mLxTHg7HFptuWHPGW4jvFvkiXM8HMJRjuGr9vG5jlu5h9IyMumlfn5ZPxv1XES5J67Z5yzvwJnz5Lnx8NU6mmWq+rz3y5o8vadIJBLdCAIw2aN2Gp4wbrP3d70ZwgCMADJEBZECSQNZ
TuDkDjh1hBPPghBBzQ44u3TpQsHbgnnWO64BeFEHGPJsfvrpp1xGdkLd8G6IUtrrOXv2bAZYAOcthR1RW13HDh060IYNGxi47ffhmLvFrQitO2kfRIvtz8Yem63c/KTrDpzhx+IdXdt2IFLHxVvPIn8FYEMRpQLwwABa8IUxGFiGYwUvmHyESTWYNc1plkxwQ9kWTLz+wzIasTSSxw6u3XWaV74Z6ruLenpu5y7ZrMDnwemXQo7EWTV3COmcOMqp6+P0n0L1hqxmOEXC9KzlOSKW9rGB6L5GxDOLv3rvJgo08XyMz8xyvckUur+zd5blITH+tRzKQ/1Mf9VWaANzgpQ7Ib3UOz+vzArMqrxb1Lb/3FDLM3tlql+wLjO2OaKydlPvMXTJTvZDuiTHZ5TVB/fb9fmUzVfb3NgWViDvE3SE9qq25Eh1lvI86MU+SylZfSkxtTzsJH+pcfG36oOfi3z8d0IkEt3gAlACpNCVDaBMSnL936OBCMJ1RBEBYYBAgF6RokXoxZdepOPHj7MPoAoRO8zwBlyZchvhfOpJzsOJeyAAZ+PGjal06dJugTNkRwg9/vjjzuiqLgsR1w4dO3AZOQlgp80UxpdWrFiRihYt6ixXP7t0mdLUUEHwYl9f5/hUswzUXdcf5wCfMH3e/ixEczETHm2Omf4XknMP8NzMuu7AiW5wxz95GxApyMB4Pkwo+mzyZnpYwdmjX82j8p/PcUShTF/jnl8VtEBbFGiV0V24Nh+UA9B1J0TRbv/Y/XjS53v70vbDZ3mWtLZp6w5k9VWAArj6bnYIL8/p7Ep2Xlf7qh4A4p0n4h1lxTrKQwJ0RGzdASKGCGAJSeS9zHK9wRSe2HMhzXX2HiYHYVxqljoqeG89bkOu40oQAa3AEdyswFmizSxCuqrchITunIXAXoayW5pP5XRW+LbdfOS6rO+l2hKf92yri1wrToEyoJH9TUBEvVp7cmR5+oaDzjKc1/nYg+EdQwH053hc
vSeg1sXP8kUkGtFPkUgkupF07tw5mj9/PufEbNqsKU/ceaTiIwxWNWo+z2mOunXrTmPGjqWQ0KvBA3fAiQgnurjPKHCCTPCyCxNwqlWvzvdp4KxUuRJt3LjR2aWO5S/fr/8+lSxV0gmV2BYtVpS++uorCg8L5zIKFXaAJoOh2i9RogR99vlnXEZuwhhTDYRaPj4+9MgjjzBAuwDnrYU4aXytWrUYSjHEAFqxYgXXs1mzZtSxY0deCx46eOggJ50HoH799decigmpnpCM/vvvv6eu3brS/9X9P05Gj0lWj1d6nHOXYgWl0LCwHFM73ay6rsCJ6Flf7x38Tz3LP3oFWI1H+HOi8PW7zzAgovtz4bZjjhntGkw0TKgyEBFF9zuErnhH3khb2Qoc0dWeXWL4CWv3Z4UYmDq+q40nd+0iAoqZ7s/18uUopYsfTN1/+8czuTsbSzaiKz5LecrQ/e8oz2Eoj6Obpi/21TsUUe8CuAOQZhm/CWs4hb6ausV6C4eQ3xKztp3vo++x9kf67bY8sxfak6OEur21MbjPzxJRdaeIYwns61IG6qCOEd0+nZBCMYlpPFvd6aPrqo7v6TCbx3eawqx3DLPIUi91jNnlmKjVaVKQIyJuXrcMqybpzxGGLxNYsjOLryrvoU/n8BcNkUgkupGENERVq1ejW265hQ0TZpB/8tPPPqWHylvd1cpq1KhBM2fOtO4iOnnyJM8UxzXAXsFCCjiLFqWXXnqJUyBBiGrqcYz2CCdgcsSIEfToY486107HspbIiQl4+7DFh1TrjVoMvhpI4Qd/LG2Jbu1NQZucs+M1GMK32B2IcHbMNVgC6eikCcWA4XK2MZy6Dq3btOZ8oXFxjp5MdP9jeUxcg91z7z08Ox/atHkTvf3O23weMB0WFkqJiedo3LhxTn+06+jRo3nC06BBg+iJJ56gBx96kHx9fbNEmPODritwIgF4vV9Wu418ATjcddcuVwDkEhHVYNLYg55QoAEo
xQ9Xp4mbHD76ujb1LET93AmDhNuN36ggZXLW+2CAGwXCLsZd/DB1TZu6HzOykZCe64Hy7GXBMLbQbXlGWTB1/pEv5vHEG6Rb4ueZ5ai6oht5xvqD1ps4hPGx6DZ3jmHU76TaBVHcoH25p5sasijC8fnY20PVE59dXiYdeQcdoTvUlwF+F7MM9V4Nhq6li5lXKHBPtAJwNz7qGIn/kV/UFCLLHAm2f6FQ79pmXKDyz1D3LXW0qXkdhnfBO+k2h9mHa2hT12r1X85tKRKJRDeSAJzVn0aUsBDdd/991LZdOwVG4XT48BFeGjI4OJg2bdrEM8ExO1sLkbrly5fzmufIjwl4QhkYb1n/gw8oSN0D4X8tooforseYSRPsEhISeLb6z4N+pvfqvcewhigpytGgh2OsSoQucsxM95jqQWeiz6iCidavW+8WOHFPu/btGHb1OFRtPLtcQSLvG1FNCHVDdHXU6FHcFu4mDfXv35+7+rUA0uPHj3cCZLkHy/HEImjL1i28rjvOP1X1KQreHszd8EuWLKE6b9XhyGaZu8swZH700Uf0xZdf8LODNgXR+aTzLm2VX3RdgRPRMY5q2YETEb2WM3hcpSn8MPWfF3oVMkwIUrBQf+gaSr2YyalwagG0ABCmn9oW+3imArer+TFNYaY0d9O6gRREKUu396J7O87mMZtOw7E+Z+2XbuvJXbbIm+kAvqzvh/IQUXNbnm0LEMPEmr2nEukZHl9qvb82BUal2nrxZCFTSEXEEVOAEz/3qj/SJyUkZz+7DkrJyKQWo9c7YMx8HtpftW1vrx3qM7GcsxF+p/pi0pGug2kKxAdYXyqQy9PR9W/zUfchcotxoKa+VOeytqvDf9yKPTx2tizndrU9V9UdX1juaW+1r96a7W5YadWu307fxu8hEolEN5IAnE8/8zR3F993nwLOtm0ZLhFBnO3lxTA1YeJEWrRoEacgMnX+/Hle9QdRSBP60AX+9TdfU1h4GPsBypyp
f6w/lBpEtQCwADlETtFdz6b2ES3FuEY7fMEfKxW5A06M4cRQgOykn20vE/wAqC1foXwW0ET3OqKVc+fNpbNxcdwNz93x6r1M4AREauDcum2rEzirVa9GO6whCYDdSxcvUUxMNC1duoQ+++wzqlylsnN1pmefe5bfLTkfjue8rsCJWcmctkcDpDYFEuU/m0NHYl2TyqamX2KodIKkNoCGKqOvzw72A1DifoYcE2DUfU/3WMSrzbjT1gMxjnyTZkQQpuqD8aQLg49xsnhE47I1dX3D7jN04PR5Xivc2e2ry7LqgVRAiNbmtTzAOSKS3O1rwhvKVMfojseqSaZmBh7iFFDO9jWe30wB7KVcaBGr7TzVdZFbaENGgFmq/NyEbAENhvtzG7qUoeqMKOvi7Y4xoBhPmiWyrJ6DMZ6T/fezjxainTyRyf5zoMpEaiPk9FwUfJxuMT9H7aPqgcT3GKvrtr1NQ9urLcbXikQi0Y2mXZG7OPoGUEM+zaZNm3JUs/Nnnem+svc5QQpQOnXaVL4H4MhbBVyHDh2in376iR577DH2A6RhnCVyZTb/sDlF7t5NKdZYR0AeZrlzwnN1DOADsEF79+5lyEIEUIMgthgniclHgDQTEDPSM2jD+g1UobwDOFF/QCK2qMNjjz9GH33Uklq2bEEtWnxIzZo15WOPKZNp//79XAf9HtDZs2dpyJAhVLVa1augqd6jbLmy3MWNnKFYZhOQjboAgrFKEWzSZPV/yVqTHf79B/TnMgHtb7/t6FLHhKhtql2RKH/YsGG8glKDBg04tRTK2r1nD3Xu3Jnuuusuuvfee3nNd+lS/4eFcZZuJ7QokEBkEEnOTe08nsA5E92BRpGPpjvhBV3Lt37oZvxmg8nUYvS6bNPbzNxwiAo3V/XRQKdBpeFk+uT3wDyNGTGFBPKFUQ9dnjZV3sdjN1heeRcidzwu1SwPdVRg9enkzVm+0XWdEax8bW0FazglT6sDYa1ztxOo1PPw
OWBCV05KSb/IY28rfWOM39RtqsrA+FcsR4nI8kt9l7r9XMupLwCohynM5HdkNlD+NpgEyJ+IS6G+3iGOJPE4r+uvfAsqG7QowipJJBKJbl7tUaD3/vvvU7ly5RjWEJ1EVK7yE1WoZOlS3EWOcZVNmjQh38W+fI/ZHY0tZlmPHDWSVw4CXGlDWegOB1QdP3HCeQ/+D2nTKZAOHT7Mk2kAWYh0Hj12jGF1wcKFnCoJ0T746zIAeoEbAhl0UW+M/0QUUhuDr1EXGCb8dOvWzbkOOwSAxEQljEfFmNXCtznuA3Defc/d1O/7frzm+rz583gGP4T/ouZkI8AylvYErKK9sFoRxsC+9fZbXAZWK6r/QX06cvQoz0YfPnw4T8zCczBRqM0nn1CXrl3p1ddeY3+0o7+/v0Q4/2lhuUEnMGCrTYFGqXaevLJOw+H+1GCYP30wdC3DBFLxXL3HAgkFLwCTA2eSOGzebeY2Puf00/coQBmyyDGL3Z0Gzg9zdB+bgIV9BUJ1flzOoBOdmJqjAY4wsQdrpiNnp+OdjPJgqrw3Byynw9FJbsswLWhvDOcVRQqf9np8KdfLKtd6rym2KCBmqyOSlwXi1D23t5xBqyMcudRyEt7DFW6tfbXFhKe6g1ZzHk58Pqb9Rz237o8r6JupWxn+72xtm3SEchQc1uq/jJ+DVE8YEuDiA1PviiEOGCJhCl8sXL6o6LZQ74olMJH0v776eckyNEL5IVdphwlBPFHJXXub5que47XxsOTfFIlEN6QATegiDgwM5NnVmNAydOhQtqlTp1LAugA6cOAARwDRjQ1/dwKMIeIIcEP3McZRMuQVLsSTeF588QUe/7h9+3aera3hDcL/ZAAk0icBKtHND/jEikGYXARfRELRNa8nH2nQxXhITPKZPmMGG/ZhM2bOdB57zZ7Na6HP9vZmOATQAjonTJzAsI3xmhpKYRhT2bdfX4Y+TA7Cs3QkVkMvtqZwDqsbIYcnIr6YnY6Z
/yNG/EYLFy2i+Ph4njyl85miPVeuWMkTjuAL++nnnxScz6K9+/ZyW+RHXVfgRJcspzjSoAFwYDhQxwAIwB+gAVu9r3yzrMSjQAPwci45gxIupNMr3yPZt7so6AyGCHfCD1iPWcFXo2KmAVTUtlirmbwMI1sbY2tY4RbT6Ps5IZx0nPNE2iHKKA+zv13Kg7mUP5PHeg5aGKEAKI1qfrfkKkA6YcuDk6UHRF4d6AyFH42n8p+7SWeE6OQXc2nf6Zxnl1/MvExNkabI3s2tDe+FzyOLqfZDG9afSD/OC6N+PiFUCFFjXV8YylP3f+GxmZ81fvU+1+va1Ls2VABrV5cZWx3PN+vF+x7Uw3O7+nZ6hWqrLwhZcodahjZ1trfdVJtjW1x9NuienxV4+E9HtkUikejfIsDSnxX+H+rJN+bfP5SFcZiAvQYNG3LET4+DxCxzTJLBTHNEPrt3705r166lo0ePOlMh5UV4LsAzr/UGUO9X0ByxaxdNnDSZ0xRhmUozCoq6IcKJWeh+y/y4y96dAMaoK6DRDp3ZCX4AddwL4NRtl9f785OuK3Ci6/Ptn1Y4oMYtvFgG2PxgMk/44Uk4dth5fwLV/XklR/WQr/G2Fgpi61vrbWtTEATQyCmy57nxEBUFAOu1uu3Ai/M5mYKsUu28aLM1KQlRx9sxkxoApuuhy+R9dR7XYLoMfQx7fyLn3MQEmKkBBxzRXfjo+2HKD8AZtPfqDMNLCha5SxnAZb4DtqqO6A63j4+1C+Ngee1yVQeXZ9rLM835jEl0d4fZnMKqeo9F/Mwsfqre3WcGU1LqRXoBIG33galykJvT1P6oREd3uv3zRZnq5wgrN0F9Zu9Q5433t/vqNtbtbrY99v87kZezzG68r0gkEt1IAsCZIAcgAiBx9FEZIMkUrsMXEUp36IR8nIhAInJX77/1eEKNBjwY0jBh3Gj1p5+mNm3a0ODBg7m7eeXKlRxVRQQTM+NjomMoNiaWo4SJ58/z
OE90N58/n8SRQvhpQ5c1JjetXbuGo7T9+vXj3JZ3lryTu6s1ZOLZxe4oxkD8/PPPMfyGhIQ4c2tmJ7yzNrvcnYNwFm2kpduNIVSBK0yDaHZl5xddV+CEAFPoem49LpBajl5PLUetc2xNG7OeWo8NpN/8InlN9VZjNyi/q9dbjAzgJR3jLqTR2BV76EN13HK0UY7ybaHKxTO2Hcw+n2Jy+iW+H+M18Uzn/TDz2Hi2aXguxgfqFD7oVh+70lYe7jXLwr792PJDeQMXhFvrwkeoY/VOpq8yjElF+RHHr46nRJQXbYp3dvpb2xajAnjWN2A/J/mFnKTm7j6LPBg+D9QXK/e0wudqLwefh3oXQHTIkXhqo+qP93DxUYZysAqR+QuKMjFz3uXzhan3a6HKnW6lhkJezx/mhvLPDV+3t7s7g4+1j7abuu5gvv7jIBKJbh4BNp05M9XfNZ1SiGeXWz4QYAnnEeVjcLK2OSk1JZW70wcNHkQDBgyg+vXr8+QYE0C1YSUhzBRH7kqMccQEICx7+XyNGlS7dm3uBm/YsCHVrVuXqlatyj7a4IsxpwBJd2XDMAEKuTIXLFhAoaEhFKegFX/H8Z4mGOZVuBdthsk/uusdQpsA1AHs7oTreCa3r1WGCfz5UdcdOLWQiB2Jyt1a5tUfFHxwGFNnXudj9iHet193+ijLLuG7KTwK5dnLyM3wDHfl49T/Uh7eOf2S+iZqu659YOYvEvYzcvE33N0K9XV3f14M9/HnmUMZfD4TXQ+OfXd+Dh/XX04+fzGrL5/HVpVpCm3xV9s+H/9dEIlEN5kYgDIzcwUehjPlB8tN/PfVDZAiGonE6D4+3tSpU0d65523OR1Q8eLFqaA14/t/MUROMXkHa7tjNZ8u3bqS3/JltNRvKc9St0sD31+FPXf3Y58hMg/tJHLoXwOcIpFIJBKJ/nkBnhDVRMTOXSwCwAXLi7KLJGJS0pbNW2jN6jW8XCTGUqJLHvtIEzRt2jQa
OXIkDRnyCw0bPozGTxhPnl6etHTpUlq9ejV3xWOVJCSkDw0NzXHiDeqK98lrnXMT2gdtY0I4ysZ5E0LtApDqqDL8/kw73owS4BSJRCKR6CaXBiQNPIAnDVH6mglUOMZ4SszOxsxzzCjHGErto++BkH4Ik4MQXYRhfKfuasYsdEw0QhnI64n0QeeMWexx8fF07PgxLh8+Bw4eoENHDjuTxJ84eYJOnjpJMbGxXF90keNZqBe2B1WZyHMZFhHBqZaiY2KcgBefEM/3o1yM/cTY0X379qnjIzw5KCf4c7aJMmwZHG3tk1OXOoQy9DMQCc7L8ISbWdcNONMyL1J8RpLNLhjm7pr9nD6f3TWYeT0nP2158cnOzGfZz9uP/5fnaHNXjnmcl2f8GR93vjndn5ey82ooK6/lab8/8/ys5ccpS85Md/uNXyQSif7tQqSRodICJ3u3sAYinE9RAJZk5cOEMGEH66vff//93I1dokQJev31151LYAI8dRqlMWPGcAJ1nS6pffv2HJXEOuKvvPoKj7nEpB4kSEeaooqPVqSPW31MK1et5CUfq1SpTKWVD/zQ7X777bfzikKY+IOlNR+rVIk+atWKps+aSe/We4+fgWfpiUFYNrPM3XfzuR49e/AEJLznwJ8G8lhR5PHELHWMAy1XriyPL61Tpw717t2b0xuNHTuWZ7qb8Ij78X4wbiP1vmbkFsdos2RlaFt3gj+u4X60F0yA8x9WyqV06hU+k+qs7Udv+f9g2ffGvnnOft7dOX3e3NrNfh3b3O7Ji5nl5HReH9vP5bRv+pv7djN9zK1p5jl3/tndY/c1z5vnzPPmsf26eazP2e/LzXIqx37enWlf08xrji1+PltvGU1RqXHWT65IJBLdWNIACZn7pngspgIrdENrqAK0eXp6Ope2RML1l196maOXdqErHCCJJO3w7dipI3nN9qKXX3mZihZ1QGiNmjVorb8//fLLEKpVqxbdXeZuXkmob5++nD4J
uUJHjRpFNZ6vwf5Y6/yRihVp+PARtGtXJIWFh9NSPz9epxzXMSP9IQW5gwb9zMt14n7/gAA6cPDqZE+sClTugXJctz/++J2X0pwwYQIfm2NJ71awihybWP/dnVBedtFJnMuu+x6wiXY174Nfdp/Dza7rApznLibTu+sGUgHP+lTA6wNlDWxbvW8387w7f/vWtNz8czqPfdPsvqbZfU2z+7i7z37Ofmz30/s5+Zo+et9udh/T3Plmd800089u+rq5Nc3ua56zXzfP2/30cV6vGfvq57OK35e0L+mU9ZMrEolEN6YQXQMYYZsd8DA8WVE+RDiRbxO5NQFliB6+/LJ74AQoli1blhPBw7fNJ20YIj08PDhtkY58YrIPjrt06cIwi0Tt6LaHMCYT97z55pvsC8Ct9nQ1WrN2DV8H8CFZuwZOAGmpUiWpTp03qVOnTtS2XVtq1boVrxykx3cO+HEAp2tCXs7x4/9Qz0oib28feujBh/j+Wwo7ABkA6u3tnS1w5kUaSmFazja3gBNbfZwfkfO6dakfSDpNK8+E0vLTIbQiKpRWYHsaW23mMfbt1/V5+7nszPDl57k573Jsns/uOe7O41xu95o+ufka5mwnd372c9n5uDtvmvbJzi+3++1mL8s8tpUVZb9mHpvn7NfNc3Y/87xt3+XnAGb6OX42wxKOUOaV/NsFIhKJblwBgtBdfkUBjo625QScpgBfiPqZwPnKK6/kCThbtGjBUUcI4ybRZd3sw2a8rCYSwwM8K1epTL8O/ZXXHweEJaek8MSg2m/UdgJn1epVeQ12CO9hAicinFhW8rnnn6P3679P7773Lr3+xuu8whB8IQAnIpxYA93DYwqD6Ny5c+mhhyzgtCKyAE68K6K6dmlotLeZjmzyNXWM6/A1x3m6i3DmZ8mkIZFIJBKJbkJp4ATwMBQ5TjMIcZTNgignLBlwFHv2LEOY7lIHoFV5ogovJblaQeAKBYeYLQ5hDKfZpd62bVue
Xd6nbx9q1LgRffPtNzwbHROIhgwZQpUrV2Y/wOwYBaPxCm4BbgDON2q9wdcw7vKpak9lBc46V4ETMNn7u97kH+DPKZFgfn5+PBseS1z27duXc34CTDt06MCw2bNnTypdurRLlzrqMWPGDDpjjE/Ni3S7ISqs2zI7oV3xDnpMaH6UAKdIJBKJRPlIiMLxJBgFQRqEAHyAIS1EOH18fDg6CJDUE3gQncQ+YO/DFh9yl7jHFA8qX748T/QBwKGLG+DXrXs3euaZZ/g8JvY0adqUx3JiglHNF2oyqCICCgHaAJT13qtHxYo5JgPBJyAggK+jjuvXr6f36r3Hy2miDjDUp2SpkmzYv+/++6l+/Q/Ib6kfJ4BH/k/Aa/ESxdkwXrNChQoMz+3at6NevXvxakmYXa+HEwDGGbwVGGqoxJbby4JF+KQpf0RNNUBqXz37H22MdsU+DDPdEcnFu+RH6BTgFIlEIpEoHwrQA/iBMWAZAnxhkg0ilfPmzaOFCxfSgoULaP78+RwpRO7MTZs3871IaQTAhA+uBQcHc9QSMIqURJjQM/iXwXTvfY7VhxA1bf5hcwoNc0RIkeJo1OhRvD57pcqV1fUyPO7y1ddepY0bN7IPnoWu+kqVKnEXOnJ4Llq0iOujDfXExKLg7ds5+TxSIK1bt07VW9V9wQKu26qVKykiPJxTLmnANMVtAli02gSGcZnO88q0n33MppY5mx1+MC1zP79JgPM6CGu+p6Rf/SYpEolEItHNLEQysTqQnkCENEkjfhtOUQr8ENn8oMEHvMRl6zatqV69ehzFfKrqUxzVRHQQk4xKlXIsadn5085WqaIbSfkGOKPPpVLgnjOUZK1zHpeUTpv3x1B0YiofQ7Hn02jL/lhaGX6K1u8+Qyfikq0r6luJsjOqjE3qnlXhUereWErJcEAjytq4N5rWq/LX7T5N2w6dpdSMrGNAohJSaOyK3TR8aST1nxvG635jCcXsdDgmiQIiT3O5Aarc
/afP87cj1Bl1P5ecQedTMyhoXwzXdwP7ObaJ6j33RiXy/TjGdezvPeVIuHtcvdsmdR/gFzoZl8I+h6KT+BjvtPVALMVfuLqaA9aIjziewH5rdkZRsHpPDc54D5S9dtdpbr+DZxx1NYVvfXtPnaMdh+PUt0THex9R7+iv7tF19Fd13KmekZx2ibe4hnNoU6yPLhKJRKIbT/sP7Od1zjEZCNBYoGAB7mp/o/YbVPvN2pwv87/v/5c2Bm2kP/74g30YODesp8vq/wXGk6JbHvd16tzJKlV0IynfAOcviyLo4S/m0e6T5yjtYiaNXbmHqnVfxBAKMFq64wQ1/S2Avpq6hX7zi6TOkzbRWz+toNUKrCDc999f19D7yn6cF0Z/rNpL51Ic4fgJa/ZRhS/m0ucem+n7OaF8nGSBnKmp6/bTbS2mU49Z26nOjyvo7Z9W0tHYCwr6MJ7DcrKUpoD1k98DuY4D54fTgHmh5L/zNF/71Xcn1fxuCe06kcBQ/NuySKo7aBXXoevMbTRCAS3g1nfHcfpq2hZ6/Ov59J9Bq2mQaoPlYSe5jK+nbVVlLKWT8ckM0z1mBVO5Tj70wdC1dCo+hWYGHqJne/rSHgtQcV/LMeupk2qXAer9YaOW7aazSWlc98n+++m5Xr70lccWGrggnEHRroTkdKqv2q/V2ED+DCD/XVHUy3M7PdV1Ib3+wzIud1HwcQo9Ekev9POjFqPX0+jlu+nzKVuo3pA1zvqLRCKR6MYRurcxbhIgWbNmTerStQtVf7o6FSlShO25556jfv360a5duzivJ/yQqD1gXYAzwonJPjiPFEiiG0/5AjgRLXtVwcv9Hb3pBwWEAxXUVO22iF7su5RiElPpUPR5BjtAG6Kc0PZDZ6lYq5n06eRNfIzIXH0FY7X6L6O+3iEcHQRopStwajjcn0q29aReXttp0MIICj/mmloBQLtDAVTbPzZS9R6L6MFPfajZyHUK6g5SSwVUk9bup4tWxE/r
SMwFVd/ZVEOB5VAFmFPXHeRoJuD0rYEr6f9UXROSMzhqiCjle7+s5rIBmog24jyiiPO3HKV7O8ym3xUg43zm5Ssc7a3SZQE1HqF+kdUx2uDZnoup4bC1VKP3EvpGwSigEACIeu2LSqQHOvvQu+oZxxQgr46IogFzQxnMUQfIY90BeqbHYmqioP2P1XtdIsdaiMSWU+UAmLVQR7T1fepdv/PewSCKOk0JOEB3t/OiZaEOwNx+KI5KtJlFTVW76eioSCQSiW4MYalKJIRHRHPAwAEUtCmIBg4cSA0aNKBu3brxuE/HRJtLnEsT6Y8++/wzBlAoKCiIWrZsyZOGxo4by+dEN5byBXBuOxBLRVrOYCDcovanrT9IxVvNoq+nb+XriOI9oQAMwLn/dCIdjk6inp7bGYIWbD3K8AboAhiNXBZJ97SfTfUUfGWoX44Dp89T6bZeHK303HiYZgUeotMK+kwBFAGEJVrP4shh2/EbGQIrfjWPGitYxfPsmqYArmCzqRyJ1d3XlzKvMLSV7eRD/RXwae1W9Ud5X3pssc44BHDrPiuYHlCAG3nynHWWuMu7gCobUUloYfBxjm76bj/O73CPKutOBXeAZwiRVJxD++w4fJbmKIgFOL7zswN6MUwh9Ggcd9k3GxlABZpOpbmbj/C9pn5eEEF3qjaYop6L9jxpDVmYsGYv3f7RDB46AKVfyuRoM9oHQxciFMB/O2Obem9vbl8MJ1is6qqHNIhEIpHoH5T6n4gZ10gjhHyb27Zto+3btztsx3YKCQ2h8IhwioiI4GuHjxx2maCTnp5BMTGxvCoQkr8fP36cJxiZw7AwaQfrn6MclBcSEkKRkZHsv3ffXjp46KDbCTuQnvAEf6Ruwj1JSUlZJkaJ/lnlC+BcpQALkUQ9fhEA8/HYDRS415Fz67ICM4DotwpAPxqzgSORADWAjSOCeImjeW3GBVI7BYvotg7aF80RzrCj8dwNje7vL6duod5eO7ib3BTA
D+M+4YPy0V3fVQFU85HrVLm7nOMoTQEGO04M4m5oUxgj+cnvGxlAtUKOxDHoYRypKYyvRHc8orrmJCXAGoBOj1H12XSE6w5QzriYSYMWhFPjEf78bhDaZ9vBWO52R9ugXqjDkh0n+Dr8cH8b9X7txwfRsCW76KwVKdZCVHLsij3UYNha6jAhSJWxiWYHOaD0dwXVX3ps5jGpELY9FPA3GObPz/pm2jYFv+EMtfiDNMR3J32h4FpHo0UikUj0zyo5OZnWrV9Hdd+tS09WfZJTGGEpS6ydjq5wpCxC9/ebdd4ib585dPZsHO1Q0PjbyN94YtCrr71GZcuV4zGZWOrS19fXCZBINYR0SEjJhPyfKA+R0WeffZbKlCnD40AxwQi5M2NjY/keU5j13q5dO84NijqgK3+t/1pKSXENBv3rpID4ysWsM+dvFuWbMZwikUgkEon+Xl22VmJLSU2h0PAwGjpsGL319lsKQGvQTz8NpH379vJ15PWc4jGFxowdQ37LlnHi+LffeYcKFS5ML770IqdZMoETM9cxkx05QKtWrUoNGjZgmEV+TkBk+Ycr0MRJk9yufATgRPJ5vUpSxUcr0urVq/8VwHkl8xJdTkmizLgTdOl4BKXvVCC8eiJdmNOHzk/pSInjPrI8bz4JcIpEIpFIJPqfhO50LFsJwKtb9/9o1aqVOXZhYyWgRo0a0a233srRUeTUNIET66ojXycipY0bNyYvLy+eOITZ6si5ieuIsoaHh/PqRVjRCPk5PaZ6cCqlZs2acSJ41Aez43EuNTXr3IK/XVeu0JX0NLp8IZ4yow9Rxr7NlLZ5HiX7DqKkWV/T+Ynt6NyIBhT//fMU1/MxZZUorndViv+hJiUMqkXnfn3HKujmkwCnSCQSiUSivyyA5bJly+ixSo9xF/nHrT7m8ZvupCE0KiqKoRCr/7zy6qu0UAFnappjmJTuUkfyd6xgNHToUD7vTpiM5OHhwd35iH4iKorlLLFFl3rL
j1qouvnRuXPnXMaI/mUBKJEUPvUCXU44Q5eO7aL0Hcspdc0EujC/P52f9gUljm1BCT+9QXF9qimgrExxvZ6i+D7PUHz/Fyhh8JuUOKY5Jc3uRSkrxygY9aaMyA106eQ+upx4lq5k3LxDxQQ4RSKRSCQS/WUh0qhXFFqzdg1P1sEkHXfSwIlJRzt37uRxoMHbgyk6OponCkHwQRd84MZAXmkIXeTZCRCJ2e2YdITyEMnE+utYXx0rGUWfiebrf0qAyox0uoIo5emDDISpQd6UsnwkXfD5js5P7kQJw+oxQMZ/96QCyico7ruqFNfvWYr/8RU6N/Q95dOZkhf+TGnrp1J6yDLKOLSDLsUeV6Cq2uVy/pzwKsApEolEIpEo/0mB5eXkRLp4Yi+lhayiVP9JlLz4Z0qa1Y3OjWlJCYPrUMKAmhT//XMKKKtRXG8Fl72rUHzfanRuVBP2S142gtI2eVPG7kC6pMq5nBBNVy6mK6j8G6KpN5kEOEUikUgkEolE11QCnCKRSCQSiUSiayoBTpFIJBKJRCLRNZUAp0gkEolEIpHomkqAUyQSiUQikUh0TSXAKRKJRCKRSCS6phLgFIlEIpFIJBJdUwlwikQikUgkEomuqQQ4RSKRSCQSiUTXUET/D6L/emjhgWngAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="greenlinenew">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAApsAAAB7CAYAAAA7SUvPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAHMxSURBVHhe7V0FnBXV91dQyqSkEbsVC8EAxUYllLAAkbCV7hQB6UZggaW7u1m6l46lWZoFlqVLz/98z8x9e9/szHtvd9nfH/F++Rzem9tzZ97Od84959xbyMDAwMDAwMDAwCCFYMimgYGBgYGBgYFBisGQTQMDAwMDAwMDgxSDIZsGBgYGBgYGBgYpBkM2DQwMDAwMDAwMUgyGbBoYGBgYGBgYGKQYDNk0MDAwMDAwMDBIMRiyaWBgYGBgYGBgkGIwZNPAwMDAwMDAwCDFYMimgYGBgYGBgYFBisGQTQMDAwMDAwMDgxSDIZsGBgYGBgYGBgYpBkM2DQwMDAwMDAwMUgyGbBoYGBgYGBgYXAdcu3aVLly4QGfOnKHTcafp9OnES1xcHF2+fJn++ecf+pvlZoAhmwYGBgYGBgYG1wExMTE0ceIE+r3l7/TLr7/Qjz/9SD/9/FNIosrWql2L1kZG2i3eHDBk08DAwMDAwOCmw7W//6a/Wdx0g0i/du2afUSWFtGRBiAdafj0AvK3bt1K6zesp759+1DxEsUpc5bMdMsttyRNbr2F6tStS8tXrqQFCxfS5s2b6eLFi3Zv3lDn6wWMU+ZDOxd1fno9ScNcuLSFmsHmww2GbBoYGBgYGBjcVAAZOnv+PF28fDkBAUPepUuX6Oy5c3aKRdQucto5rqOIFD6vXLki5UCwnBDixp+bmAy+VbQoZcyUkdKkTSNye5rb6bbbb0uypEufTuTue+6mQq+9SvMWLKDLV6/SpcuXPIneeR47lvDdCKfMB58H8vVzQVmc8wWNzF7lfKSd47JOoPwZbucyz4vXONxgyKaBgYGBgYHBzQUmQiBGQggdpAjHSNdJly+NRYcq50ashg8fTh988AHl
vT8v3XHnHZT6ttSUKnUq+fQS5N+a6lZbgpfFZ/oM6en+fPdzP/dTsY+KUdTOHXTl6hV7FPHwOl8FnIczH99VuoJbmg63doLBkE0DAwMDAwODmwYgQ9BSXr16NSRC5FUC6c68AwcOUN16demHH36gl156idKmS+tb+gY5VGQywdK4LdB43pftPiaP+eTTrQwEbbi1lyZdGipRsgSNHTeOTp48ZY/q+sFtLoLPYHAYsmlgYGBgYGBw00DI5sWLfmQTnzi+wqJ7eENDhzRdo4mySLt0+bIsKStER0dTyz9aCmHUSaGujURapsyZ6O133qYKFSvQt5W/pUqVKtE333xD5SuUp6rVqlLDRo2oTZs21LhJY6pcuTJV/KYiVfq2knyWKVOGihQpQtmyZ5O29LaxvK76fe+D96lVq9Y0d+5ccUpygzpffDo1lzgvfSkcn5g3LNWrNAD1UM5LyxkqDNk0MDAwMDAwuKkBsnT+wgURnUCCiCENhEoBZWGvGHfmjNg5xsbGikazTp06dOdddwrB1O0rYaOJ9EcefYQef/xxKluuLC1fvtxuLXFAX3PnzGGC+g09/sTj9Mgjj4j2FP3odqCKdD777LM0bNgwOnnypB9JBEC4cW6KeCvg/KD5he2lIt6oC/tU2GrqxBIEFGl6/aTAkE0DAwMDAwODmx4gVE5CBrilAUiNOXGCho8cQc+/8LyP6CmBthH2lDlz5aTXXn+NJk2aRMeOHRNHHTdy5tW/DuSD9MGR58iRI9Lmw488TFnvy+pzQFJ9Q7B8D3vOETzGmBP+Gk7Vn1ufgdKdcEvTAXIarIwhmwYGBgYGBgYGDoD0RURECNFUdpNqORuC4xdefIH69etHK1eupLNnz9o1EwJkDB7w8PrWNavBgADvc+bOoXHjx1GVqlXonnvv8TkOyRiYbGJsTzz5hBBTjPl/CZgf4JzQbyDCacimgYGBgYGBwU2HYBo35AUqg6Vz2FaqJWssY4s20T6GlrFy
lcp06OAhu4YFt3ZxjOXoM0xIFSEM1r+Oq1ev0PARw6lgoYJCMpWtKMakiHCdunUoKirKrhEa1BggChiNM80LWGbHUr0hmwYGBgYGBgY3PUB2FOHB5yUmQFjOdiNBSFNe626kCjaQI0eOpHffe1dIpfIMx3cEbId88OEHFD4wnE6d8vcKR3vYbhIaTN94WNAXiBn6BfAJbaezf+d4QTQvXDhPGzZsoNatW9Njjz8mNqL6cjoI52uvvUaDBw+WJXgn0GbCWYgfq64RlTQ+*********************************************************+Ako4gSPtHG+YsXaMiQIeIVDo2hvmyeO3du+vnnn6l+/fo0Y8YMIaVOKAKH9oSIcT9wxEF/SFfkEkvQGKNONhUBxacbYBPaoWMHeqPwG7KkDpKpjw9L7UeOHvE7Z0Ue0Z9zJtRYE5BNToNcLxiyaWBgYGBgYPCvh49c2scgXF5EExpP2BpClEc2lrjXbVhPffr2ocJFClPatGnFJlItned7IJ94pG/cuJEOHjxIu3fvka0k169fT5GRkbR27Vo6fPiQj7wC0QcO0DpffiStXr1Gvu/fv1/yUQ7kDiQZhA8C289AWsVjx4/Jkjo0qxnuyCBjVGTzlYKvUN9+Yb72FSSMkwvZBNzmyS1Nh4zVJtGhwJBNAwMDAwMDg389QPKcWkL9WAEkCmQJy9q6BvF0XByNnzhBiCa2igR5g00kdgfKkjULfffdd7Ri5UofEVy8eLHYdNaoUYOlpmg8p0yZIppJhZmzZlHz5s2pZs2aVLNWTfqtenWqVas2jR492i5haTixreZ5Jr5oGyGXQA4D4VTsKdG+fljsQyGcWOIH6YTHepE336SOHTvSnj176MTJE3T69Omg5FERXidQB8TcSSoxPogenzQQDNk0MDAwMDAwuKkAkgQyJMvHTKJwDEIFwXekOcMT7d6zm37//XchlyCaygEnd57c
9PkXn9O06dO4TSaoNsHq1y9Mltrz5MkjOwKBkLZq3YrOnYvfc71Hjx5UqFAhypM3j2hGc/PnQw8/RA0aNLBL2GTTtueEthCxPfVlbS8I4Rw6hMllEdlDXWk38YndjSZPmUzLVyynqB07pDwII8RJOnGsyLcTamxOUom587KHdYMhmwYG/xHgbwL+MEDi/+jYmQYGBgY3Ga4ygcIyuWjgmBhB4+hGnAAQt1GjR9Grr74qsTNhCwltIYgnYmhOnTpVAq7rBLVL1y4SyF0RU3zWrl2bzpw5Y5cgcei5//7748vceotoTb//4Xu7hPV3GeND2xgbCPLfLlpGN8A5aeiwYZT/+fwybqXhhCNT+w4daNDgwbQgIkKWz3Huus2oAvqXZXzt3BTwnJDxuMxZYmDIpoHBfwDbDp2mdpM30dfdFlK5zgvok3Zz6f1Ws6jJqEiKOhzHf0jsggYGBgY3EaCxExIHMsXfITpxUqQOYY7atWsnJE15d4O8vVzgZdEenjhxQsrpANl89LFH/clmHX+yiWV2nWyiXZBN7K0OgOiBDOtED2mJwd59++jtt9+mO+64Q4gmCCc0sm+/8w699/771KhxY9q3f7/P8ciLOHr1K9pNHiN2Vbpgt6GAtkBgdc97NxiyaWBwEyPuwhUas3wftZm4gf5kaTtxI7WZsIE6T91Mnadtpt/CV1DdIatpSzS/sf9t1JwGBgY3F0CAnKIDRArL1vPnz5f9y0EKlXYQWs2PP/lYvLvd0LVrVx/ZVEvYcCByks18+fJJeyiDtkFif/jRIpvQHILI+ZFNFoxLJ3WBAIcieMdjpyH0AVKLz0yZM0tfJUqWoE2bNvnO3TkHXsDYQNRBKKEVPXv+vNiV6radyAOJDbakbsimgcFNitjzl2na2miqPWQVNRu9jrpO30JDFu2iWesP0rTIaJq36RCFzYuij9rMocELd9GJuIT2OgYGBgb/FoD4KEKpvrvBMiGKJ17Q+rVq1Yoee+wx
IYMgaxDYWEJTqbeDb2gb0DWbSSWbgIzV/q6ApWtIKIC2dtmyZfTRR8XEWUiFQ0KfGBN2ORo/frzrDkfoG8RWzYk6NwCkEiQTaSCTKkQSyiqgjiKk+jw58f9CNq/9/Q9dvvo3Xbh8jc5fukrnUljQz9VrmAh7AAYGKQjcZxevXHO9F4MJfg9X+F5NLtDG0u3H6Of+y6jnzK20ZNtRqjt0NX3XZwn1mbOdKvVaLLKI04WMjlpHm6Nj/f6IGBgYGPybgOVoECRl9wjNoJMAIU2WrTUit37DBvqsdGkfaQTRhFYTGsGZs2aK3acCSBXqA126JJ9sYnwYky4gb84xhgLsIHRftvsSxN7E3u3fff8dbd26Nb5N7hd9g0xiaVxIJPcNQqnmDHOJcbiRSKS5pXvhf0428RDcdeQMzVh3kAYt3EW9Zm+jHjN12eo4hmwPQRKW726nj1i6lzYdiBUCYGCQ0gBhnLwmmrpP1+/J+PsyXpx526jvvChatw+2QUknffwngPYePyMks/6wNbR8x3Gav+kwNR21lsau2Ce/vyb8vXz3hRS55wR1nLKZ6g9fTat3x8iLoIGBgcG/EaGQTZAqLJvrBHLT5s3iba5II8gaPMubNW8m+SoeJ5a60eb1IJvKQejQoUO0Y8cO2h4VRbt27aK9e/fSzp07JVxRYtE3rB8VLFRI7E4V2VTjynt/Xpo1a5aEVVKaSswNyCYE54Z0nWwGAsolxnHof0Y2obWZv+UItRq/nr7ptYiKtZlDhZtNp4INp1ABn0zWRE93SANb/NIddTn/pQaTRb7mh+rUtdF0lsdgYJDSiD13meoMWU0v8z34Mt9/BSC4Lz3vWavcq42nUdU+S0UjmRzA9hIayzpDV9GABVG0/8Q5Cpu3Q34HP/ZbTi3Hrae2kzbSOCaeO4V4RlLLsUazaWBg8O+GWsqF6EvlOkA2sWuQIptYWh43bhwVLVrUj2zed9994kkOgIihvCJX6u+k1zJ6XByc
Li0ShjacZBNa02rfVZPdgNq3by91IAiH1KxpUwm/hEDxicWOHbuoeo0asrOQsttU48p6X1aaPn26jA1kXGk45Xz0ebOPcc66HakTINwgnOo8g+F/QjaPxJ6nkUt2U/kei+jx6uMo07dD6c7yQyjd14Mo7ZcsXw0MXb7URD92KXP7F+EirzeZRmOW7aWzFw3ZNEh5nDhzib7qFkHpvhpEab6w70d1X+r3qS1p5F4dSPdVGUHVB66knYfj7JaSBqweTFi1n6r0XkKzNhykY3EXqdHItVSVj78PW0ZlOi+g/vOj6OjpC7Rh30kp13/BDjp8KuF+ugYGBgY3E5RmU9kiwgu9cePGvhBGIGepU6cWgtipYyepA2opGlMWZmSSBrg6CNWtI1pJEDHAjWxiX3NsK7llyxYqXLgwPfDAAxJ789FHH6Unn3qSChUsSNOmTZP6iUWPnj2FWCq7UzWuHDlzyPaaOHcAhNMLyIMjkK79dUIt+QtJtdMCIUXJJgZw6twlGhSxk97+fQZlrDSMbi07gG4p3Y+lvyVltM+kiN6OUz7rR7eyvMZkc9KqaLp42SyjG6Q8Yphsluk8n+9B7V4voz5dhNNv5bIP/DyGwhfsFHvm5AD1Ry3bwy93C2n2xkO0L+Yc1Ry0krpN30qj+aXrByacQxft4nFelHJV+yyhhVuPGjMTAwODmx4gSBeZRGEZGd+x3WSJkiXpjjvv9Nk6wsmmJKfNmD7DruUOV81miGSzcpXKtGb1GsqRI4fUU4L8rFmz0siRI6V+YhEeHk7ZsmVLSDa5H2g2z58/b5f0BsimWl4PBpDNKzyPwTScKUo24ZgzafV+eu+PWaLVkQcryKZT8FB2S3dKqOWU8EM8VekB9M7vM2nexsMBmbyBwfXC8biLVKLtXH7ZAZl0uS+dwvdohq8H04etZ9OyHcftVpIOaDZhNvLtX4tpwPwdtDTqGP05cSPN4d8AtJldpm+h1hM2iN00bDp7ztpGB0+e
11/YDQwMDG4aqOVhfakYgJZz1apV9Fz+54SQgZwhPmXOnDlD0iwm1RtdNJtVqtDaNWspb968vr4VOcycJTONGDHCbsEdcj72OekYMGCAK9nMniM7TZw40dUW1DkvbpB8FvAovU8Qdux8BBMD51h0pBjZvHrtH9pyIFaW6O6uOMSbaDrF1vRAK5lAPrUlWBoEbXB6Kn7gI7QLnCQMDP4XiD5xjoq2mG7dlyGRzf50X9XhVGfIKtoXkzA0RWLx99//0MZ9J6np6Eiq1ncpdZuxlUYs3c1pp+jwqfPiOFShxyJqNX6D5OF3mlxtqoGBgcGNChAhBCQXO0T7O9Kg3QTZfPa5Z/3IZq5cuWj27Nl2bW9cb7KJfAR8z507N40dM9ZuwR3wIIfmEWRPhxfZhMMTts6E85Gy11QAiYQNJubFCyCiWFaPO3tW5o2ZpaQrkhqIaAIpRjbhJNF3znax0bQ0PCGQTX4wp/96EOX4bgQ99MsYeuTXsfSwUzg9YZp+PIYe/GU05f5+FJPcoZT683Aq1X4urdt70h6ZgUHKAi82cAoSsul2n0MUCcUnvxw9yPd1n7lR182uGL+/6ZEHqBYTWHia95u/g6avO0gzWTpN3SzL6liy38AE1CyfGxgY3MyQeJFMppTdJYgVNHSKbDo1m7BvhBYQcBIzHSlBNuFJnj17dho6dKjdgjtAljE2pzayf//+4tzkJJvYOx1L91hKP3nSnw+BKIKIKy97N6AM+gThdBLcUJBiZHPrwdOyNR4cH4RsOh+2TuGHLshhoUZTqVqfpdR45FpqOioy0YKQLg2Gr6FfwldQqQ7z6KX6k+nXActp55HkOV0YGIQCaBWhRXyixnhLw+52r0M0sgl7zefrTaKZ6w/arSQfeMnEkvm0dQeYaEbRoIhdNHzxHhq5dA+NWb5XiOj2Q3Gy5G5gYGDwX4TXMjrI5thx46QMHGqg+fM5CGlI6g5CymYTZDNPnjy+vhU5vDfjvTQgPNxu
IXGAzSbsM9GOEvQJjSk0uPB+h3YzEERbyRJMW5kYpAjZPH/5Go1ftY+erTOBbikVwlIi56cqF04v1JtM7SZtopU7Y2jPsTO09/hZ2pdI2XvsLO1iYrl+70mxW+s2fQuNXrZH7OgMDFIaWI5GeK9c34+0TDk87neflB4gHuvYpzwyBbTv2MzgwInz8puK2HKE1uyOoUOnzhv7ZQMDg5sWIIUXLlygM2fPCPGDQ5Ba6tUJlBfZRBD0qVOnShls0QitH5aPYZuo10+uZjNybaS1bzrn6WQzY6aMNHz4cLuFxGHIkCH0QL4HKG3atD6yqSRDhgz0448/0ko+ZzUX0O4qUWQa3xGPMxZhkvg75k4nn7KkznOBsFGwAY05cULmxqll1XH9ySaPBQ+3+kNXU7YqwwMvJSrhh/JdFYfSzwOW0w5bA4lzSo5Yhqz/yBIh5BoSDQxSGAjoXrHnQrqzwmAmkxrZdL5wIc+WzJWGim3lbn7BSing7pffhnVoYGBgcNNi0+ZNVL5CedkrHM42tWrXosOHD8sysW7nGIhsYrkZUKRMkVUdXbu4hz7St4X0JJt26KOCBQuK1jFV6lvjyWZmJpsjQiSb9vgUPMnmbbdR+nTp6bfffpN90gGQ4jmzZ4t9akREBK1ZvVragsc6dhuaO3cuzZs3j44zmTzHadDuAkeOHKZq1apKuKjHHn+MPv/iC1qydKmQesA5T8B1J5sgeYjd90nb2RLj0vfAdT5sdWGyCXvLLtO20KmzKbs/MzRPsGc7GnuBjp0OTeIuXKarGmNHG6fPX5bQMYhheOw0xL/OST4PlNOnHG2cuXBF4jBadRLWS55clLA7cdwHNFqh4BITccwHNL8pNaaTPKYLlxLu5HDp6jWZJ/d6CQXLwgildeUa/7j4ReLsxSvWNQhpzFa5GD7POL52KbV8fJLHg7iuotXU73m3+98mm/l+Gi3e4cnRvuN85L52OffjcRfkvCHoA8dIPxp7XubvMl8H
XBmsSFj3ZsI2vAR94qUO1/YMXw/rPnIv6y/29eD+8JsI9X4F8DcGkS5O+e5bt/aTI9ZcYW953KMJ/2waGBjcaMDfIBDEJk2b0vsfvC+EDeQPkjtPbglvVLHiN9SuHZaRd0kdL7IJByFFNgOhXfv2Eh9TJ5t169alc3YsS+DPP/+0yKZdBuGVEFrp28rf0ob1G+iJJ5/kfi0SinyQRCyDjx492m7BG9C4whZVX94fGD5QvOlvv/32eKLJgnNLlzadkE2QXAC7FX3yycf04osv0gc8Z02bNRVSvXv3bmrYqCEVLFSQKlSoQMeOHxcNJjzkv/7qa/r0s08p631Z5JwgWPZ//Y3XaeDgQXT0mPumJNedbOKP86wNh+jZ2hMsmzU8ZJU4H7bqgcvl3mg6TWzJoBlKKYDrIMTLyKW7CVv3NRsdSc3HeAvysdvKhFX7hBTxI1UeQKt2xdDAiJ0STqb5mHXU3NFOM247bG6UeCWrfi9d+Zu2HYqVJf2OUzbR7+O4Hupq9YIK+lGi+tX6bsFp2BkGdnmbD8TKrk1eADk5wURj+Y5jMlYs/aJ+yGPS+vWsw2V+57yu07eIPeKR2ItMKqxHN/7fdug0hc2L4nL2ubi1YQuuRZMRayl8wQ4xr8BS8MTV++hPPl+pH2jcaJvL4PzaMKkbsnAXbdh/MkXutU3Rp+iJ38ZZ5iNu97suNtl8rs5EGrJ4lxCopOIIvzyN4OuOc202eq37PCiR+YikRjyfXadtFg94ELh1e09Q7znbrfsAZdzqiqyT6/H72PU0ZNEu8XAHycW9/Qf/Xlyvhd2nr137erTH/cr14BGPF59gwAscVk4QP7TXrG30B9+3fm3rfbj170x3EXWf9ON7c2nUUTrJLziGcBoY3NgA4frl118oY6ZMQoDgaPNhsQ/FThGaQ0WM3i76Ni1btkzqBCKbKvQRynihQ8eOfsHg8flJ8U9kCR7B4rdv3y7bUur7lYNU3nX3XaLZXLt6LeXO
ldvXN/IV2Rw1apTdizeUU49ONt0chJTN5jPPPkMdOnSgffv2SVloL6H5Rf+Yr7Llykpbkesi6d333pV0BJnft2+v7HZUvXp1SYNgy82XCxSQctgKE2m//PYr7dxlEXknrjvZhLYBW+Pd/90ouqVkmP1QtcX5sJU8i2yW7bxA4gHqGsSUAAjOT/2WUbaqIyg7S47vRnrKfZz/4M9jqO7Q1bTjyBnaznURDPvn/svptSZTJQg3POct0epVGU4l2s2VnWBwNjv4cywTaTguwTbv8epjKff3IyknyleLryff9WOnqHwR7tPv2GoPXvyIK9pwxBqawQQPmhonoPnEQxQhcOCM9XLDKZT3x1H2eBxzgrb1Y5WmpzvraOm5+POpWuNl1xrs/b1xv+X9jId3xJajct3zfj866LVAfuZvh1E5Lo+57MOkqET7ueLFnVP6D1Bfm598P4+WbVJrD15Fo5icYXer6wWQxfEr91PeH/je/9S+9wOJkM0BVKT5DFqw5YjdStIQxffYj3xf5+HrmM3reiix5wPzWbTFDInUALI5LTJa7ts8PP7sgdrgvOxVh9P93BcCx09ZEy3EDPF0834/isu41LX7FLHbwPVAxIl3/5gpvw20A60rXs7cAEKN2KDtJm+iz7suoPx1J8p969e23ocuzjyvciw5efy4T17h3wUC3g/m3/yeYyDk9kAMDAxuOIAkFS9R3EcsH3zoQZo/fz5Vr1HdR4ZAvKD1XL58udQJSDZtzaayRVQOQvoK3dixY+m999+j9Hek95E6bEVZ5M0i1LlzZ/qNyRkIXtp0aX3ED17hL770oiyvr1612tVBCDabQ4J4o3shrF+YkFu9PbSPNhs0bCBEW2leQYbz5LX6Rz5MD3COGzZsoBIlS0g65mbTpo108OABsUdVY339jTdo0ODBNGv2bCpdurSYCdRvUJ/LuTu6XleyiWsAB526Q9dQVn6Q+ew1dbKJB6zjgXtr2f5UPXxFitqsKRw8
dZ5+6r+Mbsd4RPNqaZdchcd/7zdDqRYTkwWbjxC0MQ/wQ+hW5AWqW7o/ffznHDrNxBtkr+XY9fxgHsN10KeVr8r5vrsde4kqp8o66/G44XSCIOHT1x3wEXj8RC5cvkqLtx2lzzvPtwLt4xr5teM4L71ddazElxZkHlGW+8n47VDRpsH5C4CG+MuuEXTH14P9x+Em6KNUGL3HZB2k6mEm+reUtc00VL5ePpDg2nFfIFWDF+28bnvmYwkaGuKslYdbfej3uZvwPXjb5wOpVPt5ohFNDvbHnJWXoLtgKxrk3hThMrfy57stZ9G2g7HSxuLtR+iTtnNk+0xfG6q8Xxuc91kYpeNyH/05W2LpPvIrXw++Pr46evlgwn2l4vl4vs5EJuv76Dzfo06c5hckePm/0mgKpf2crzv6CtSPnuf87hSVp4uWD0IMLarZ7tbA4MYFSFLJUiWF2GE3oGLFigmR/OWXXyhb9mxCkkCIoIlzajYDxdkEuYQG8fSZMwkchIDJkydLfdTzLYXzGGCXieVyaAyRpogflpunz5gu7SxZvITy5HYnm/0HDLB7SBx69nLfrhJB3adMmeJzXkL/gcgmiDvSC7xSQEIlnTp1imrVqiVp0NKq0ExoA3ONdJBRaHTdcF3JJmy31u07KdqqO74eZP2xdj5gdbFJ6F0Vh1CbCevFTiqlAU1W7cErKd1X/EANNj4mQPf/NJrJ6XIhqI/yAzV1OU531rPPQwkI6m8DV4gmpsXYdfGeyaqMXjclBH3wAzxH1RHUkMkdljgB2DpG7j0h2ishFKXtsr56GKN9bskdq6qvPbyxVemL9SfRxFX7+E3xH9ocHUuVmajcw/PlFx7LawycDq0ZtFkSUivQ+PS6jjb0a/F83Ymyf/j1AMwmsCf6HeWZ8Kl7RI1DH4NK508Q8F8GrBDNWXIA8xAEhc9SmV/yfOdnf/r1izQWvrfv4nF+33cpRduB5FfvjqEyneYzieTfbrBwZdwuYtji
WmCV4Fb8LlR/+HSU9Tt2itQB8Q6n15tMl0gSOqB1HbVsN+WvM8G+7ty+ml+XdnzfnfluoubDqzzn4XqW5N/Mih3utkgGBgb//wBJKvVpKSF3mTJnoi+/+ooiI9fRpEmT6M+2fwpR+u3XX6lb165ikwjA6zoyMlK0dCCiiiximbtylSq0fMUKKQfNpgoC7wSIa5E33xSyBUEbEDm2vcxVHuSFl16gBQsWSN0li7zJJhx9koJ+/fu5ajYRzmnWrFl+TjwByWZJi2w+//zztH3bNjp08KDYeyINZPOVgq9Q+w7taSmfP+ZgzLixtHHjRs/tMK8r2YQdIJZGX2k4VUIZJXjoOIXzQUAe/m0sDViwQxwFUhoggCCCaaAdUeNzPmjUMT/QQBSL/j6DHuEx4mFoPZg8zsuuh8DybSZuoNkbD9PTNW3bVY3gJOhP1YWgHMpD0+cU9YB1G7fzO7dx99eDqUKPhUzqLK0ZbN0ajVxrRQkQLZRex+ucMB4Wt/FgnHobwYTr3PftMFkCh0MI7CYxPtlhyo08KNH6wDVIje/OsShxa8drjDz+DNwertX1uPeiDp+m55gQ4Z72u0Zu/XM+tHkwA+g4ZbM4pSQHILrV+b7OWImJu+8+sUXvVwlff7yMtJ+8kU7YTnnLoo6LljXdV9qLouvYrU+cJ8hfKpwr7ie5BuozwPVwE7TJ99l9VUaIDbFuv7meX2BBgtOCBKM9r3NCuspDOc/fEYuzrpvgvPiFLFXZcHq61nixETYwMLgxoZPNezNmpDJly4rWEl7o0LZFRUXR5k2baNfOnb5lZJDII0eOULt27eiZZy3tJsjZbbenFlvMrt26STkFkE3sea5rN3ft2iW79tSrV0+W7tGGU3Lmzklfff0V/cpkF+GSVJzLQJrNQYMHSRm1RzkEGlaErYOdJsbi1LIC3Xt0l92CnGQTms0ZM2b4yGAwsqmW0fPnz0+bNm+m48ePyw5E8L6H5hZ5cI6qULECn1NX
nx2oF64r2YTHMWzW7v9xtEVQ3P6A68IPBBCHIs2m07S1B+Shh0DT8zcfphnrD4hTSXIFy8hYNpa9n3mMcC6p/Ndiuk0eWvYYnQ8vdcz5WJaE/ZqPaOrlnML18AB+tfEUie9Ze8hqSltuoPUgtvN9oteDYDwsWP7OXHmY9KkLlmaxu5JF8FzG7fzObd3FZLNMx/m0eleM2Eku2HKYCvHYVL5/HZdz4zI4nzsrDJHtFPXxwC7wHiaJqfFS4aznJTwP9/I5dJ2xhX80fzO5OSZ2l3JeGI9bHYg9TiFxpfrRbXx8L5MqsbvVxpOF50iIkvNc9PPUhfu8ncdUpc9i2nU0+UH/V+w4LnaQco3c+tOF+76N5+6lBpPFMQ6e9cnBnqNnZC/09E6Nvde5lwyjR38ewwRqvzj1QXsIe8h3Ws7ke5CvqWoD9fU29GN88vzhPDLxecu10K4JtKw+TaSqH0i4z7v4XgNpVtp4/C2F01KuH0bG9636dxPkcTu4L6ExV2OJl5GiqZR7KZhg3NKXFTEAjncGBgY3JhTZVEvYxT4qRkuWLAno4KMA8le+fHkhUCBn0NxBO/p7y9/tEhZA8hACCCQVAAHUd92BNzc8uJ9+5mmxdYSAkNWoWYOOHPW3y4eGMWLhQtmaUmlAFTnMxMSvf7i1jI54lxAs42P3HhDOM0yWL3HfahwA+M3atWuparWqopm1SLMlOC+YEsDDPlSyqZbRYV+6h4kkznU9p3fv3l1META/yFdz1rx5cyH0XriuZBNhTHrO2kYZsSzq5Y2rPyyYkN5eNpy+6raQInefoC0HY2XZGeTzuboT6YX6k/ylXghS1/7k8nAgeJkf5lgqXLj1qDhwYBn50w7zKBU0HG4PwQRExX7gqDHLd6Q5xUrHciLs2LozoXqh3kTZHcaXr7erC+dBE5y1ynB6s/k0+rb3Yok5ai3fWwJHnreaT6cMsG+UNuPrJmgPUrof3c0P1c+7RtDaPSfEe7v7zK1C
BkT75FZHid0+ltof/W0clWRC+H3YMvpJG9OPLOW6LBCNjxBx55K8ry17fvCd74nMTFB78D0CLfgMfhGAo1UqWYLV6ngJt5Oh/CAhaOV7LKIf+/nPEZw5ivAcgawHJBOqLxA+JoalOsyVYOfJAWxhhy/ZHf9C4OzTKdx3WiZ177eeJRrFxIT+cQPCjZVsP8+yRVZE0U2QDynRl38rE2W7SgDkHxEWYOYgLxBoQ5WVelqbdhrmGFrpVxtPpW962fesfU3wvTLfxwUbTqF7vxkSX1erb33X2+0v8/dltwjZoAHYd/wcfce/X8vswx6TKu8hd5YfIuYRMGlw/o5+4WOYkcBBz+8e0c/VkY5yWNnAS4GBgcGNCZ1sgmx9/MnHtHTpUoqLixNtJEgWxEnSAHhlf/nVlz7iBLIJ7SDIIwBiposXnOXcRAE7E82YOdOdbDKRCznOpo3LTEJ/+PEHqas835XgvO7Pd78soyuyiTkIlWzu2r2bLkGry8Qd9aKj91O5cmWljho3yjZp0uR/4yCEZcS6Q1fxH3vbScH5hxui/1HnMmm/GEgNR66hQ7HnmRAeoRJt59I9FYeKhg9aKn9xSfvSeWyVwZLb7fz9iRrjJBzQ4dgLEk9w6tr9QmZvleU0exyJFXUOCaQ/pWLiBTLbedqWeC2X/kB1a4cfopkqDaXvwpbKLjIIEQTb0nhBTMQLNHl1NJPRGb6+5FNvS2+b+83I81iNCdi2g6eFcEKjK844Qe3x+DyYrCIkz1+zt8tWn9A0YRxqPFj2hZ1htxlbxUbV73qr89LbhHC/8EAeMH8nXbhyTcLmgMze8mmQ8UBwviXDhCgs2W5pquPHc17mB8fYhvGzjvOsoOo66XIbE+enZoEz1womfMkBwv/gRQn3bUhafZ4vaCG/5Bet5NprgrjPWn9Q7g2cj99566LOX8rgpWgO7Y+xlpPwIoawReJJr8o650s/5uuRnn9nFXsuouU7j4sXud/14Pvl0MkL
NGn1fnq35Uy6TY+56yWcj9/tpx3n++YE9rSIHgD7UNcx6cL10zFZ/aDVLJqwcp+Mx7pvrTFBEJdzN7TATI6tNu0xebXNabfxNcULDrb+NDAwuDGhyKay2fz66/K0fv0GWSKHxzi0jSBOP/70I23YuMGuZQFxJ8uUKeMjm5DU9lI64k1euoxY0SwXL/oRxsTgPNcFWVP1QXoXRrhrNhGOaNx4a7vMxODX336lezPdK+3pRBPnDfJ6+MgROoOdkHgsFy9eCl2zuTfhizYchrp06SLe9igHwVyluIMQnIMQugjaLp8WwuUPd7xwPhM+aEGhDYWnJzxR8zPBkTy3P/yhiO/hwQ8eHscXXSJkuRbLhIiRCXvBp2qOt2y3QukDZUCkSoWJ1uWZWuPlYVam0wI519L8YCzWZrZoUUFw8AAu03k+1R6yiu6W5WGNhDkFbfNDH5qTZ2pPoN5zoujsJUvlj/vRJ5JCtHDLUSHKVj2X+dWFzw/2kfWHrZEHLmKfFm42jW6F1krNkZdw22n58+vuEeI0AjIDOMcDO0eEvBFNNuZT1cf41Nzqn9wv4q+O4+uMMbXjl4CccJ7SiaqX8JjwEvE7EzpoEQF9jgBcY1zrL7ousF549DnSx6S1aWk259GaXSesRpKIrYdOU/nui8QO00c2nf0pQTr3DdMImFocT6a9JjYYQPxRvBykwjk7r686dzUePmfMDzTVKpA8wmHBCQ7OekHvD+TzfY2lc1x/X7QDl+uxcMsR+X3Icrp+PdyE28Vv7IuuEbTPJsG9Z28TpzDRQnrNpxI+r0yVhomjFDT5gD4mNS5sDIAXOyGbIY1pMBVjYo6/bwYGBjcm/MlmZvq6fAXZKQfk8p577/ERosJvFqYlS5fYtSyAIDVv0VxCJCnCh7J33JlBApgj7NEVFiwlox9lOwlA04d8kEg4yCBQuu5IBFIJgoelbyzpK7KJ706bTUUQER6pfv36tGjRIrGzhCB2JzS1GKvTNAD9
gSB+9tlnotVVjk7qnIsUKSKOUDABwPab8KrHuLZt20a5cueSMops4nw2btros9kE2dweFUXRB6LFLAHxR2fOnClmAOgTBBcOSSjbuHHjlNdsItjy5DXRsqTm6hykP+zsP+IgWggrgriJIIK9Z28X728hHwnKOz69BO3agpiTnaZu9nm5I+xR01GR1lKyrn1ytinH8e3AXhFL4/WGrpYQKGNX7BVbULEJjTwgJBl2ZYjHiSU/EE0QGAnREuxhxvmwkUOYIminrgYI5jeN+8J8SZsQda5u7fIcQksF5xPEPh26aDc9zPMRErHjubnry0GE4NwghV6ANg873/jC7bi1pYTHeTvfF4jFCJvcLQdOU/WBK4WwhOSwwecLx6thi9wDxgJ44QGp/qD1LEoL20WvuVHCbd7OAu0ctL/JAbR7BRpO5j75XHBt3PqD4N7CCwZ/It5ql2mbheglBwf4OrQct05spSUsl9tvRE/jFwNomDtrfUN7jVioni+KfoL8/vREjfE0b9Nhqe8GvKQg8gA2bPDTIvq1ox1zv3hhg3kEtJL4JTQcviZ45AElXB+xP/HyinvBC7v4XEFosQoRyj0CzT1MNPBCYWBgcGNCkU0V+ujDYsVo5cqVEohcae9ARBHoXcXZVMvpIF7YmlEtpYOsYSkapO+jjz+mbdu3i1YTL9DoR9lOAjjGMj3awq4/CxcuFCKmSCWckbCUD6Kq+gOuXPYmm4gVCnvPj7nv999/X+TNN98UB6N5PE60ibZUH1gab9asmWwdeVua+DBLIIEgnwgJBbKJpXuMHeMFYQWJfODBB6R/kE04+6Bd2H7CDAHpIJubt26VJfhff/2F3nzrTbHZhEf9iRMnxNv/1ddeFW1qw4YNU55sYjeW/vN30AM/MaHBw8r5cMCxnsZlQD7eaoGA1ofFcafxqLWUEeSjhObRChKD9rDk7WzDS7g8NCHQqIDAwR4NQFB2xASEhkzaVOX1NtV3PIS4b4Rw+rTD
fJrERBpLjoilCI0edufBOUNwjHScw5o9J6jHzK30XJ1Jtr2m3Z6XcB8ga9X6LpFdfwJhYMQOsVn0jV2IhXYeunC/0BIPWriLH9wXxZQgO+I/6hpIL+H2EZgeEQKUVtMN8HKHhgga5KBkk9vMwAS20l+Lxbs4YqsV0N0vTJCbqPPj9t/+PXDwc4wVAbihZbbqau24CfebhtvFywGWWJMDvAhYLzEB5kFdL+73diY62NZyHL+4KE1tUgGijL3VxTbS637QpWRfeqL6OJq2NtoXYB/z+jaWu0MhYCyI0fpRmzli9uEF/Da6zdhCT9YcZ4dG0tpQc6G1iXmBN33LsevkNwVTDbwIWGW1cm7C+XjJhY02lu69gL/NETjX32eGrOXPxi+biLGb3IgBBgYGKQeQPpAq7GwD4gPiNX7iBKr2XTXZrlInm8uWW3E2QbwUQJJatWrlC10EwobycPBp1Lgx7d5jOQhCoylhkED2+BjkDH2D+CE255o1a+gCNIdM6KDxxD7pIIcoo0ORTVlGd5BNiCK8SlDmtddeozGjx0isTLSvNJzwFMd53Xn3nXLuqIuxf1b6MyHQ7dq3o6NHj0p51IOAcCIE1Kvc5q2pUon296uvvhICOWXyFNGGos+XXn5JdgVCPNF33n1H0iC9+/aV5fjNmzdTiRIlZIwIGp/iZBN7LLeasMFaUoXWMNjDgR/Kd/ADCw8TBLReuydGnGCywoP1c802k8ketC2WttSlHTfhBwRsxH4LX+GnscLWjB+3ncPtMCFwe8igfb2PUv34QTleCMzJc/EeZ4GAh3f/+VHipBKq7V4mnrMGw61g59jWEt7BaAeCEDAQBIdvDptAPrdQHpCYsw+ZbE+PPEhbD8ZKwO97g2kg1UOdP0EQsK0nzBtA1tV4INBig2zPWndQTApcY486hfvNyP1ji8MDJ8/T6GV76fWm02xy41JeibomPJd4UcBuTF64wGNqP3kT5f5+lPfc633xmNPzJ7Tf
lwOQ6mAAWew7N4ruxUtMiGQzA18f2PYu33E8oBYuFKzYeVyIX6hE8ZbifalQ46m0nX8b+AMpzkH8YgGHoaDOWvb1QCSC3wYsl3vWC1itqDl4JZM1/i3o43JrH2mf9hfCPphfkKBBwHlhx62Qzonr4+8F5mHupsP8APhH7lP/+xZLYX9TP34pfqqmClHlaMcpfK3y/Thari/aMzAwuDEB4ojg4iBZIEPQ1P1Wozq9zQRJbckIQgTCtHTZUqkDbaMCbBCHDx8u5AqEFWVB2uDZXvTtohSxaKFvGR1/N7F0DTKJv1UKCLUEO0gQOZBSlId2FYK9xXWAbC5etFgCyGNsiuAqwXGq1NYYICgDAghNIsir8oLHEj2IoBq3lLVtQBs1bkQdOnWgiZMmypisv/fWFpcgzNiCEtrIHDlzUKrbUtEbhd+QOJ01atSgRx99VPrM/3x+OnToEK2LXEflypWTNLRf7fvvaMiwYdSpcyd6ucDLko6l/xS12fyH/8k2kPzwuVMeuCE8HD5l8lF+sCzVwnln3qZDVGPQKirffaF4j/9ie45itxh4QyN4tPtSnCbqwcEPCHjJtp+y0WeThofNhFX75SErhCDYA4zz8TAq3WE+k7XTfjdUIGDJGnum3ymOIi7EA2PUhcvcXWGIaPmgBerHRBV2cNgzPAzfbYF947t/MLEDqVPn6SVc5q6KQ4WcrdwVQ4u2HZGg1OmZ4CDPtY4Su23Ef8QyNx6yvvHYAg02bF9/5esDU4UEGlzMrYiWxueZnQn4oAgrnirCHz0Mk4Bg52K3feunYaLxCrTfO0hszcGrKDNiTYbidMT3KcLzwIs8OThw6hw1GLGG7sD9qebX7f5S84JrXn6QePhjX/LkALfl5LUHZGvFBHPuJnb/8FyPtV+goIFsPGKt/MYSlrfF7xia72HUZdoWud+9EB1zTuyaYcfsG5vb+FQ6Xw9sD7tw21Gpj+sCO1TXOi4CbSt+39BCQiuP3w1+Q0r6
LeB7l9PxWxPnPZc2Egj3jTFgZcPAwODGxbkLF6hxkyb0XP78lOHOO4Rs5ciVU76DMMIb+9VXX6XatWuLbSWgL2uDiCF0T6NGjWTnH0UAQV4RWxJaO2gtVZ0rTNjEdpMJJ4glyOeatWtpz549dJKJa+S6dbJ03a9/fxozZgwdPXrMKmtrOKFlRDB0kDos/adLn57JYgZPwXiwnD5jRnz4IizPjxs3jooXLy6EGrsYoRzO/ZlnnqFhw4fRrNmzZCwKOE+cAzgNzAfWcR60v4jDieV7aIRBPnHeSPvpp5+kn5jjMRL26MUXXxTzgpxMkh9/4glZhsf8IsZo7969RTPqhutCNqEtQDD3UkwKxU4RDxa3P9y6MNnMYS/Vnrt4lTbtPyV/0GEDFrnnJG3cf1I0nmv2xFCPWVtF02ZpXRxtOx9Edv5D/NCCRhLEBkBYpu7Tt9Jj8H4GCXQbo94Wl8n07VCqN2y1aG1DAegoNG8IsZIOY5V+tDbdhPPhNZ/vpzH0coNJTBomU4EGtuC7Lc/WniihkYTYubWjC88tyrYYs552Hj1D41bso5frT7KcV0K5Nix3fzNUbPJ841BjYgGxwXIlyKIvFJNeH8dKfMf96UluD8vNJ89ekp2NsFQvc6TXdYrdTpovB8m94gX8cOBx/xW/rIhzkE421Th0QRoTw5d4XuZsPGS3kjTALOBzJlVwqgpK5iFcBjFC8aIVdz40jbkXTl+wnN4erz7e6tvrXNV3LgPThV/DV4gHOgATEIQfc3UOQl29vuT3p9xMTBGj00spi2TEd8U+/YjDGfC+s/vA6sVrTabRBv5bALQct56y4B4J8Z5N/flAuo/nFb8VbGtZoBHfr0rs39Er/D33dyMJ24S6teGUtEyUP2w9RxzPDAwMblxAc7d69WpZCgf5gqbv4UceFjL01ltvSqzLsLAwioiIoCNHj7oqkLD8jS0dCxQo4CNuIF3wbn/yqSfFAQae6zpAHqEphGZ17759
FHv6tASSB8GEUw8+Fy9ZSqdiY2WMKAvCh3poq+I339A7771H77z7Lr3Ln17yRuHCsh2kIrzQbk6cOFH2Jb/zzjuFGEMbi73Zn3/hebHh3LJ1Kx08dIhiYgI7wM5fMJ8qV6ksDlL5HsgnxBHni9ijcFLCuK9cvSJkvD+TZyybP8FEE+UhKIt90TE2lHXDdSGb2Mt41NI9VJD/wMvuLvrDyUv4oYewRFMjo2UZEQ/dY3EX6fwlqKjthhkgogimjKUsqef2MNSPuV2xh2s6jWasPygaTQDLffWGrbHs6kJ5KJcKo8eqj6Xec7bRmRADbsO5Z/F2LNXPpdvRhlc/DoEGFVrb278Il6VQn+DYTkO+LPuFIkw2H2CyDY3k4djz9NfsbZbWCuNxK+8iePD7jcVFxMEC5YPNJfeL8cM5aMm2Y+KMAq2r7BwUAtnEDi4P/jyapgTQLl299g9NWRstdocJYjLi0zlGvo+wN3jZTvNpXQC7w1AAhycQcNlJJxgxsscB8ww4xOk75SQF2BO96chIa0tU6d/Rn9anCN8b+C11nb7Ftyy8i19IYMMYUgB2zk/9+QB6ts4EWeb2As4LjnN4KQnpJYfHCOegCj0W+ZbmYYcqtp5u5V0ENpi4J3Gv+X47uiCdRWw1Xer7iT1ncGCDBhr23gYGBjc+sJMNSB529cE+4dixZ9SoUaLNVBpB8S637R11gCiBACKkDxxjQOAg0BSCeD7yyCMSvBxLxXC2UVpNEEgsmYNEAtDuwYMc3uNHj1nRcJADTSj6QDkI9hyfM3cujR03jkYzKR0zdiyNZcGnEuThc8TIkbRo8WKxz8SS/OQpk8UhClpGLGvDzhNjfPbZZ6hNm9biaa5rbgHp1/6uA1rZFStXUNt27ahHzx7UrXt36t6jB82eM0fycX6qLZSdNnUa9enbR3Yrwvzic9v2ba5zqnBdyCZCr+Dh9SA8yYM9VCClLTKD3UoQTsT99C1AC4ZYjneVZ2LiRpac
D9fPrPh/8DZdtTuGbyprgtbtPUFfd4uwNXEhjLFEX/Ginb0xnrAGAx7e41fulWU3yysY4tK2m/jKB5AEdTTR03ieXqw3SRwlxAN/dKTYhYJo+NUPJtK2yzh0cZbXj5UwocQ1gac+NNZY1v/4z7mUAcurbtdUF+4DBOG9VjMDkhtcI5A3hJCyHD/0NrTvvjTsXtVflr+x/J5U4G/L6OV7KOd3QZyDlOB8mNTBMW7S6mghyckB9hGv1HOxRdy95l8XfomClhsxI9E3fh8RW4/Qi/VDJ4X4DX3cZk5AAhZ3/gp1mrZZ4qgGtY1EHvcLTXfLsetlFSL23CUqAfvqkkE2IHCK3Zar4F6D4LtbXV3sOnn4Ja3NhI0BozIYGBjcGIAdomjh+BOC7+oYxFCRwUBAudjYWOrbt6/s/w0CByInhJNJHbR+derWoV5//UXr1q/37TWuA/2AlAXrU5XDGBGOCKLGjTBF6liFT0J52Jb27NmT3nvvPQk8DzKsnIow1nKfl5Mg9dDS6kBdzA/acyOh6APOTOgb5wTNKfrHOaBv5X0PqDHrZS9f8d/G04nrQjbhydtw+GrKCG/YYOQBUtoKaA3t1pYD1pKZF7AsjUDxIByh2oLeU2EI1R++RjQk6tTnbzos2puQHsiQEn2odMd5Eqg+VAcOLEtiSTNLFYTzwcOK21EPtlAecKGKr01HOoT7RWBxOErA63brgViqzPMMr/qQyFByxW1MfE0QPqb9lE205/hZscV7ueFk65q6zYveBs7ny4ESDgfBuL0Ac4kmo9ZKSJ+g9yD6RLv8vf+8HUxU/X94iQG0+tiZKcOXuD9DMAn41Iolid1tQJ4D/DZDAsxO8NIm9sxufTqlZF/6qPUsua/5T4zMG2J0PlZ9XHCyad/PMNGoOWhlQJJ+hMkZ4naKqYTznkhwzH2y5Pt5tNwb5y5doZU7Y+g12FcH2+0qmDj7krQg9wdExjRATEmGLNrtMzkwMDC4cQENHMhR
YgDiBRLmrAenmD59+lCBVwoIicMSNQTL6vBuf+HFF6lylSqylO1GOAFoPSE6CYOWE/1BE6oTOJTRSaCzHhB94IDsT45l8nsz3usjwSCcotV87lnROIL8OYG2QGCx3B/qHGE8KA8BwYRgjpOC60I24e1cqddiSu/lFOOUUkw+Kg6h1uM30KEADyxoXpZEHRUnA3kQ6iRCHnz2d12Y2GSrPEx2vkHwZgBLeniIPQ9nA6d2z60NCJ/HT2HLxIkiVD4ALWyLMeus7f5CIcYst5a1lvZgtxlUeH5F3PIgnIdlR+wRDnIGG8YFmw8TQkBhp6agY7IfwrKEHqgfpwQZE+YYMT+haYXDFsIwSTxVCWcVZEx8zbFLVMepgeNRHjp1jqr2WSwvGgnuQbdrzGl3VxwqobGSA4TDQhDx29Am+vW6n2zyArKJPbtRJ7k7BwEwX3m2Ft/XId5v0Gx+02uRjzxBY9do5FoJnC7jC3Q97HPDtes1a6vPwcgN2Gv+Qya1nja9fsdWn0/XmsAk09q6c9DCnWLGElQbb4853hQl/r7zE+1+9Dv2ECy74yUH5jjzNnmH2zIwMLhxABLl1NopgGwhH6KTOBxDqwci5QR2zunYqZM4zaildEXsYNOJOJawmRw5ciRt3rIlgTZRNIncrl9/PL6LTNigNRT7TTsd43YbO5bM5y9YQLPnzqE/27WlgoUKWVrMVJYjEAgwPrFDUseOHWnHjh0JSCqANAnJxOcaKtmEuYEQTf7E2KDJTAxZ1ZFssoml4wWbj0hQ8jSwrwqqVWLhBx4cJEYt2yOesF6w7L4QImeqvRQXwgO1dD/Zx3jCqn2+5W8QnA6TN9FDiAHq9fBSD0B8MgmCJu6P8ev5pghtUnEhtzPprtpnqfXgDzYPLFjuvbfSUHnIFmo0JbA0dnxX4sh/qf5k2YYQnrjQCgvJrjfRXYsoD2r92FqyRnBwLKv6tR+K6OPS6sIJB1tIbtofK9eievgK2ZI0lDlC
GXjrY/tBL8BWNpKJNaIWyNK8G/HSz5U/8UKA3YxW7Uz6nuh4Q12167jsJiX2ms7z0edWHfP9l73aCOoM8pxM5yBoJdFOPrURgt6Xm3D/CCXWaOQauwWi3cfO0OfYcQlhsZzjdYpN6mBvOmPdQf4j6q0RXsHzClOOoM5BENx3TO6wrz32RMfvHgTYFz5MH5fbGLl+Bj6vR38dK7az8Ej33ZM4VvemStNFT9e+w8EIRLPGwJWyOmBgYPDvAp7JOuUCWYIGUsIXaaQO5UA03Yge0uFkA+cibF0Jr3G1ZK3IJ+JTvvXWW+IJj9BHMTExshwtbXLbbkD/yNfHoQNjwlgRG3PevHlUvEQJeue9d+nJp58Ukou+FdGEYBegNn+2kd2LAkH6ZKKI9kMB5sSnfeU6qKtMEsQONcR2gGSTTTzwRizdI8uiIcVbRD4/cB+vMU6caQKN9ezFK/TXrG0ShkceWCE8DEGWsC3joq1H+EJajWPJEDE3ZUnPa3yqbXzyg/tJHt/AiJ0hx9a7cvWahG+CNkf6CPaA5TLY670gP+AQezJ8wU7R5qDP5AjCEo1ZvlfOGcucHaZsFucRV7KOc1WCYz5vEDuYN/Sauc21/cQKlmgxpomr9osmDAQY5EaceEIgSCDkuP6BvIHhVDZpzX7xZMZuTK7XWD9PzP2Xg6hc5wX2cnLSAA0cnJbg5ezqHKT604+5DMgaXgJCfZHxAjYRQEB6BB33vK914f6x6QJMPRQ27D8py9WyDO8cr1N47CDpcATcEu1NwC5cuSpbkmK3q5BeErlf2JzCOQirA3DIw2qGZXsbfE4RoeGBn0dTTSaGuN989x//pkS0+9FVXMqhHezdH8Ev0oHCOxkYGNx4EJIEUsSfimKAHIkjD4sXyXMCZAo2kiB8LVq0oNffeF3C/IDoKdIJTSdCJWFZvW69euJcBIckEL/EagDRH7SHMTHHxcGoQ4cOEnpIQjHZTkC6gOxCu4qdf7DFJM45pSFj
5H4wl272n15INtlEWKBuM7eINtGy+bIfAs7vSvihiGVaeA1vtEOceAFasOajI5kADfZ+mKIP1c9n/emurwdLrE60rZg3grmX7jQ/4W41buND2qdh9E7LGUIeQ3XggIZ2yMJd9CyW6tEHloid7er9MeFGrL9fwpcnO9aiF7C/dC0mI7ARlDE5x6B/h5QMozzfjZRtOJPruOIGtDmfH97v/gHPZw9SqETG2l9Cz7zXalZA7RJILLYofBx2h3jhcSMo+rlyvwjz03RMJB2NTbrjBwLwI4C/2Ik6+3AT7hckGzaWszccDKgZDAWrd5+gL7pFWCGLAs0lxJ5PFaVBYfaGQxIVQuUHPAfOx71UvO0c2WTAC4gqgZccBNcPSjbtfvP+OJpajdsgcVSxtC/xcIv3cZSzv+vCdREBo2iLGSY8kYGBgQAESLffBLkEQXIGYVcAT0AexAm0BS927NozYeIEKl6iuC8sklpWB/HLcMcdlC17NiF/CLvUrHkzGjFihMTBRPgjfM6YPl1CCUVGrqNNmzbTqlWrafKkyZIPwXaXQ4YOoc6dO9F771sOQFnvy+rTYqpP9AtHJQRhr1u3Dq1YsUKWx5MCOferVoD6UIDy6EuZAUjdEDSmySab2B8bdopYGvQ9FPSHgVM+s7aArNRrEe084u3wASCYeuXeiyktHlZuS6MQ9RCCYHmeCVyTUZESUBqAdnPq2mjR3oS0fSQe2iX7UoUeC63dakLkXCA87SdvtLRM6MONbOrHTGjz/TCK2k7cSEdOpYynKzyVv2biLQ98nJdzDAnGhB2TxtGGfYFfApIKEHJob7Gsj/A5rmNSgnS+5vd8M0TilgZ0RmHCiLBWorlWdZ3t6cL3IIg+dki6eMXbjCMYoHmHpzJenoL2CeH5hea4Ys+FsuyftJ2DsJSBgLwk93Xh5tPEBtGvf3x3jgdzzQKNIWysAewOBS1nvp9Gcfkg18KWHEysaw1aGcRe8wz9yNcMcWqt+oHa
tpbmEUXAMqu5JrsqQfuL36FeLkE7+M73CCIdIM7p5gDaVgMDg/8OQBB1px8QKbX7jw6QI6RBoKULphkE4Rw/YTx9UvwTKlq0KD388MMS1xKEU5FACJyIYOeJIPLQhmKbycKFC8te54hd+dtv1Zkk1qMffviR3nnnHXrt9dd8UrBQQdkXXUIa2e2BZEKg3UR/cFqqV78ejRo1krZs2czk73L8snYAwucGzBXsMHWyqs+LG5CuzADwHdpYHKco2RQHiaGrxcYxIHlQwg9caBir9llC+44n9JhSgL3liMW76WWEZJF2bXFrE4J+S4bRA9+PpHAmNCqYO9pBMHDYc8Exya+8Xl+loa8SfajB8NV0MRF7Vh8/c1G85v2Ihz5e59iZGEMTh7EhdNT1BrRms9djWX+21R/OS+8fgnH6pD+lKtufijSbxiQ7ZWIKYjkSLyZ5fhiZsH99TOo7EwkQyC7TtviupxvgpY6wVjBL8DtPZ9tKeO5zVBkhDlRJBX5S0Ej/OmCF3NOWs5PWh1u//IKBOK/NRq+jQ0l8wcB+vCCqcC4CUcQOTr7fnVuf8rvhT5Rhkl1z0CqfcxBIet1hq2WLWARq99VXbSnR0kACe83eFtA7G/P6bstZsqOPr77erjqWNNx3A+i1JlNpze4Totkczr/7+xEXFmRTjV+vowTpfF54ccALCexPDQwMDECCQC5BgEC+QKhge6gvn4MYgYRif2+lmZPyLkA5eHIDsMecNn2abBtZsWJF0WaCEKayCSG0nCCfiijqcnuaNJQxY0bKmzcvPfTQQ7JTj1s5CMgr2lJEU5FZ2GcituX69ev9CCIIo9MZKRRgblSYIwXMBdqDBAP6E5MFrpOiZBN2Y9UHrrB2bQmmzYPwwwFLqM/VnUg/9Vsu9op/jIsX7Bry+9h1VGvwSlnKhmYrvh1+8OATx862ccwk4qka42nWhkM8gdZJYym+FbeZsyoTHMTsU3Wd9ZXw+LBU2H2G/y4BwYCH
f5Xei2UM0o48JO3x6seqXyYeuZkY43wRVzC5wE4y2NFl4qpoOsHEF2Rg2OJdYl8nfYJsqHGoMcmx9p3JyDO1xotm6Xpg5+E4Gr1sDy3YcoR/rH/LkvW3fy2mDLhX9GugvutzhmMmcbA3ReD/QIAH8/t/zAxuM4y2OR8abkQmSI75AnbNWrL9GJVqP8+65upc9L6caTy/sE38oNVsqj98LbUav8Hv3k8g49dTC/4twFmm0Yi11HD4Gqo9eBV1mLyZFm87KsT9vsrD5V7yzaHeH0SNgyUtk3Esb6u/B9DcY9cv7CnuV1e1pQRp9ry92ngqTUeMzgBaWQS5f7LGhPj+E7Srp1l21oiYEBNn2Wu2mbhRIipY5+Uyj1LPFh4XVkpgf3s9NJvYPx1hl2ADjJ2hLvGxgYHBvwuKHIIEeREgIUlMzs7YwdkDAeXimGTqhAwIHxguS9nYKhL7kmN5XdlTuokijiCjIrfFx8j0ErSJtnPmyilL9Njb3ekIhHM5y+d7KcD5ekHMBPi89PBNmD+0pwj29UCyySa8R2syMbyrosObVT0M9O/6QwYPko/7WNoLLJHzg1jkU/5egtM+7m3lgTzo7fraQ7rdHo6lXH/xaF21y/IwxpRvjj4le61nlP2yuX3fWFj09uST2+B2sKXl6GWBvbp0QH2NB1O5zvOtMau29D70NAj3A03cO3/MoKFMClfvjhGNED4TI2tYQLYGLIii91vNksDpME/AEm/PWVvpKSaP6rx8Y9DHpAuTpnvLD6GmoyIlZBLaduszmOA8FjLBbDBsjdhndp+xVTRWIDfQtPqW9VW/MieYG1tUGpeBIxk0eV4AqQahxVafrvaauiCP27zj68FUptP8gHaHwYD4mnDygce13Ldufblefztd7nlNcN870zBHILLF/qJbPmL5sBd/9pawSbD5/GnAcmtFAf17nbfdH+YcIYuGLo7fBx7X6bnaLnuPO48hPB5oIHH9sLWsF0BCsU1sLhXv
NMF1huhpfD0qDKZvei4WswKEK0MMz7v0EFZu56bGyO1jJ6gnqo+jTlM3S3QBaEjd7stAsobnAn83xi3fS1V7L5HtO/HydiERqxsGBgY3DkC6ghEv5IZCzlDCt0ytAQQNW1NOnjyJXnrpRXEeAolUhFKRSy/xKoN0RTbhHPTSyy/TrNmzJdj88ePWHutOhHK+XnCrm5z23JBssnno1HlqPiaSslWDvZz1YHN9KPiO/R8ceAgiNqRPtDwReVi6PGyUqPb5YQjy9jU/JLbYziTQps3hh/IHTMLSYUlPf/A5xe7n1tL9qEiz6RIEPlTgAbty5zEq1W6upT11a98pPG6cK7Q6sDkDAUJMQp+UD01gkgBNLOJiIjA3lpxPn79CsWcvS1icRxGrEHPkvA4eAg/gNDwejEHG5OjPVfRyqh4LyAIcN2B7C00gPLcRUkausRqPjM3lGnM6bBHhTLON63sB+3ojXusDTKSsdl2usZ7G9wC0ZnWZsMUmw3whluf4z4kb4yMc6Oej+tJFzjNeEtz3HnILxNcGAqoPo3Er9sp+7gj1JFpJr/tajYU/Yd4BLfcs/j0ojF+539rm0o0sO4X7wL1atssCsdP2ArTXTRC26NthMl7fGAIItlLFlptQluJ6/thvGd9LHiGsPATe9AjrhN8DxO/+DEFUHWyIcCcT3e/6LhMTIQMDg5sQAUhUqARLL3f+3DnZc33WrFlUtWpVypIlC6VNm1a0kk7xaS3TWFpLN4FdZrly5ahfv340cdJEmjd/Pp1koumH60wGUxrJJpuwwwubt4OeqjnB9kYP/QERktgPZsThk4evnZagHD8wseSOuHh77J1mEK8vbN52CcESykMPD9RU/IAr1WGeaEdCBS44NJvYZ/uW4rZmU4nXAxdpKl19D0Xc6oJslAqj91vNliVrAHagPWdu5esy3iZDIVwX1ab6rkS3R9TTnWn6d+4TNns/MXHA0uj5y9dkWRik0K+8m9j5d5YfQl90
XeDbK9sNyIPTi3jcO9t064fHBa0bdjOCtjWpOHDinGiRoVXza/96ij52vi/TMKH6oPUs0Uj2nbtddreR34TzHJ3C1w9krFjr2bTCNpFAFAnsuJQRTjyhkE3uA7E4v++7RExTvLCBfweyiYBzad5NOB+/bdiBYktaAKGPag5aQXchAoVaiXDWU+KWh7SQRftd4Nj+neDvRWf7pc3AwODmwt/XLIcWX/xIG3iOw2YTjkWhkDgs0bs5xUSujaQWzVtQ7dq1qVatWiK169Sm6tWri41nqVIl6cMPP6CPPipGZcuWpUrfVqLffvtVytesVVPKN2jYgBZELLBbtIDx6vuv4xiCZfDkAO2p4POJAepAQu0/2WQTWj0EcC7daQGlhXMMlsedD4CkCP/hT102XPYmRtBz8bh1K6eEH0yZKw+XJeADJy2NRDQTAizJyRI6HqjqIeNWH8J9puJyX3VfmGj7L5gTwElBiJkbAXH2q0io15ic6XpZla6+g4h8OYh+DV9Be21iBscoBLbH/u6WA4vLmIIJxqiIpt6n+vRLtx7Uvrolw+hhfmgPitgpWs29MWepZPt5tm2vfe6qrFPsdu+pOISq/LXYF1nACSxrYAegAg0d2lKtDb80SOl+lPeHkdRr9na6wHOUFFy6+jfN2Whvf+qzA3acvxJn/6GKsx6/TNz91UBqyYR9475TVGvwKuu+DuW62mTzU36JWrf3pJzDxuiTfG9MFU1eSG3weDJxfzWYCEL76AXsEy9OS25tOIXbxHV7vu5EGjB/h9THC0CvOdspKzSjzhe3YCLXQYl9Tn5ptvjSHdeMf/uII1qszRyx+05uaCoDA4MbDyCJsEd0Or8opyLYLwYjm+JUw22gPMirQrB62AJz1cqVNGf2bJo/bx5t2rRJlsVDAdrW27/AfUOC2ZsGg5vNJuDszwmc/zmuB5IarCyQbLIJnDx7mfrOjZLg1hKsG+QGtmbJEf7DDw3U510j6MtuEXQbkwSx
5XQrCyneh+5iIlN78Erab+9CApu6Qo2nWd7sqAtiAHGrD0EZbqdE+7miPUoM4C2NYNDP15skD6wEbTv7DTaWUAVt8Lixi0yvWdv8PNthTmAF/R5hkW2v+l7jSMz49Hbw+XEfawn80GmZG5AJ8TCW/ADXUQmXgSNZ2c7emk04Zf02cIW1ZOs2Vrc0nqvsVYZTu8lJ12xapiPrrHn9BHbH3G5I52SLW14wYeJ1P5PkcSv30rDFu8V7GyYfAdtTeSXC5CUK9rNwfoF5Cexo74Q2mPMS1HMTbgtL9tX6LvXUbCKKQWV+OUiLGKqBfqu68MspNIndpm+VlwfYbWKMxdvOlet/S8lE/C3R5zfQvHgJjxnmH9X5BXXn0Tj7rAwMDG4mgBSBYDk1coowBSNNIHdnzp51DaUEwEP87LlzPk0hgsirsiC6iNkJj3Z4gIPgOZ2YoC1FfWgNFeCwA0cmkEI1bjmPEMYbDNIOt6m3g7EqMukFVUc8+rkctMWBxnJdyCasZw+eOCe7/bzVYgaTxFFiFwcSYMlQylyJxXfsLVkqW/Lgz2MkODviMsImL3vV4bKntJSrlLAe8vL8MEo0mQiFg8DS8OKFHWNGzsuEOlLPMQ6tLWiKsBT/aYf5tCwqcR7ZmOP9MWeFxGAbRBCRLFW0fpzicg6+dK88F1Fj/qzjfFlC13c8ghPLwq1HxAMc85mlMp87z61bO9dF7LFjTOgHjiyXodVksohQV3BQwbJtJre6miA+I7RoiN0KsrnLLR4rzzeckN75faaEE8oYwpyhXzjU4J74Y9yGgHutewHXefOBU1SJ5xSadLQn563mNcT7PIEEGD+2NM3y7XDZFhMe8A1HrBHHKdzTweYSkvGbYZSVx/pRmzm0gu/r7YfiqHyPRWJvKtfKpY4u6APlcD2+C1tGx067k82p9m5K+O2jT7e2dFFjf77uJOowZRNdsTcSOHvhKo1ftY/e5ZcVjDEri9TBHCmx6yZZHG3gfsN8
4mURO17FBogjamBg8O+CkDxbC5cY6OQpEJEKBNGWOrSGNxJAGhMbZ1OHKuskrE5cH7LJwHI67NjgxTxy6R4aFLFD7Mr+mr1NSKgIvgcRaEixdePYFftEw4FdcOAt2nvONvGulnIubfXivL5zttPcjYfEoxXOH/DYxRh6zeR6+hg8vkMziD7Gr9xH0ScS7/KP5WJo4aatjRbtUzjPQW8eE+ISqnEGFTUee0zBRI0ZDiOww3P+OKDpjNx7UjRiIO7YM7036qr2E9FXQFHtsGBMuI4rd1mEHV7fcGrpo+6HEAXjBOmAHZ8T15iY7Dh0WrYU/Gu2fV+EIBgbYpsu3n7Ut3d+YoDpxX0OT2W5H9V5qz7074kRt3p22+rexjWOPnFWTAcwvziXBHV0Ufn8ibkcw9dgP48dAfIRQB2/qVDvzV6YYy6P3aXcNMKYFyzv95tnjyvY2DSBqcWyHcfkLR0QRyG+5hH8MoHVicGcH4Z7R7WbiLZdxaU+5hif+Nu180ic/JYNDAz+nQDxgWZQkSVoC0H6oIVDmrK3VDh58iRNnTaV2rRpQ506daLevXvTnj17JA/lobUD1m9YT73+6kUdOnagtm3b0oIFCywNJbc9YcIE6ta9G7Xv0J7atWtLXbt1pR07dshzeHtUFPXo1Yvacnrbdu2ozZ9/Upu2f0pfHTt1lLa69+hBkyZPpk2bN1Hfvn2lLPI6duxI7dq3p1atW1OLlr9Tv/796fARyzcDZQcOGkjtOR9jguAcwsLCaPfu+MgjTmBMIN6iccVc8fnpmlQ1f5inYMDfbTXX/xOyCWAJDKFCsGwKzQDID7QgiRHYg4Eoog1sB4gtDvFws/Iv+JX1lwt0nD9BrrBMCA0fxhC4jptcEHKDeHtJAXZ3OXfpimjNcB5Yckz8GBIpsRfoLPenB6xVwI0AG0PsFoO5wUP8fzGmGO5DhY2BSUNS7gUIrgWupxOIo4odiY7HXeJyiTsXnD+uD0hNYoHfEkgq
xpXi11UTzKf1m7gmL1NuZbzFGid+Wxf5N4Xxo72E5QIJ2rjA95D1x8kJ3GfQSLrXDSzq3PTLgT9aCGuFdPR54qx73esn1hzBJOgyz1ESbg0DA4P/ZygNG4ikaDKZBOkESEgWp2EZHPkKGzZsoBIlSvgCqt999900mYkfgPIq3iTIpOxTbpf75ZdfJLj6nDlzKP9z+X3pSuD0c+DAARo3fhzlyZsnQb4u92a8lz7/4gvqE9aXMmXO5FoGgnZWrV4t4wEpffDhBxOUyZkjJ7Vs2ZLWrVtHO3fupGgeA3YZUsAcgWhiaR7fncD8IQ8SDJhzt7l24rqSzRsJ5mFxYyDAvWdwE8FcZgMDg/9vwNYRJCoQ6QEpQjm1tA2ytWbtWipdpowQNYQeypUrF02fPl3ydUDjmT17domDibLNmjWjsL5hVKRwEUqbLq2ENLrr7rsoY6aMlC59OinTvEVz2ev8+efzSxzO9JyO7SxVCCT0h8+HH3mYqlarRuGDBtL9+e6XukhHWYRCgiDtxZdepKioKBnPoEGD6NnnnpXdhdAvSCo+EasT7b5RuDAVefNNqlO3Lu3dt891XpDmtP1UQfExT2rFyQmkO+sFwk1LNg0MDAwMDAz+OwBxDEZ+sHwszjaaHeWWLVvoiy+/8JFN7NYTEtls3ox69epFL738khBD7CTUokULGjt2DL373rvSVtYsWemtN9+iqlWr0M8//0RlmdQ+/thjlOb2NJKfOUsWKlbsI6rfoAH15LbCBw6kvPfnlfZBWB986EGpW63ad1ShQgVp/4i9jI4l9CeefEJIbHMey8yZM6hW7ZqiJQUBFS3srbfQe++/R6tXr3bVYiINNpsg6QqYQaRfunxZPPfd5hTzJ1t9MjENBYZsGhgYGBgYGPwnABIFwonlX4Xt27fTV1995SObIWs2QTb/6kUFXnlZ6qVKnZp69Owp7X1W+jMpAylUsBBNmTyFNm7cSMOGDaN333mXUt2aSrSWjz72
KA1kgrl//37asnWr5CuyCRJZtGhRWrt2jdh/rl0bSZuZGF+wTQCwXeZjjz9G99x7D7cRTheYGPbv34+yZcsm2k0QTbTz+huv07Jly1zJJogk5kO3YVVAedhjupFNaIhRz61NNxiyaWBgYGBgYHDTAORI2W96QZUBNm3eTJ9/8bkQs8SQzebNmzO57EHPv/C8Re44rWy5srLzT82aNal48eJU+I3C9EfLP+jUSWub38jISPrsU4uIYuk9//P5KXJdpOSBRI4ePdpHNu+66y766KOP6MSJEzJeLGvD3lSNOzw8nB5/4nG6+567qVu3rhJSCWkYo9r6Eu0UebMILV++PGRimBSo+XYjpoAhmwYGBgYGBgY3DVTsRy+nFaRhiRh2iSBIa5Nhs9k3rC8VLlJYiCPSsXwNu8ohQ4eItvLUqVNCApXtIzSMpUqWkvpp0qWh5/I/R0uWLpW848eP06hRo3xkM0OG9PQCE1l4l0PjCYEHPNoDoNkE2YSdJrzQY2JiqH///ilCNp3ziGOVgu+YbyzFe/VxXcgmuvT+h3wl6jjQP71ssH96m8Hq6fl6ebd05z+3NLd/qr4q76yn512vf6rNUNoNVibUdoL9S047qq6qH0o7evlg/9zKqrRQ23D752wj1PacZQLVC7XNYP/c2klK26p8Uup6/dPbCdRusP7c6qq0YHVRwsDA4N8KkB9o2pwESQHpIEWKGG0WzWa8zWaoZLNOnTpi7xmxMILy588vhBPpcOZ56umn6Icff5Q902NPnxYihiXppUwsdbL5bP5nhQgCp7mcTjYxFmgtH3n0EVkuz5s3L338yce+sExwEFKaze7du9GZM3E0YMAAypbdWka/XmQTEUhAJvVYnDiGzabSsqaYZvMqNxp35QIduxhLhy+eCiDIV6KOnWV0CZbvJs4+3ETP18u7pev1EiNu7Tnz9ePrIc4+A0mwMqG2E0yS046qq+qH0o5ePph4lQu1fiBxjiOUNp1lnG04
JZQ2QxG3fvXjUETVuV5jguht4btX28H6dKur0oLVPUXHL8XRuWuXDPE0MPgXAoQHNohCrjzIj46tW7fSl19+6SN4oZJNLJUjtBFIYr269Sh3ntySDoHDEDzFhw8fLvkgbNC0Ll2mkc20TDa5zPIVFtmExlInm2gDmlLVJgTEc9u2bVJ+0GCLbN7DZPOvv3pJzM/wAeEhk02/edKg0pUdJ/KFLNvxRgE4FSVmu8wkk80Tl85QxLHNNGTvAhqwex6F79Flri1u6fp3p+hldXErC1F5zrL6d2e+l7jV0+s705SoOl7iVtZZL1g7ehtOCbV8Yso4j3UJVD5Yup7nLONMV3nqUxc9Xz/WRS/vVsYrT0/3ErfyzjT9WKU5xVlGl0Dlnce6eJXVxS1Ppenilu6so5fR85yiygQq58z3Kq/S3fKUOPP1Onqe83gu/z2bSyP3L6Y1J3fT+ave+8AbGBjcWAChA1HCJ5bJQZa8NG06QN6+/CrxZLNGzRoUvT9a8hBvs0qVKvRSgZckjBE8wiEgpKvXrJGtKTEev2V0m2yuWLFC2nCSTYwFbUBLinIPPfwQlShZwqfZhDe60mz26NGdzpw546nZRB9upBLbaToJI8ph/iCAzCmX0WMsw1QB4hUayYkkkU287c88HEnvL2hBOcZ/SzknVqZcE6uw6J+aTLBF8nTRyogEy9O/28e+tu1033dbnMcJ0uw6kqZ995XR87U033dbfGWQ58xXaSx+5fQyTtHztXpO8SuvRE/X8p31nMeeZdzadksLJHY5addZ13mslU8gznKa6ONOkKbqsfjKuLSToA1VRqvrKna+Ko803zFLgvIq3+XTl6/EbsN37MzzSPf1ode3PwP2ob47ReXp9fTyznSVpz5t8ewboqfr4iiD+fa1o9LUsfbpKS717fQcEypRvknf0bcru9P2uIP2Xz0DA4MbHdC+iac5kyLlcR4K2RTNpoNszpgxw86Nh5Ns1qpVS4gqNH5w7pk3
fx4NHzGcWvzegl548QUph+Xv1m1a08FDhzzJppdmM32G9OJABI93aDF79+ktAeLj4uKkPLzYlc1mx44d6PTpWCGbCWw2ixThfpcnIJWYGyztO0kojpHu5YmeFCSJbF7++yr13zOX8kyqSrcMep9uGfwhSzFNcKxEP1bf9XLqu5sEy4fYbQ9RZe1jv7rOY6dw3iBnvrOO81hPC5Snp+l5Xsd6vUDpuqgyTtHyh7AM0vJwjE85b7c2VLpXvhKvcm7p+nc3ceY7vwc6dhG5J1hw3up8Rdza0QRz4lfeTVR5lzz0m+B+8hLVjirvPE4JcbaflP5UHbd6IbYnc6yV8ztWbSixyyTI80r3yguWxjLoAx7LR1Rodn1aEmMtVxkYGNz4gCbuCgimfRwMilTB7tIZ+mjmzJlSRtfmOclmgwYNpBzIYKNGjcTRCIANKDSQarehEiVLSlgjaAKxnB0q2QSJhEc7lscBJ/HTHYT+/PNPij0V6+qN/kbhNyhi4UJxiALhdJJLN6AvkGMQ+OtBOJNENq/8fY0mHFhJb85tQneO/oLSjipD6UaVtaWc9TnSFnWsRE/3faKMU+zyrunad6mrl1Xf1bEz3004T8biqKOP1bUNlaaJOhe/uqqsXkelsyQo5xRVxv6u5/ml63nO7yx+82yn+8aplfPlO0XP18r46utl7HS/9iGqniqnjt3KuBzr7fk+9bZ0cebbxyL2sbM937FbeV2c6XpZFtWm77eh1/WoA/H17xRV3tlGKOJsw3GcoD9nHV0c+WqufOO2v7vWdYpdTq/rq6el+9rDd/1vjRJHexC/NrX28F3yHOVFrLJpuY+s4yrSV8s60bpYa7nKwMDgxofbjjbQcEJUuiKYsJ/Ed+RZZDNes5kxY0b68aefqE/fvtS9R3eaOHGi1Fee3opsNm3aVNLe/+B9ypU7F1WuUlnCGG3ZukXibGa4Iz0TyrSyM9DevXuljcSQTWg24a2OMUBj2advH9Fu9gvr
J7E8kY76cEzCdpvt2rWjr8t/LXE3hWjacTYLvVqIFkQsSBTZBFAWTkGByCbyMJ8oF8hsIck2m9HnY6jztslUfFFremNuQyo8t5FDkOaWfqOKy3jn2KKn/c/kfz13Xv2lxDhCaTPUfv/X85QYwdhu5PE5JZTxXu/zsdtTv7Wgv7cQ+0/i7/YNfM5pTF8v6yx2m2euxu+fbGBg8O+Dsj1UBAsECs4tyrMa5AjL6GoHIZA0kMAHHnyAnn72GYmhWblyZQlhhGVrBExXTjsIfdSzV08pg2NsU9mECeiwEcOp6DtF6dbUt9JTTz9NXbp0lfoAyGbJEiWlPIjtM9wH0gBFNvPksfZRh4MQiOPTzzwtpPOZ554Rcln4jSI0dsxY0WZK30wqc+TIIbadcFJSZBi2m7D5LPVpKYnvCVIYCkAXMV+YK4iCSsOc4TvIvcwnz2Ucjz1FQh9d/ecaxV055++NfsEWzavzXy032/ncCPL/MZ/mGoYm/9/zdD37T2Zb4o1+9SL9bbzRDQz+1RDNpq3FBEmC9k2W2/lTAXaXX31tL6OnvV1IoCKU+A4Hm8OHD9PQoUMpZ86cQgKRB80m0hDIPet9WSUNW1eW+7yc7AwEx51vv/1Wwh9hDMDKlSupTJkydNttt0mIJNh2rlhpOQhhuXzMmDH04IMP+vpWS+FKMK68ee+nAf0HCNl8peArTC5v9RHM226/XXYewnjgYV/xm4rUsVNHOnr0KJi19IN5UBpI9V1PUw5Wuvc58nAOilAq7TBMA/A9xcimgYGBgYGBgcG/AYosKeKpA1tB/vDjD6JFxHK4kuw5souGs3SZ0nTs+DEhgtBE5siZgzJnyUR/tGpFi5csEY0nCCS0iiCI0DRiaRtbTY4aPcqn1QTBhVc4vNZz5col5d8q+pZsRwmcjouTPqCtxD7r+lggOXJiPA/SRx99TJMnT6W+ffvK8rnKx3ihBS1WrBhV+64aLV68WLbIRHB5BUUO1ZI3CCW0
k0hXhBjfsSc6REHKcr6EO9LIqJpLfBdzBUM2DQwMDAwMDP6LUMRKX1JXOHbsGI0cOZJq164tjj5KGjRsQE2bNaN+/fvLNpGR69ZRq9atqWHDhlS3bl0JjwSNIWTWrFnUokULKlCggGgf06VPJyR1+ozpss0klu4XLV5Ef7ZtSx9//ImER3rwoQfpvfffk7BJwJq1a6lGzZpCNsuVKyft+Y2nQQNq1rw59Q0LEzvTFStXUv8BA6hxkyaSX69+PerSpYs4LcGmEx7yIJX6UjgIoWh2bdINbSTKKI0voNIhOjCHYhdrf4coOI+dMGTTwMDAwMDA4KYGiBBIF7RyTlIEEoplciynR0VF+QSEDVrP6OgDQsYQcmjXrl2SjrIgqUoziMDtBw8elHBIWB7HUnu+B/KJE09sbKxIyz9aUoFXClCWrFllqRvay4KFCtI6JrHQGg4aPJgefewx0Xh27dqVDh867D6eAwfEvlP1iTTkY0zR+/fT2TNn7DNLCBBJjBefak7wXewv+RNAuqTZx9cDhmwaGBgYGBgYGCQBIGYgogrdu3cXRyFl89mwUUNZRj958iR9W/lbyQMZheMOCCmW5REy6fy589S+fXupg1BGWE6/mWDIpoGBgYGBgYHBdUCnTp2ESCqHHSxtHzp8SLSgsKOER3nxEsXpo48/EucfeJcL2Tx/njp37ixpsB1FcPibCYZsGhgYGBgYGBhcBzjJJpbJER5p/vz5Ejj+3ffepV69evm0mCCfhmwaGBgYGBgYGBiEBJ1sgjhiF6EPPvxAHHjefPNN+rDYhxIkftCgQRbZVMvohmwaGBgYGBgYGBgEg1OzCcmcNbN4mMPxB5pNeLEPGzbMkE0DAwMDAwMDA4PEAWQTZBHOP/A2x3eJvWkTz8JFCtO0adMkGLwhmwYGBgYGBgYGBokCyCbCGmH5vFHjRvT5F59TnrzW9pMI9v5G4Tdo6tSphmz+dzCdqtlvGiLVptvpwE7q8qqW92oX
TnFgerX4fJ+8Sl0SFFRw9KfEr1/A0benVOMWQ4Fbv6HWBUIZjzpvR1n73HZ2eVUrG1hQJbHlvfoNfI1xCbW8kK+xR1lBqNfYDSHcczYSPz8e41IS0vgMDAwMDIIBW0OCaIJwhoeHU/Xq1YVsgkDWrFlTdvWJiYmR3X/w91cnmyCqIJvY5tKQzZsFfkTCQRJ3dqFXfXm30Kt6piPPTZzPbj9S4yZ6hRDaFwmFIHiRJUioBCOU8Shi5ChrdREqeYaABCe2vFe/DOf5+xE4fwKW2GvsJIOJusZu8DqHBEjC/AS6D5SEej8YGBgYGHiibbu2EmMTy+gDwgfQL7/8QpmzZBGy2b9/f9+WkIjHib+9jzz6CK1evVrIZrt27SQtTdo0NGTIECl3s+A/SzYDabX8NUcaEXU8tOMJircGza8tX7q3Fsuz70RDH1NCUuZHrgLAfzyBNaKhlE1Me0By2kxIALX6Xi8bDtIXP09Okhffll//IVxjNyR2XhRCqec/D6qM9/kYGBgYGCQNo0ePpvc/eF880KdMnUJdu3Wjz0p/RqVKlZLlc2whCUyaPIkKFy5Mlb6tRFE7oujSpUtSt+jbRanYR8Vo7ry5Uu5mwX+UbAbQanmShEB1HA/0EOq4l08cQQkInTTZ7cT3GSqxcIwnoPYrlLKJaQ9ITpuOFwBbVHZirxeg6vgPI7HX2A2JnReFUOo55kErk1SCa2BgYPBfgdq60SB5+G+STS+tFuCh2QpMHDwe+o62dHESGYGjfMicwxXuZCtRBDYx4wmlbGLPLzlteiwdW/PuThBDJ4caHP3r4nqN3ZDYeVEIpZ5jHvQySTpfAwMDg/8IQDSx7H3x0iU7JXFQe40nBxjDzYD/JNn0e8gGFJuIOh7qCUmEl3bLQUJdRH/4+2uaPCRUUpCAbCV+ST7oeLTB+8+pu5YssZq0pC8RO9KrVYu/Phiz28uG5zV2J+3xp564a+yGpGoYkzM/Cc4r2CANDAwM/oO4
dPkSbY/aTl27dZV9zps1b+YnzZs3pyZNmlC9enXF0efy5ct2TaKoHTto8ODBUq5d+3a0Zs0aunjxouRdu3aN9u7dS23atKHGjRv7tdm0aVNq1boVzZ4zW8o6AQI6b948cSiq36A+/dn2T4qKivLZg96I+A+STQ+Nn5vYD2D/h7oLaQukKWUEJLc+8hictECCassCaNqkL19+MFKTGBIVCnHx0P56IpTyXv36pyM5/how8fQjolYdz2vsqiFNOHehXWM3JHZeFBI/P373TgCNp4GBgYGBBdhSzps/jx597BG/v5mQ1Lenlhia8D5/6OGHadKkSXSVSeS1v68J8WzSpCm99NJLUgYe6f369aPTp09LuyClCxcupPuy3SdtqSDw+FQxOmvUrCFlncCyfrNmzehh7hN1cubKKYHi4WR0o+K/RzYDEUOPZUk/IpGAOCSGLDjK6u159J0o+LVhn5srWWJ5lQmT/4n4IzHjCYW4JPb8Qinv1a9fukUM/clkvKg6ga+xo37AwQe4xm5I6nVP9Pz43+vBztfAwMDAwCKFCFf0SsFXKH2G9EICldxx5x3yNzRjpkxUp2592r49ii4wOZ0fsYA+Lv4xZb0vK6VJm1bKgBCGhYX5kc2IiAium1HyEe4oS9Ys8onwR+gLjkXr1q0TLagOkE1oU+/Pd7/URT8IFG/I5g2EQA9Zf0KitFcO8uB8MLs90AMSAXdtk3vfiYN+broWy++cQ2w/MePxb9+9bChldCSnTb/0eDaplVXiUScB+fK/B6TJJFxjNyT1uid6fvRzSirBNTAwMPiPAUvWWPo+eOgg7dm7h/bt3ycSFbWdateqSRkz3kuZMmcST3Ogb1hfIZYIX/RZ6bL0RuHCdNfdd1GOnDlcySa2tsyZM6eEQjpx8oRoP3PlziV/m0FEy5Yrm4BEKrKZ74F8Ug7aUUM2bygEIgHeGkrPB7uXZs3xMPckfj4C4N136PBuw5/QWBKIACVuPP5z6l42
lDI6ktOmf3p8VUd5iNau/zXWtYDOevb1T/Q1dkNSr3vi5yd+fB51A2hBDQwMDAz8gYDt+Z/PL9KzZ086ceKEpINA/tHqDxowYABt3ryFatepI5rHbNmzuZJNENW06dJSoVcL0RdffkGvv/66LMtj6b3Yxx/RgoURUvbSlSt0iT8BkM2mTZoasnnDItADNZC2x5GXUJwPZxdi45QAmiZPCUhccHoudTwlgBYt0Fw44UW4dYRSRkdy2vRL18/RQez0OkCo1yC+o8RdYzeE1KcL8UvC/LhJ0giygYGBwX8Xx48fpx5MLl96+WXRYP5W/TdJU4g7HecjnkCHDu0pe47slC2bO9nEcrz+d1kJdiCqVae2lAUuM9mEPShgyOYNjkAP1KDLmR7EwFtD6EVGEpIHN82jmwTWRgIJCZWI71ytMbmSEw033RK6jeDthn7NLCS2vD9Cuu4uxC/x8+MUZx3/8wh+nxkYGBj8N7F8+TKxrcTfypIlS9DMGTPooh2o3Q3wNs+enclmAM1m6tSp6aGHHqLXXn+NChYqSAVeKUBvFX2LunTtImWjo6Np/bp13Pdy2rZtmwSGh8f6Aw8+IOPAeAzZNDAwMDAwMDC4CbBq1SqfRhFazZ07A7+cByObsNnMnTs3/fXXX5KO8EUQOAWhDNqvW7eur8+XC7xMO3bskHBJDz38kI9swmbUkE0DAwMDAwMDg3851qxZ4yN+CE20Z/ceO8cdTrIZFxcn6TrZRFgk2Hi6ISYmRuxD895veZ7DaahCxQriHQ/bThDNat9Vo61bt9LVK1ftWjceDNk0MDAwMDAwMAgBM2fOpPuyW7Exf/7lZ1fNpr7FZcuWLYUgglT26tWLTp48Kekgm/Pnz5cQR7C57N27t6Q7gTifq1atpFKlSood521pbhPvdnzecust4lg0YuQIafdG3m3IkE0DAwMDAwMDgyCAVnL48OH01NNPia0lwg9hFyAnQDTVbj5dunShp7n8E08+QQPC
w0VTCSB/6dKl9PAjD1P+/PlpyJAhku4E2jp06CCFhw+gMmXKyNI5vNuh0XzyqSepRo3qtGHDeiGlNzIM2TQwMDAwMDAwCIJjx47JTj0tWrSgOnXr0JSpU/w8zxVAEKG5BKC9RCik3/9oSUuYXJ62l9Fhk7lnzx5q3qK5LLXDFtQLV69eEXvMRYsWUevWralBwwZUt15d6ta9Gy1ZsoTOnDl7w++hbsimgYGBgYGBgUEQgCCeO3eOTp06JSQTBNC5u4+CIn/wHEd5CILDq+V1ANpNpMfGxoakmUQZlMWSOfqHphVpNzrRBAzZNDAwMDAwMDAwSDEYsmlgYGBgYGBgcLMBjkoXztK149F2wv8fDNk0MDAwMDAwMLjZcO0KXdm3lc7N6Gcn/P/BkE0DAwMDAwMDA4MUgyGbBgYGBgYGBgYGKQSi/wPVPhzbuTJbFgAAAABJRU5ErkJggg==</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>127a8a5e-f8c6-4fa5-90a2-b672def52299</rd:ReportID>
</Report>
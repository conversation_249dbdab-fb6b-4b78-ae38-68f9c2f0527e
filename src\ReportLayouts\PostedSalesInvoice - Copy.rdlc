﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-11-08T12:43:16.4960013Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b23244e3-9790-4712-be0f-6041ac5f0e69</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>20.21329cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>5.32893cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle1">
                          <ReportItems>
                            <Tablix Name="LinesTable">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>0.84984cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.80281cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.00969cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.15991cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>3.22008cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>0.88196cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.53822cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.0166cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.26739cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.53468cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.85953cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>0.86393cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox82">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!LineNoLbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox82</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox88">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!CustPoLbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox88</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox70">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!OurPOLbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox70</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="ItemNo_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!ItemNo_Line_Lbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>ItemNo_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Description_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_Line_Lbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Description_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Quantity_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_Line_Lbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Quantity_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitOfMeasure_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitOfMeasure_Lbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitOfMeasure_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitPrice_Lbl2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice_Lbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitPrice_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="LineAmount_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!LineAmount_Line_Lbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LineAmount_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="LineAmount_Line_Lbl2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant" />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LineAmount_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Style>Solid</Style>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.38806cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="ItemNo_Line2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!LineNumberNo_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>ItemNo_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox90">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!CustOrderNo.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox90</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox72">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!OurOrderNo.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox72</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="ItemNo_Line">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!ItemNo_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>ItemNo_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Description_Line_2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Description_Line_2</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Quantity_Line">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Quantity_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitOfMeasure">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitOfMeasure.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitOfMeasure</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitPrice">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitPrice</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="LineAmount_Line">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!LineAmount_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LineAmount_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="LineAmount_Line2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!CurrencyCodeHD.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>7pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LineAmount_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox85">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox85</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox1">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox1</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox73">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox73</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox79">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox79</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox3">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox3</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox4">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox4</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox22">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox22</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox26">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox26</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox32">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox32</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox33">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox33</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox34">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox34</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox243">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox243</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox86">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox86</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox2</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox74">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox74</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox80">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox80</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox23">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox23</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox24">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox24</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox25">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TotalChargesLbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox25</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox36">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!NetGoodsLbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox36</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox51">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TotalAmountLbl.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox51</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox244">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox244</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.6cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox87">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox87</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox43">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox43</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox75">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox75</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox81">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox81</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox44">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox44</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox45">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox45</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox46">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Last(Fields!TotalVATAmount.Value)</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox46</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox48">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Last(Fields!TotalSubTotal.Value)</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox48</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>2</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox38">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Last(Fields!TotalAmountIncludingVAT.Value)</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox38</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox56">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!CurrencyCodeHD.Value</Value>
                                                    <Style>
                                                      <FontSize>7pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Left</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox53</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <KeepWithGroup>After</KeepWithGroup>
                                    <RepeatOnNewPage>true</RepeatOnNewPage>
                                  </TablixMember>
                                  <TablixMember>
                                    <Group Name="LineNo" />
                                    <SortExpressions>
                                      <SortExpression>
                                        <Value>=Fields!LineNo_Line.Value</Value>
                                      </SortExpression>
                                    </SortExpressions>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                    <Visibility>
                                      <Hidden>=Fields!Quantity_Line.Value=0</Hidden>
                                    </Visibility>
                                  </TablixMember>
                                  <TablixMember>
                                    <KeepWithGroup>Before</KeepWithGroup>
                                  </TablixMember>
                                  <TablixMember>
                                    <KeepWithGroup>Before</KeepWithGroup>
                                  </TablixMember>
                                  <TablixMember>
                                    <KeepWithGroup>Before</KeepWithGroup>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <RepeatRowHeaders>true</RepeatRowHeaders>
                              <DataSetName>DataSet_Result</DataSetName>
                              <Left>0.17918cm</Left>
                              <Height>2.78806cm</Height>
                              <Width>20.00464cm</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                            <Tablix Name="Tablix2">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>20.03411cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.50619cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox35">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Notes :</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox21</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.47973cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox37">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Comment_SalesCommentLine.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox23</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <KeepWithGroup>After</KeepWithGroup>
                                  </TablixMember>
                                  <TablixMember>
                                    <Group Name="Details" />
                                    <Visibility>
                                      <Hidden>=Fields!Comment_SalesCommentLine.Value = ""</Hidden>
                                    </Visibility>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <Top>2.96445cm</Top>
                              <Left>0.17918cm</Left>
                              <Height>0.98592cm</Height>
                              <Width>20.03411cm</Width>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                            <Tablix Name="Tablix3">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>6.82227cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.44758cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.53423cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.56844cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>3.32836cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.30378cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.64382cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox39">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">CUSTOMS TARIFF NUMBER (HS-CODE)</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox18</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox40">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Net Weight KG</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox33</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox41">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Quantity MT</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox38</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox42">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Quantity FT</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox38</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox50">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Amount</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox50</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox52">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox52</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.51153cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="TmpSalesLineGTIP_Tariff_No">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TmpSalesLineGTIP_Tariff_No.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>TmpSalesLineGTIP_Tariff_No</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox47">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TmpSalesLineGTIP_NetWeight.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox34</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox49">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TmpSalesLineGTIP_Quantity.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox39</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox53">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TmpSalesLineGTIP_QtytoShip.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox48</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox54">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!TmpSalesLineGTIP_LineAmount.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox51</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox55">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!CurrencyCodeHD.Value</Value>
                                                    <Style>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Left</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox53</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>Solid</Style>
                                              </Border>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <KeepWithGroup>After</KeepWithGroup>
                                  </TablixMember>
                                  <TablixMember>
                                    <Group Name="Details1" />
                                    <Visibility>
                                      <Hidden>=Fields!TmpSalesLineGTIP_Tariff_No.Value = ""</Hidden>
                                    </Visibility>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <Top>4.12676cm</Top>
                              <Left>0.20863cm</Left>
                              <Height>1.15535cm</Height>
                              <Width>20.00466cm</Width>
                              <ZIndex>2</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <OmitBorderOnPageBreak>true</OmitBorderOnPageBreak>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.30896cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox27">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox27</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!PackagingLbl.Value, "DataSet_Result")</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!TotalPackage.Value, "DataSet_Result")+"  "+First(Fields!PackageDesc.Value, "DataSet_Result")</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.33542cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value EvaluationMode="Constant">***</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!GrossWeightLbl.Value, "DataSet_Result")+format(LAST(Fields!TotalGrossWeight.Value))+" kg"</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!TareWeightLbl.Value, "DataSet_Result")+format(LAST(Fields!TotalTareWeight.Value))+" kg"</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!NetWeightLbl.Value, "DataSet_Result")+format(LAST(Fields!TotalNetWeight.Value))+" kg"</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.41479cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>***</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!ContainerNumberLbl.Value, "DataSet_Result")+"  "+First(Fields!ContainerNumber.Value, "DataSet_Result")</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.38833cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>***</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!CountryOfOriginLbl.Value, "DataSet_Result")+"  "+First(Fields!CountryOfOrigin.Value, "DataSet_Result")</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox14</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.44125cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>***</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!plasticwooddesc.Value, "DataSet_Result")</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.46771cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>***</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=First(Fields!CompInfoName.Value, "DataSet_Result")+"  "+First(Fields!CompanyAddress1.Value, "DataSet_Result")+" "+First(Fields!CompanyAddress2.Value, "DataSet_Result")+" "+First(Fields!CompanyAddress3.Value, "DataSet_Result")+" "+First(Fields!CompanyAddress4.Value, "DataSet_Result")+" "+First(Fields!CompanyAddress5.Value, "DataSet_Result")</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Vergi Dairesi : " + Fields!CompanyLegalOffice.Value + "  Vergi No : " + Fields!CompanyVATRegistrationNo.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox20">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Beneficiary Bank Name : " + Fields!CompanyBankName.Value + "  Branch Name : " + Fields!CompanyBankBranchNo.Value+" Swift Code : " + Fields!CompanySWIFT.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox21">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Beneficiary Account No. : " + Fields!CompanyIBAN.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox21</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>1.61396cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Image Name="Image1">
                          <Source>Embedded</Source>
                          <Value>flexucgenbuyuk</Value>
                          <Sizing>FitProportional</Sizing>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Image>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Header">
                    <GroupExpressions>
                      <GroupExpression>=Fields!DocumentNo.Value</GroupExpression>
                    </GroupExpressions>
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                      <ResetPageNumber>true</ResetPageNumber>
                    </PageBreak>
                  </Group>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Left>0.0842cm</Left>
            <Height>16.49935cm</Height>
            <Width>20.21329cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>16.49935cm</Height>
        <Style />
      </Body>
      <Width>20.29749cm</Width>
      <Page>
        <PageHeader>
          <Height>8.05979cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Rectangle Name="Rectangle2">
              <ReportItems>
                <Image Name="Image2">
                  <Source>Database</Source>
                  <Value>=System.Convert.ToBase64String(Fields!CompanyPicture.Value)</Value>
                  <MIMEType>image/bmp</MIMEType>
                  <Sizing>FitProportional</Sizing>
                  <Left>0.03121cm</Left>
                  <Height>22.99583mm</Height>
                  <Width>60mm</Width>
                  <Visibility>
                    <Hidden>=iif(First(Fields!CompanyLogoPosition.Value, "DataSet_Result")=1,false,true)</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                  </Style>
                </Image>
                <Line Name="Line1">
                  <Left>0.03121cm</Left>
                  <Height>0cm</Height>
                  <Width>6cm</Width>
                  <ZIndex>1</ZIndex>
                  <Visibility>
                    <Hidden>true</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                  </Style>
                </Line>
                <Textbox Name="CustomerAddress9">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Customer (Bill-To) :</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Left</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>3.95288cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>2</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress10">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CustomerAddress1.Value</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>4.40675cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>3</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress11">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CustomerAddress2.Value</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress2</rd:DefaultName>
                  <Top>4.86536cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>4</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress12">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CustomerAddress3.Value</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress3</rd:DefaultName>
                  <Top>5.2887cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>5</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress13">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CustomerAddress4.Value+" "+Fields!CustomerAddress5.Value</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>5.67676cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>6</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress15">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Ship To :</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Left</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>3.95288cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>7</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress16">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!ShipToAddress1.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>4.30092cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>8</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress17">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!ShipToAddress2.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>4.75953cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>9</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress18">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!ShipToAddress3.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>5.18287cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>10</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress19">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!ShipToAddress4.Value, "DataSet_Result")+" "+First(Fields!ShipToAddress5.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress1</rd:DefaultName>
                  <Top>5.57093cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>11</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo5">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!PostingDate.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>3.00038cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>1.81171cm</Width>
                  <ZIndex>12</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo10">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Date</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>2.57704cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>1.81171cm</Width>
                  <ZIndex>13</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo11">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!ExternalDocumentNo.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>3.00038cm</Top>
                  <Left>1.89784cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>3.8724cm</Width>
                  <ZIndex>14</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo12">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Invoice No</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>2.57704cm</Top>
                  <Left>1.89784cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>3.8724cm</Width>
                  <ZIndex>15</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo14">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Page</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>2.57704cm</Top>
                  <Left>5.79385cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>1.81171cm</Width>
                  <ZIndex>16</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo15">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!BilltoCustumerNo.Value, "DataSet_Result")</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>3.00038cm</Top>
                  <Left>7.60556cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>3.8724cm</Width>
                  <ZIndex>17</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo16">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Customer No</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>2.57704cm</Top>
                  <Left>7.60556cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>3.8724cm</Width>
                  <ZIndex>18</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo17">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!ShipmentMethodDescription.Value</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>3.00038cm</Top>
                  <Left>11.47796cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>4.34208cm</Width>
                  <ZIndex>19</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo18">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Delivery Terms</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>2.57704cm</Top>
                  <Left>11.47796cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>4.34208cm</Width>
                  <ZIndex>20</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo19">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!PaymentTermsDescription.Value</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>3.00038cm</Top>
                  <Left>15.85532cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>4.26492cm</Width>
                  <ZIndex>21</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="DocumentNo20">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value EvaluationMode="Constant">Payment Terms</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>DocumentNo</rd:DefaultName>
                  <Top>2.57704cm</Top>
                  <Left>15.85532cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>4.26492cm</Width>
                  <ZIndex>22</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress14">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>="Tel : "</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>6.13538cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>23</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress20">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>="Fax : "</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>6.594cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>24</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress21">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>="E-Mail : "</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>6.98206cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>25</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress22">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>="Tel : "</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>6.02954cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>26</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress23">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>="Fax : "</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>6.48816cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>27</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Textbox Name="CustomerAddress24">
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>="E-Mail : "</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>CustomerAddress4</rd:DefaultName>
                  <Top>6.87622cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>0.38806cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>28</ZIndex>
                  <Style>
                    <Border>
                      <Color>LightGrey</Color>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
                <Rectangle Name="Rectangle4">
                  <KeepTogether>true</KeepTogether>
                  <Top>4.34094cm</Top>
                  <Left>0.08613cm</Left>
                  <Height>3.02918cm</Height>
                  <Width>8.12797cm</Width>
                  <ZIndex>29</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                  </Style>
                </Rectangle>
                <Rectangle Name="Rectangle5">
                  <KeepTogether>true</KeepTogether>
                  <Top>4.34094cm</Top>
                  <Left>11.7806cm</Left>
                  <Height>3.02918cm</Height>
                  <Width>8.33964cm</Width>
                  <ZIndex>30</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                  </Style>
                </Rectangle>
                <Textbox Name="Page_Lbl">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Globals!PageNumber</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value> / </Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value>=Globals!TotalPages</Value>
                          <Style>
                            <FontFamily>Segoe UI</FontFamily>
                            <FontSize>8pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Page_Lbl</rd:DefaultName>
                  <Top>3.00038cm</Top>
                  <Left>5.79385cm</Left>
                  <Height>11pt</Height>
                  <Width>1.81171cm</Width>
                  <ZIndex>31</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                    <TopBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </TopBorder>
                    <BottomBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </BottomBorder>
                    <LeftBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </LeftBorder>
                    <RightBorder>
                      <Color>Black</Color>
                      <Style>Solid</Style>
                      <Width>1pt</Width>
                    </RightBorder>
                    <PaddingLeft>5pt</PaddingLeft>
                    <PaddingRight>5pt</PaddingRight>
                  </Style>
                </Textbox>
              </ReportItems>
              <KeepTogether>true</KeepTogether>
              <Left>0.13297cm</Left>
              <Height>7.66292cm</Height>
              <Width>20.12024cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Rectangle>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <TopMargin>0.5cm</TopMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <EmbeddedImages>
    <EmbeddedImage Name="flexucgenbuyuk">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAfAAAABcCAYAAACRBefHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAD4vSURBVHhe7d0HlCVF1QdwzDmhglnEHDBgxMWAizkhImYwoiiCiCgi6C4KIrKAIhIUDAsqKuYcWXPOIkbMOefc3/nVOf93ivlmdoad3bfv7dx7Tp3q7ldV3a/rXzfVrerNhqKioqKioqKpoxLgRUVFRUVFU0glwIuKioqKiqaQSoAXFRUVFRVNIZUALyoqKioqmkIqAV5UVFRUVDSFVAK8qGhC6L///e/wr3/9a/j3v/8963nof//7X7v+t7/9rf3mPKTOP/7xj5aH/N6XKSoq2jSoBHhR0QTQf/7zn+Gf//xnE7SOI7idy3//+98Pf/nLX4a///3vTThHKCchAv2Pf/zjqN4f/vCH4U9/+lNrT72ZikBRUdF009QIcMwHE0IY2F//+tfhz3/+cztfG7FGMEMMDYNUVzuYm+vIcdouKtoY1AviHMNqhLHjnP/85z8ffvKTn7T0s5/9rAltvynnd4Ke0E55gj1joKioaNOhqRHgYU4EN6HsPIyNlUEIh1wn3DGtMLZf/epXw29+85sRQ5Qc+x2zy3FR0cYimIRFKedwGQt6zZo1w8EHHzxsu+22wxWveMWWbnSjGw2PfvSjh+OOO2747ne/2zBPUf3d7343EtqOS0EtKtr0aKoEeASt41zLud8I8h/+8IfDZz/72cbsvvKVrzTXI8GcRAEIg2SZYGyuY3raKiraGETYwmMwzYp2TFn98pe/PKxYsWLYeuuth0td6lLDBS5wgeGiF73ocOlLX7odX+xiFxsue9nLDg95yEOGD37wg62u9owH9eE9Y6aoqGjToakR4JhQP0dIEEcYs7a/8Y1vDM997nOHO9/5zsPlL3/5YfPNNx+ucIUrDDe+8Y3bddYJxqgOoR53umty7RaT
K9pYFCzCczBJ+H7+858fnvCEJzQBfcELXnDYbLPNWjrf+c53rnOCHN5vdrObDa9+9aubR0p9SVuEOswXFW0KBNc//vGP2zF8kwGJAcHHY7ghPN9xymVMbAo0dRa4lx+rmZXB4l61atWw/fbbN4vkQhe6UGNoW265ZRPkGJvjnXbaafjUpz7VOlk7ybWrPUyzqGhjERwSsH2w2qc//enhEY94xHCZy1xmJKglwhuupf46IQ//t7jFLYa3vvWtzQI3TrRt3BTGizYFCu8PH//e977XMD5bQvFEZSz0v007TY0ARzrBPDYtSmf84he/GF74whcON7jBDYYLX/jCwyUucYnh/Oc/f2NmrJMwOMdbbLFFY4Znn312Y2QYJACgEuBFk0AwDYcE+Xe+853h6U9/elNKYZpwjqAmwHMs+T1CnYude51C++53v3ukDGQ+vKho2injJAI500UU3o985CPDO9/5zuF973tfm3pikePzCVo2Fvpp1GmnqRLgOoDw9vJZKlyF22yzTRPeYXSSOUGpdzG6zpI56qijht/+9ret87UDAPKAoahoQxDls3dhw1rwBn8YknM4pKS+9KUvHa50pSs1YUwwRyElqAWv3e1udxvud7/7Dde5znVGiiqMGwvyi1zkIsNuu+02nHXWWW3cSO4hD+4lzIwV43q52IumgTJO4rH61re+NTz1qU8drnKVqwzXve51h8td7nLD1a9+9WG77bZrsSPnnHNOK6tejDXHmwJNlQBPx+mAb37zm8Nee+3VGBamdvGLX7wxMYL7ete73nC7292uMTfMzPVY53e/+92bZUIR0F5ci443lU4tmjzqlcQIbOcouMZkWAyf+9znhjve8Y4N1wR4rGvY3n333Ye3vOUtw1e/+tUmnFkdBx10UPNCEfIZC7AuDuSkk05qCqv79c+Qe0ax8DshXlQ06QS38PrrX/+6eWBvetObDpe85CVHSqzcODCddNWrXnVYvnz58LWvfW3kRu+9
r9NOEyPAw2CQlxyLYSbphB/84AfDqaeeOmy11Vatw7gN5YT5ox71qOEzn/lMY3CsbfPfmF+sFGUFtVlD6z4Bg3wmeZ48h+MS8EWLpWBqJp6cB9vPfvazG06jdBLMAtSe8pSnDB//+MebxRwhLAnmOfnkk4drXOMarTysR/DvsMMOwyc+8YmRBSLHwJI8S8ZB/zxFRZNKcGpp5Cte8Yrh+te//oi388JSWo0Xyi5PlWPeqMc97nHDt7/97TbOskpjU6CJEeBhIkgH9efIcQSp5WFciDpOB0kY1rJly1qgGialgzDDxz72sedyp2OKt7rVrYa3v/3trSORNrUvV1dy7DncczaGW1R0XgmGemUweIsVDI/veMc7mvsPtmNVUExvfetbt3nxeI7UJYATjMnt/sxnPrNNExHixoMc3g855JC24Uv/DBkjcC933o+3oqJJJVillN72trdtY4OyGy8VPs/yhn1jwXW/c6uvXr26zZVn/G0KNDEC3AuVMJHZGAkmY76O24RlHeYWV8nVrna1dh1DwwgxRXXe+973ts0uEp2uwzG3hz70oW3uJMI5jDXMzTHyLPmtqGhdKYIyzEOCK0I4eOMSN2/d4xpeMZ/DDz98JLwzTtTXpnM5PN/85jdvYwMjw9Aor+JEuN2ztMy9jCX37vGunaKiSacf/ehHLcBTjIixAuswT1CzyI8//vim8Bo/LHJjSLl73/veLYi5LPANRDOFZX/u2IsXXWhtNw0r89467oEPfGBb640JJdBNXTuwxSWpQzFGLhUuyVNOOaW5YrTdMzHnYWbypKKidaXgEY4cw1sEOrwJxuESNGeHIcVjxIqwQQtvEqGrvnZYEjlHjrV3zDHHDFe+8pUbziVjBN5NLX3xi18cCXDCXB2J0ltUNEkE48YIbIoLQfDN0yTK3DjhIjdGyALH+Lz4EQJeGVOssE+JNRbIgBe96EXNG9Xvk+BeKOfuaVxMA02cAMdcpDA6LzcC1RyGbSPD
4HQMa1pHvelNb2odTMiHUaZzBDDc6U53GrkVWSa0Mm54HZ776LhoZu7pOJ1bVLQYgicJwVWwFpx+6EMfasE2cA2jEubEnf6e97xnNB4k9XrLXX3nFFexHfe4xz2aRYJpwbqkndNOO60xJl4sSTupG4WiqGgSCE4pqfgvXMInQS54+ZGPfGTj371XVXCyZWPBMjmwzz77tCkkQtxYMKYEh1pqZqykrHGZcRBZMC18f6LmwL28MDoMqz/GcE444YQmrHVaOoR7ce+9926RtsrpEDkA6ATHAHDsscc2ywRjU4/wZ90ceuih7eMQ7qOuZ8jzqJeOzLMUFa0LwVGw5bhnHJa5vOAFL2hBOFEyMScWg+Cb7CoV5hKGJnctubYwLu7ya1/72m2cUAjiSr///e8/vOY1rxle8pKXNAvFmOqZVp6vqGhjEzxScoNzApc39ZWvfGWztnmWyADCmQw4/fTTz8XzkRUaBLYxlTFgjHG/82hp03iBe/cyfiTtTMtYmBgB7oVJXn6Oc527gxXCstABGJOkYywXo3lhcDohQlwdx2F8duvhiuR60Zmx4gVCaFunKQssAY7z1A+TLCpaFwqWMBhMAr7gCdbe9a53DXe4wx0anjEkFoXcjmq2UiVkU19SR4JJGO+FsOssFzEerA9tpl3Kq61WzZMT4gS45wjDKyqaBIJjCTbDi+UE8l3ucpcmuGN9U3JhPUslYdm4iCB++ctf3qZLI+zllhm/9rWvbe54Y0XZ8Hj3khtn00ATZYGHkeRFIp1gn/OVK1c2TUsnxC0ocO3oo48eMTjalDb6Tglj0+YZZ5zRmGKsEkmb++67b4vwdU/zJxQCuXPtyaVp6dSiySP4gcEwCpiEUYFn++23X7MOCO4wJ5tSPOtZz2qYC/YwseBRG44l7fhdmxRXv3MT3uY2t2nCW7uwrm3W/bWuda3GwMLwPFPaLCra2ASXElxymxO0tsz2Jb4EaMIyXNu4xbJhY0N5Y0HdjBW7
de68885tfKkTZZbQ//rXv95kRizu3DPjahpooubAvUBMJAwO/fKXvxxe9apXNQtFB0g6kOb14Ac/uHWs8um4MDQ5ppbknAvmgAMOaHuk60TMDAhueMMbDi972csaU8M0rRP/5Cc/2ZiaDk0qKlpXgiUJRiMsuca5tG1EAdcRsrC96667NkUyzER5Lj/kHNMJPnvGI0AnVsvDH/7wFoUL530SD+KrZdpUz/hwLBUVbWzq+S1eTgi/8Y1vbOOE1Q3Dcpa1pZNwC+/wT+DL1Y9hZ2mmTb0ytsgP+4PYBMYeCsZP5ETG5rTQxAjwMDZ5mIkO8GnQxzzmMaPdpbx8QpclrVN1VuouhD7wgQ8Mt7/97Zt7UVtpkyve1nuCfQREYHDpVOQ+UlHRuhKcYkgSIfulL32pBWXCIkzDIcXSrmoEexiLcZCxAYPGBgEepdV5fuN9EuxJ6TU9lHXhURBYIL5uxvrQrjaC8aKiSSB4JIDhGu6/8IUvtFVGcYET3saMKVVBbaxodTImwqeNMb9RlMWSGAvwHwXA9wKiyBoLYqES3DktNDECPJpPGJTEmrCmz5ydFx4mRPN6xjOe0TQzHb1Q4a0sjYvmZS5cR0rcK0kYqU+SigpGngc45AFGUdF5JRiC6QhazMVmQrBGOYU7WBSgY/MhQTbKwqyysSgyPoJJ5NwYUM71fMWMezHR6Jif5KMolppxtYdJygvfRZNCwThhzGsqXsNUZ6xvMuCa17xm+xZGxoeyEd7GjbEiJ9SNCYbbtttuOwpipixrU0AbI/GjH/3ocOSRR7ZlyhkT00ATFcTmxeXl6ZQPf/jDwy677NI0p1goNCiBDF64jlEu9eYjTE7bdvERzIOx6UztY24AIolcpJnlmQChqGgxBEsSJoO5SNZlH3HEEW2DCZtSCNA0b23zIWXhVYJz53EPsrItfzQ+7Dxoe1Vz3oS+8WCzCp8T9UGUAw88sEWfcz+6B6H+ute9btRuBLdjqahoQ1P4
cCgYRBkn+K4xsmbNmrbcF+8Pf6bkPvGJT2w4D3ZZ2si59jPOHLtmzDznOc9pcVP4fvYEsVrjQQ96UPswkKlU4zFLzKaBJmoO3EvDgHSgqHHMJ3uZE9ySnXZoZDokmpYO1GHzkToYnq0ldRzLR9uUAx0KJDqVa+X973//CEQlwIsWS2FM8ITZwBR8ce9RRsVecBOK0YDT1MFM4DtMycZDloBhOhTNe93rXu1b9/e5z32a21zAD1eg8rmPINATTzyxea1gn+Lg3trXtjKF8aJxUQR4hG+OkWPYlLjH7f/PwIoAZ3Dd5CY3aYId1pE2yA2kHVhmeYd3G2Pa1Z6ANooyvh+FILyfB9ZyzhiF00ATJcC9dAnZfMX67q233roFHhCsXB7cizoCMwsDiptkPmKh2K3HPLdO407JvIrzuDHNh1tapn0A0HZSUdG6EIYQzIbJSJgZ/ArWJMhZ1mFsflfHsVxinbNIwnQwIMzIuWWSppUQ5mVpjTruHUbmmnuGSWobwwoDLCoaF8FksBkBjoJVCqldN+MdxaezOgOOM5bkcN5TlFe/KwvfsE6RFdBmvOD12pS0b17dTm0ZM9NAEyXAw6QQK+INb3jDsOeee7ZgHHPWgswErmF42V5PJ5knWQiZU7cu8J73vGdTDFj0EdqYoVxHcmP65GhPAVtR0WwUYYhhJEXBTE5wSn5zLsFyv2cBnCmfPOR31vfDHvawkdcoymesiV6AGx/ayD0cG1tyiZVv1zbTSdaaJ8Id9c/Rn8uLitYHwVKEt+NgTKJ8mpcW4Mlog3UYZ4lzdWfLbHVgNOOlP+8VAzmFwKoOwr+Pf5K0bRzxyBLghL1nmAaaGAHuReflYzTf//7327IuH2pniXvxPuiA6egQDCcdFXf6Qojb5cwzz2xtcUFuscUWI9e5zpTHAvdMEnKvhd6jaOmRQQ8fYUzwEoEdwem6chRObm1rwIPdMB/l5RkPwSAhb7tg3ijCWk4BzTHc9gK8J21r
g1DHmIwh1j5mZQz4rr7Idc8ZnDv2LHmu/FZUtFiCKRgL7mHT2IDNjBcGlKXDeDL+TFn1USrLfZVVL+MMZby4lrEWz6wkRsRqJp/cjedKCt93zAJftWpVKz8tWN+oAjwvKi9Z52FAgnIwF8u5WMO+MiZ63Ly4jlJWJ2GG6cwwSb8nT3IPKb+pw8LHEFn4Pi9qXgRIaGNZJ9vXy/FclHsVLU0KluErFi9iNXPhwRurQuS5aFdrtK1h9d16uIcdeYJxtIWhyV0Tk4H5mPYJ87GfAQbEMlmIAEdZ2cENn/0QjDEfUuHGN6byPMaJcYWca6eoaLFkbEjwJDmGuf4ahXLFihUtcpzb3MojH+RhfSubegg2M/Zcdw63lN6MRdsLs+h9pYygjrEmZTxRiBl2UQKmgcYmwL3gMBEvSCf0OReH73yfdNJJLUAnbg4BC74a5oVqI0xFPXUwuTAdjM4xqyadnM50XZ7nkOtc1oioXJG6liboRB+VYKVrI0w0x3ORNvP/ipYewUiwAqtwioFYvmIuT/AYoQljGAiGwdNjG1W4gVX41Ibj4M018+K77757YzZcipRM46KfAzdW7C61NgEup0AQ2MoT3tpQX12xJZ47/yPH6klFReuDYCu8Mjg3Zlxzjo8z1k499dTmebWG2wdMxH8YU3h5SB111ZNC2oDZyALeJ7trvv71r29LLG2nmv3UjSF5H8TWtzXJNDYBHuGJvFQdEQbx05/+tM1t77HHHqO1el4qBoPJWe9H2Cor1xYXut3SLJehXcm5vd/2trc1az1LblgcttrDSD/2sY81ga2+DsrzcGmKTvdhE+tyLevhvhetS6mgKABDgNenoiLUYwKu4c+c2/Oe97zhlre8ZbMgKIcEJ6EpWQ5jaQsGlJT6cA5vAi8xsbjOMRpLwXiItEf45vp8Frh2ue2NM+vBKRLZIMnymnxzPOPL83iGaWJoRZNPMBWFENYiG2AM7+Zt2n///du4sekQPmws+Q2+
w4dRcB1+7jy5FAW036SFILfE0goOH7hKQJsxxoWetqeBxupC7xmVF05wsnQJTm5rTAVj8zIxJC+Wy4MwTYfoHB2Ose22227NDagTMDMMUYS5xfjuobNZPpQAXzGTY1Ii3HVsnsmzREADj0h1Wp9gN24Xz6hcDwzlwxiLiuCjx0SYCve5wMnsOwCncC455haMcgpfknraE6+hrp3Z1CWs4RuToQSH6RDg0kJc6NqHZ3spqK9NirKxRnml6HqWWC6SY89TWC9aHxSeC4sZNxReOxPywJIF5IDgNVNNjD0Ef8pnrKRuKOOOfAhejb8cZ0yqL7ePgmWbduZMTIlNvpTPWMzYkUuuTRKNTYB7CRhBmAFGwzW+4447nosJhaHoQOeWEbDA1UdeoA6wFaQAHEJbZKLjHXbYoXU+tySrAaMTpGbNt/tgfhbrs4pY4v0z6VBuF3OB2mOReBbzjhQM84Pui/oOTQcnFS1N0vc9U4nVipnA7zbbbDMSwnL47pd+KademAcs2g0QniP4WczmBWPdY3CuL1SAS56LBWIHqnylSdIWZdi6W5aKOp4jDDHPVlS0WIJD+DY+8N64y7nJfWjH2Mg4YaQpGyyqkzGSMaa9/Oaa33JdWb8Fz+6XcwoyTxOjzfJksVBkhg1iTFuREcr2iQzQrvqTQGOdA2dpeAleAFfe85///LanOQHeC21J57EQzPX5Bqx62vDitMMdjrn5oImtI7nAuUl83ESOyVl+xkWovus6R0AEYS+gqG9Th7Pqn/SkJ42CgjyX4DbWCmbqufukXpJ2pKKlSfAAlxHgwZUpGPPXMGWOzRw2oQvrMC/SFgNRX10MAi5ZIxhayrLYReGaNvI7JkN51V7Gy3wCPBh1zAo3VeQZMEtjTTumsExnKec+GGKwLi8qWizBEn5retP0p13VGFlwboxQVCPEjR283NiKcA6WjRWGlUBR7nVTsX4zjpJTRpVhxacddTNO5ZLrDEque3PkvFHkhusU
WHU8c+o5ngQaqwWe5AV4+SIKuUy4qlkDOiwCXHJuH3TamRdGE0NeKjc5q/ppT3tas8ZpTKwSHeVly1nRmF6sZ0E6gheAhftEm67rFMxKLqCNUoGxSRgjoX/YYYc17U3bPTPL/wkQipYmwQEGA0fBkoEv4hvmCMgISbjiCYJf83Dm48Ik5BgbV17GBMvYNqhvfvObm8VAGT3nnHOa1Zyxok0C3PJLZWA7OA1m81xy98mmFpRUCdYpGo9//OObla4Mppfy2igqWizBIGOJAWd6iLCGOxjMGJHg0RQmvh/+CstyuKTMMsosLSNHTLVymfsN7n0i2pfITj755BZIagdD94Vlv6cd49YHU/bZZ58WX0JpNp4EdlKUM3YyptSblLEwNgHuBXj58rhPvESdcN/73re5E9NxPbNjga9evbqVlbw4WpVlZpgbC8Q8OSYpeh0zpJFZngMQ6ptrd+2ss85qjFOEu2hcHSdATYfqFLkyAidYPAAkeRbWvI1leo2spzxb0dKkXkg6hi0YZOXG2oZXQpeCCNOwx3sk+BJGYQi+KJG8P7xHxoD63HvqYETHHnts22Y4+zpn3Pg6k3FhDNighSCnoPbC13HOLdXhotQ+jGNcnk+8iKWbxqhEcTU+PFtR0WIpfB+fxZ9Z3HAcHIbnSrxQ8Aev6hlbXN/GzF3vetfGp02Nmip1TJiTL8bfvvvu2zbsMk7wfAqx7YQJce1E0ZVOO+20JkeMU4k8YugZa8pJEeKE/qTQ2AQ4RuKl6QQv2DmGcMYZZ7Qd1tJ5GBFG5yW6Zg5cIE+Epo5hZfhmNwHNwmbBc0Wq41zUooX7mKA5bBa4+6qHERL65rpZ6a4nAQlG5ZksteHOSZSutswPcs9HIwylftHSJdg0uOHCMQsWRuEvApaAxGhsTMSKhpkwBfXksIWZZCwkYShhLlZpsFj635Nch1WR5jxc8TAFo55NgnXWivl5jMtYcw/31YapKfsxKJv6RUXrg2APrghF
XiXGUcYGng6HhDplkgBnfIX/wyFPKu8VgY0ns7IFJ8MuoWvVEexqT/AopdeeH4w3Fr/laO4N156FTDEPL3CZkPcMxpL2fGOAPCGr3N8YpVBMCo3dhe5FeAlenC0c86UZKRaKThCQxp0RAa5uOp4GxIXOBUNQa48LhBDH3GheXOQ6kDVhXlBdTFV7XJKYpI5QNx2DeWKoLHzRiQS9TvRcOpS26DvNymgvTM2x+jkvWpoUhgAHorkxpjACCVNatmxZm2NGcNQrppgKTJp7Uy9WiePZkt+MF0JdWXmwKnqXwoo8jxSsZxzJKaTm/Qh9dT2ntnxEyEYzxqvn9GxFReuDYBGv5XUS/8EAg93gGv4kx1ZpGCOwG9nBrW3qCS8XmwTLYqoYWzBMQLOcnRPw7gPrPLR4Opd63OywTUHQtjXiBH6eQzJ1dfDBB7fnzbiZJBprEJuX7yUgzI67g8WMaehAzIgrnBuQ1kM7MxdoWz0vOFa8NrSFqbAivFzWNEakA3WUjuVawYgENyhvjpwWxiqyXlybGKdnkZTRvrasKxfIEIYmp51xOXo25QAL5XkmrXOLxksRlHCZL+lhAnFNOxdLgWEoBy9wJw92CHBWcZRH1kgYWjxCFAHt+k0e5dcx68Exxmee3H3gE6PK+JNg1zUE6zxOvSKtLctrBIdmrGmrqGixFD7OFW6ZLz4dPBsnsM/IgkNBbLAKe1F48WcudJY176prjEGeIzw6Gx9py9w4/m+66slPfnKz2nnGRJgbB9oNrj2PmCrKrLruLxkHgj5TVr1JobEJ8N46kbxwc3ZhQgS4ZEc0vymjo71oKaTjWdICFnSOOWvt6lTzG168JQE0Mi+eFW4uUufrdC4UnySNANeee4WRakvOCueWMX/i+dKZ5lSOO+64kcDP/5HUK1q6FIxz6SUQEmbghzDmVWKZK5OxADPBIKJQcgFSACz1opRKsM31zir2fQDzf5kjDz7t8mZeUdnTTz+9KbfBdI9T
570AN75sKMPaoCjkmc0bWnIZhuv/FRUthIK1HAeDOZdEoBsnsbbhDgbtWc4D6hv2DCaCNW1kjMGua3BspYdPTFM+eWHJB5/mJVssKWbgKbty5cqmRGs78+CwnbYoqTZI4jbvFdnNN9+8jUH83v0oA/3/mJnGSWMX4BKtyQshXMOAJC4RkbFekJfl5c58KV464cxFyNoQectCx7RobTQwbegMUYQ60TwKN7z13ZbePOABD2hWtI4L80xyzbO6bqkZhQLzTWdy0ZsfpBSkbt9G0dIlmJMIUVo87EkYFGEoYpZiCF/wjbHAuHMJc4Ahv1FI5Ul+S2LB805FuZTg030TZau9YNOYCwXbwSu8a9/HTXx7IO15ZgqIJZSmpzJ2i4rmI7iK0JTgC+4jKOGPokrIEpSwhsdKgj4FYHJ7U2RZ0wnwVC/41aZjgZhkAMNNfJPycEqQa5tgj7vc1Coll1zgjdWOsZIxIJcYaBSJjAWJosFTxejL/5IyLnJsPI+TxibAdVheGM3LelMvHZPQcV5SNqvXMemgvJjUJdy9RNa0l0oJMDeNURLeXCBxj7DSLUPLDm3up45ladrRdubBQ64FJJjo0UcfPdpcIIzNsjJuGCBV1vMUFcGRXf7sM5CBD3Nc37xC8RbBWHDdn0to5rF2YSzMA+4wtyiVSRRV896wm7ED35iSa+6VcYXkYaqsHPtAGyvaClPlcRLVrl6eqahobQQrwTW+H+zAWuQADxGDzXQQGQBzljRmzw+8ntDPTmpIG2mHULcXCI+qMWYePWXcgyLAoOOlpfAaM9abu4dgNUYgMqY8nzZzHytDdt5555EHTc4Kd4/EU3m+meTeaWNcNNYgNi/RGleBBV6uTsMkMAuCVaRgmIqXqqPlOfZbgIExsRq4Qyy8p13p/H5uQ701a9Y0YWttKzehe3gG7bmPpLzcNURws6Rc05ncOIlM9KySTWQyF65+0dKmYFQgDIUSVoIX2jxGAlM0dEwjFgpM
qxty7veMAdiSXI9HCoMiqI0diiUG5l7GQAJ2lJOCa8dpJzjXjjY9CyuF65/lkjl2Ce6txRXsVlS0EIIpWIMzCc6cE6Qwjf+aAjI2YIyQdOxb9xTgYLdPqMevaVaKMcHKs+WeMCzuQznuclNM++23X1MCtMEDC8/iqniBUQS439O28SAORbyUsZXxQJk94YQTRv+pfzY083wcNDYBjrw00d+EdSzaMAkucHMXXg5GFwbn5fYvy0smYLXlN5aDJTkEus7wu/LaUB4D04F26lFPR9OedHYYpHKuK4v87rfci8XOPRPhLXGJ2jM9gUJFS5tgAP5YsXFt98qpfQeCNdiCb7nzPimTcjNTmItjWLWBEfxxm9u5jcVCgAfHSNkwprTjXDuSY/dLe3YizMeEMFYBeFzrxktR0UIoWMWDYQvOHIff8uhYHSToLNa36G+ygWe0HxtpL+MCXi0ji/udUDV1lHlzEePGgulP38gQJG3JsPFnVZOxyajD7z0j3GsThe+7n7HAcPN8xoNxTGEQ2Oxb/nke5fO/NgaNTYD7k9wWXBNeRJgbBkHr94K9TC9V2XSia3FX+M2xl9UzpBy7HgtEwlAJdr87x4TUd97Xd819HCsDbLFO1DPfDSBZpuD5dSyhbv5dGfWLljbBgeAzyh18RwjCCes2GI3QzHnwCIdSmEKf4Cs4Uy9C2DEKnnPdOQyrm9/k6su1lWdJGUqxvdb74DjHxixFoahooQRncBjMxuDi9rbvgPEhsXAlghfvhv8I1358yHMuaM2qCTyYEBdvgiczBE2jWlrMYDOlZErIdcLcdKtr5JBny1iS8swZfxQJU7C+iOY5jWPTtILgBJemXsaQuo7HTetNgPvTeRH5UzlHji0Po3nRaOL2c+66UH91dJZcSqelHflsLyll1I0wlpwHCHFLqq/ztB/C6NTzW4S3Y3UcUwKs3fU1M89M8dCpOtO8Co1Qe8oWLU3S9zBgcGMUBjyswIl5N/sHwBPm
ZKzAolwKzuVzpWB85rH7agtueaM8AyU0eJQr6965V9pQz1iIdcSyyHfHJc+PSe60006jqamiooUQfEXwSuHDe+655//bhGj77bdvBlwEd8qmjWA47cK3QDfTVfb74O42nSqwU9Q54Q3bPLp2L7R8jIcXD3ctY0/bKDIjz+k49X3Kl5HpOQlwygKFnFGH8pypO25abwLcH85L7jsM+U3QjWVjNPpoX7QnIftefurnZaQNnZcX6nhmUiYvPetrk7jOaVJ9GdfV076Oyz3kfsPQAAn5PYLdvrqW78S9KFFCLHWwMYAyQFG0NCnBYpRRAZOwHSGIYZmLo/lHiMJYrADnPZZnS36P8A8+kTbUV0ZbhDhGReDCPuGsrhSmBeeEu5T2jR37MthEKfiWKCKCd3LvoqL5KBgLP4VPZC9yLm1KobFBIJrDNidtbJiuDFbVDTbVD8aTYJfFDrcEOrxTUqV4XSW/KUcW+E07UXpTxnHumXs5xvspCORWxoPnZuVbzmlshfK88nHSehPgHjwP74/kJXgxXqKdcWzSQpuJ8BaJLmQ/QWXKpm7qSzPPZ0t+1xE6KUELznWCFx1hrfM8j9/660lhVI61FWYJiJYRcPfHupJsDCCqMcpD0dIkeIFf60jhOkoqjMC6z4lyEwp8hDllYTA4hTNtrA3nuUdw6hohrQ6CUeOJi9I8H6wrB+fuIamrHoxLrtlG0jLL5cuXNwXbGI3L0DmrXD3PXVQ0H8EXnMphED4JVcu9uLONC/iSCywTKAyL+C2MKQ9vwaqUa45hFg937tgYUA/OZ5ZNcu4efpNcUz7jLuU8t4SUY83b8TNB1xnPrHBL2DwHSl11xknrfQ48L0uOvCAaEFez+QidhkFgDiLDrcfTucrl5UnayMvOC5+NXO9TNLCU15ZO1gbrBIPlZiHkcw9lZ94z19TNb7Q8nUlrTGeae+FiFEBUtLQJsxJQOdvHeVgdLHMMy+YoXHuWU9pQSJ5jrkTz5awV
8RWWS9oa0tbBhLL5PdiES7l7YkyWX3L3iYDnGTriiCPaftA2jmGR299f22nfvXw0RTnrYkXc8i55bs+KwWqH4sE12VtSRUXzEeEYXgqfjB9BZMZCpmi4o63OiOUN08F1z4eTco4Xp5x6kt+0Yzy4HuXUMVIninN/LW2GHOdcTi7ZjXDXXXdtY9lzGx+myQQx5x6S9pUfJ41FgAtgEDWY4Jis/WORWJ6ifF5mkvpy172kvPi5knKYjFxZmlGYjs60BtdyMha0Z4nWh+TquE+eRdKu6zrGb8BhvsU68IDQf7HZCyWlaGkT5RFGXvziF7fI88ydwQnBKMEL/BCYpl+4FK2HJXhznOQaAaqcROu3/IYiGtzDN/eje4on0b778nbxBIju1Yb25GlTcI7fbCuMkbIqYhXleT2n+UPKKaW4qGihhG/ioXinlC1ToxySBaK8LQczZuArwlddPDlC2HlkgpR2g/+eVye5lt+0LYksF6Fu/Lg+H6mvLfLL6hIKrXERI9TqDGM+QlzZyJRx0QYX4P4Qa5cbDqPwx3Wi3MYtds6J64SwT+o7MJ2ozbUl5ZDj1GURCSCyVtyi/ywFsLuUNpF7O5Z0WlLfriQSl5XTW+AYpijdWie7tAnmYQRuBL+YIyNQCUYDPsKxt0Ck4GghSXnzbxRTuDRuLCEz705AG1fad8+0nXN5f44ReSblXJP6OubtfWiIt8p/yngqKpqPIogzJuQ2MjLdGJyJCucBitEFy/h+5EDPh6XwYpS2+2P3lDsnrOHV1Cyezg1u6RqBy/tFJvRtzpVSxn/wKWkeZDLM85NfpqrcK+Xc333HSetNgOfl5Th/Su7lHnrooU3bxzjCNGhkhPgxxxzTNqa3DlDovpfFbcfVx43IheirNdzfPlIisIArkUtRsjRBVKKOCnPzYrkdWRCYG4ZEg3Jv53ZYs6mL+1mcT8i7PwvbsyS3htDvxx9/fHOZ6DT/IUwVQ/UheHPgRUuXIuDgXaIg
mm4RYWveL8Ix2Cc8e8G6kAR3mCBsB+MwyprWXvCd8s4xnB6vfle2VyiSUgambVWcqSb/jUvUuC4qmo8IY/iMIHa81157NR4cjDkmEwjTCHll5c6TCOBeniDH2uYyd8155sSdq+cZ8GQB0rbOtpumsYBX2wQp7a0taU+Cfa5+XgNj1vNTlm0z7DmQ8tqMUTguWm8C3CD3J1D+SH9OmOYDDJiFF4GJsGa5+7j2epehLVAxJm5DQtO6P8knGiVLunysBIPkFrd1no/E6zQR43ZdU56mF61Jcl9WM3el9rkZWebO3VvOvUnIu+7ZJMcUjkwDSLQwrtJTTjmlgaZo6RK8GwPBPUvC8kLYoETCmjlmmIedCPJe4AajcyV1zb1ZchkGZ56bd4nQ9TvGEibjnBCPcM55FIe061g9m2KYvzfVRCHGBDGwWEZFRQuhCFICFu83LgRJ2kQl+Ic5H5WyOsNvYkLEaiQWRAyI+A8xHL4EhrfbnIVinA2M7Nomx+9NYbqnJKhNYCarn6yAbXybIiteyY6dYkIYcHMlW71SjpVlvJkmNVYyjijllsRFyOc/S+OkDWKBy6PhhMw72FjevFtcfZiJ41jlXs55TWFAPkGHWbKsfU6U4A5jkjCtlJXCxOTu3zO2pDxTXy9lXedS2WOPPUZ7XJcQX7oE68ZALAbnBJ+9kwly1ixs2u4UPrnzbMdLCSXcKYKU1bmSMhRSH9IxXZNVD4Qs5oZZURCCYYqosUYZVm+77bZr96CwYmru6+toBLZNikwN8TRlE4xYT/k/UgnxooUS7CSQTW6uGw7D68NPbTNMkMeggnU4dRzDDXYZasYLq5ch6HvgpngYbixs3lP3scqDHLA8ud9vBK82NijADLWMjbmS3xlxjDnGG/kSBZg8YczZobMfI728Gxet9znwtRHtyUYXXg6NyAsJw4lAXltSXkrn94lLxnpCgRFebur4LTngAJA2HEd49+2m3lxJOfW4MmlzNEaMTUfG
nVK0NMkANpgpcgY0TLiWQc4ysPSEdWsaSDItxNJgcRDycyXTSSJ5WSOmpFg3EaoS68SWkjaNgU/ue+XV4/1yDxHo7mNKimXj3qagrAQxb08pgGHPKvc/8h/iRi8qmo/CD+EIduAVtsSF8PJEiMNpeDDeSzASkH6bix9H+KeOcx5VLnpYt1SNJzff09emXOyTsrn3zHZnpjyX45SPnKAYUBBY+Rnz8ii946SxCnBMhybGEqFB2XIyLzQvx8tOx8x80flttsQVb96cVmTDFUqCTkvd3nWfdv2u89035daWgIsGR1v0gQeAEaGr0zA7HVm0NEnfJ8Vi7a/BSHDSC0dpZpnZkvbUwxwxi9Tn2ZI7t5MaRuaTiYR0GGnKu0cUC8m5MoSz6/kfrHop5f2uvryoaD6C02AOvghwWIJPK48I8fBtPJUMiMDMud9m8v8kv0VuSKxruLfbWrxQmeqc2U5kTM7nSspoJ+VzP9OmpmyNL/8z48V4Mg8vHyeNTYCH4ci56ASniQgkCHfZZZfmBpFYteYbuPZoOVwkEpcj9wm3n8SdIvl0oxdKIaAcEKhc2two1u5ZewgUXnw6nIUuEt39RJBLlpYpz0Up0eSsj5V8JUdQj0h6nyvlanSvdJhO9L+Kli7BgAQPmJfkmACU4EPqBWhSysy8PjMRoH27jlnF8ghrAZ2Y5Nlnn90YjDqup55nVDa/5blyLQI9/yW/OS4qWggFm+GLknNkvwJLHk0jCSwLX8ajWcsR5hG68ghRxxG+jlOGAId5XiaBxuSBcqmjTIy0XiCnLakv219L4uE1/WTK1Fx9xoT/mnHpWv7nuGisAtyfDWOgtQjGIWy51rkAbbcqWaNt8by5Pe5GgTpcfSLQuf24/0Sl04J0GmWAmxDTQl4il412jjrqqCb8Ce2AhQIgSME9tO+e7m9JG8HsecylcC3aON++twInBE1wgYrMDTD9H/9Lcl60NAnmMnhz3F8LM4OTDPwco5l1Zibl
egFu/KjvHDkXlU6gC+whjFk+sRIy7iTP0QtqybnkGOU+eUZJ+aKixRKvEd5ud05BbDb5Ihht7OVjI4IyGUtyBhRhbwMkRp1jBpftfRl4ckaf1ULwjjebOvJVPdNJBDblgEWO95vfZpyJ+3APiRHpXC6JJ/E82hCA6vPXFAPL3siYKM2TQGN1oYcJJEUAhrmEvJyegfg9zCrMJNcl7WiPRawMppVNXAQREe7mxwlubnMWPXdO2lUOAwQAbeW5/Jbf80x5hjBBufNcK1qa1OMjGAluQo6D2+AqZeRro5SRSzCuDcewC3sEuLZdkwefM8dcf6+027fd4z5ltKd+UdFiCZ5gU0yItdr2MhBJTjhmsxXGE+OO8cSoYmxJjCuJUUcJkEvKalPbxgZDixeWwDftmUBmAprx5z6CSyWyQG5DJM8hmd+2xM3um5LnJLiNCeOArJgEGrsFLvVMxXEYWkgnhKnMZCZzUUAh93J7Lck5rU8ADw1PxC1XTphZOt3z5L653qc8e8+AHWtfXrR0qcdNEsz0v/X4kXoswZ4cqec3KbiTawPG03Z+t1NU6iLX0p7yjuVw2mPV70nKSH5T1rUc597yoqLFEizBVnAph2WYgzHX8GPkOmNMnnrKOMfjg8+0l9/V54XlVbVdMGtdzJV14IS9+sg9w/v7sZDfM+bcSznnfvdMk0BjE+B5uWEMSTmXe/EzU8hxXnCfvFDJMeakXK6ljus55wbnHs9mGO6tg/q25krKBkhSnkHHuu73oqLgAr5QcAOHBn7wHkYh9deV85sEZ/AVXPtNGXXiRseo5Eiesn7HeNSJle44jCjneT5BppL7ase4yHOlzaKixVKwB4fwB2cw5noSvEmwCMPKB/epj4f3YwNW4dw5byx3urbdw4YuBxxwQPviHos691VPuxkDSc7zrJ5Buyk/SWNhrC50fzyd0L+o5Hk5khfMpWIJjaAB3341by3SPIvsuU68VG3qSK4PQTzmKiS/23VH
R6dt7Vrrap7Egv0s1reO0DUdzXWSzkpH9Z2W/5FORn7zP4qWJgW3CDaCNwQX3IIwDMviKjAEjEEZv8MvzHHnWUmhnF0C4dK5ccDtJ25EHXWVh3Fl7SQoh2XHNsPgKhTUGQbILWgpmTI2ztCuY7kxZfdB7Xl2zyOpF6YY/BcVLYaMjySYkvDS8FQ5nm36M0olJZVAhmeCGqZhUnljQRt4O2+U8vnGBvwq7zf455oXRK1tZXlmufL9rh3tqZPjPGdSfpcmgcYqwNdG6ci8KC9WYIKF/CLJLd0SlGBxvdy2rDQqzEVnEcp2xsnyNB9isOD/wAMPHAl67QOG9bIW8/v2sXL2rJYs2FfHvrnmZXSiZ+mfr6hoNoKNCGyYybkEeytXrmw7+tmkwt7lBHG8QJLymIrNjmARhi2N9BlDx3Av4EYsB2Ug7VsdYVmO3QaVsz+BXaKMEcE9vlVP8BsnlFoBnVtuuWVbJ2v3NvOD6tn4yPKzFStWjIQ3JolhZew4LipaLMFR8CvHZyNAgzPz27Au+ZCIfQ0OO+yw0Zf8uMFT1zhSxzz2qlWrWnlYJ8i1qxwyn258GYvc6sah7VwPP/zwUYBaFOk8D0Gt7Qh1ud+lSaCJEeChvDwuEFGKmJgIcsu8RCJK5jNEKNqfVuexonfccccWbWgLVkvLJIJffXVFmesAjExgAwaGKfpYu+VjkrXjyotsZKkAQM+Mi4rmoh4jPWYcw7NI1qxPtaEFBdV1v2MQcq49DCWRswQxYU6w2q/AchZjwffns4SRgut62rarlfJWXMA4JdW+C5QD48UOV37THkFue0sKgGRZj2WSrByMCjPzH/I/pKKi9UGwK8E+viwFY3BnqW62wYZ5yi9+bjkXY85378OfKZqsbAI5678ZY+Kc8Hv3MdZ4mIwn4wv2tWN8aNOOa5QDgWywH9wnacO98tyTQhNngetIQtyL95ETjMZWelznNCxuEC+ZtsRlbsmM9dw6hfAm9C0fE7xg
vaEOwgwtV8DEtE0BYNnsvffeo4+gcJsDjV18MDXaHisJwMJgi4rmIvgw0FGw7Bx2MBdYIzhtNGGv8TADv2MYjgl1mIVXwtd2p6wDWKSwWgajDcyMG15dW0xGgFtec+SRRzYLhAJrxYVxwUpX3pJKDEx53xIwJjAt7atn7FiS6TnCtJIbl0VF64vgH35hq6dYuLygFFiCmMLr3FjAu40PuOcODzbthW6ZGeHNCLM23HRoFAPudx5dSmqi0VnjxoR66lCGfUiLsA/u++dzTVvSpNDECHAd52XnhXnhrBHakQ1cIsAJbAKcIKepmefDlDA8AlhHagc4uNZ1Pq3M4n5CncVuD12KgbV9BLd2zINYY26DF25FjE39dNpsYCsqCvX4yMCHHbiGOdM72Rxi//33b0xKHSlMgcJI+BLUmBeXeZZGmtIx3WMDCktizHNjNLxLGJI6VllQfCkMFAGucWVZ3caJj0ZgiBigrwBansN6kdxHXWNHck8k94zGSFHROMiYIVzxfrzalBEMwqg4D1iHY4YaXCrvmLWOd5s6UteOnKx0v6sv3sPUknL2DzEujUMfNjFGWOWC3IzF4H/SaWIEeJhdmAer2Py1OT3z3axs2+VJGJvfuBzNZbAyWM7m+rx8whgzcmzTF9qVxNVuPZ+d3nQiq0agkOA1wLDEgJuGOz7MMIxYW0BQVDQbwUYEOLwkwTTGQ4BjPAQqpRLjiacJttTlISLACVgeIhtcBHsCbsRuEOwEtm0jCWoWiXN1WPaC4Ci3jrkgKQ0seQyOC52L3XP4UIQymJpx4TeMjILsfkmeK//NeVHRhiY4g0cfOmFQ2ceDIKbgssTh165ocC64zfgSu8Eo8y0McR+8T1zk8GwMGkdwztgzVgRtMhKNOV8bU9bY4GY3LqcF6xM1B+7FYWxens465JBDWqCNzmB9cH9kvs48tXltc3bcitwgOllHxZKXzINgZJgcF4pO41LU2drWFq2NosC96Zyrxv2j
UISRFQMrmosWKsBZ0IQvwQ3vrOhgFeYE2MAzAW6uO23wLAnaZMFTWPMpQ0GXGBLFgKDWtt8EabJCCHxfQLMaA65NKRkLknYIePUlTMzmF+4nGYvBvf/nmYuKNjTBHUFtKlPQJ2ON98g0D+ML1gVjGhMwyZCDfTzdSg8bvRDUMO57FSxt44jXynWYt9Oae/DyioPicmftUxZKgK8DecGYhJeHeRC0IgoJV1axr5hZUpPlZJbJcCvaA5eLXAeau8Nw1A/Dse5bp2FkNDCdbe91zI1CYH7d5xV9xs65eXRWvbYBKZaS9oqK5qL5BDjPEeErscAJ7iiaysAYAY6h8A4R4KwJpAzPkU2I8rEGDMg9TS/BP+XAbxiR6ST14d6+/rYc9gxwTUEluCmvAnfgnTWDUSprDtyzabtXhP2XUmCLxkHwxgKHZcos7ykDS+LmNh1qCTB8Gje+6uc6Q830qmlRq5HUNV3FytZmLHBjBf8XHGecuI8xQN4Yg9NEEyPAMYcwC8esaVGFOo1mZL7O9QhTTE8ZHa3jWOgs8l7Qcq9wC2qDAOcWN69tf12WCKXA8jNLEiTWvOu2WsXIKACehxBPYENR0WzUC/DguBfgmQOHQ+4+7m94Thn4gk0BZcphMCzwCFKeJB/vIaRZGieeeGLDJQWUQCbEudPdx17OrHWfusWQ1FeWB8rcIQZmJYbllcaQKSfWDEbnWfvnl3s+eT+2ioo2FMEdtza+zQo3p81rar9y1jiBDYssa4YerxPFWHmBmfDPKGOpmyoyrQTDxowVGjy6XPMC2SjFBx10UBsrjDs4z7icBpoYAR6mgdlImIpAHPMgXBwErOuEchbxywW3sUIwNp1nXgRTUk4dTA1zY3VnUxdLxlggXPQYo3a1Zx6cRSKZF3eNwoD5AYByRUWzUT/og2XnGA2c+igCwcw6Npft4zy8Q1ZT2LdZsqqCmxAzopQSxnYNtFLC3JyxQPhiVDZ2oWByJbLItSsAJ9NI
MBsGZyy5LuKWBR4BLmjTPT2DZJ1sxoP/MzNps6hoQxOswSoLGr6NAZjGg8P7YZTCyVNKGBszvLUMMJa1JcL4PoXZRkXqWXomnopFb5kwPBsfxpE2M26iXE8DTZQLvU+CdswHEswEtCUDeamYI8bonNakHIanswSmYYKsnOXLlzctTGea/9CJ1rgChc5Vj2sy99TR5g7NBQpuA5JegDsuKpqNIrARXObcMQYhIjZzzhiIpWDZrIXwtexLZGyi0JUzL82zhJG5RlBzr1suhnG5h30RXFfedBJL2n1jNTuWew7LxATqYHbuyxrhQmSRGCfGEGsm8R8YGcznf5QALxoHwRus4tG8r1YPRalMDtP4uSBlglsyT7569epmoXOHs8J5qyirgqJ5oAhwcsK0UsZGxoukfblr00AT5UInlDEMiQC3AQWBymLmQo+mpBzmgrxw0ecENoaoczAi1orOM8/Hkifo1SOwzS2KWte+62FOOt/8ivWAXDjZ0MI9pqVDizYOhRGgMBjnctgyXUNQJmBMMJtEOGNUy5Ytax4gO0+xsPO7On4naCVf1GM9BJfcizBP0TXlBLPuaQzJPQtL3HVBO2I9jAtCX3J/CoT67seliNl5Zu70/Adjp6hoHARrhLH5atOdvFPwCIvwnIR34/vGhVVKrpERme60yoJFTmhbgmyNN3kA72KoIqiTQhlb00ATI8BDXly0fu48ljB3ITegjk3n5aVjLpKOw9gwSsE45kGs5TbPFwGcsjQ0FjmXYW9VYHLmxAVIsPijMLifekVFc1HwiIKXnGMI8GYejzJqWocXSGJhWFFh+sdSL0zG2m7X8jvr3DUWOmU19xK0af7O9JCyloS51o8P+M75mWee2TwBPFra5HqkOFBa5aaqWCnqGDPw7384Zu0UFY2DjBdbqRLAjC/TPPBLLpAD8ExIi3niFmeI4emJNmfcyY0F3liBoYS9aSJtCmwj0DcFmjgXOobRMyBCOwJd8ptryrlO
6EY7cy3BQHElJkUzU0Zyrp2+TJJ7aDu/p47joqL5CH6CYQRHPZb8FkwRjsGl32EZdmMdBIso9VM2+cwE2+r0v7uPZ3AvKc+oXO6FAYodce66a8qkrvOiog1NwR3MyZ3DcjCY5JrxAs/99T7xIlE+taOs6SHlNxWaOAu8qKioqKioaH4qAV5UVFRUVDSFVAK8qKioqKhoCqkEeFFRUVFR0RRSCfCioqKioqIppBLgRUVFRUVFU0glwIuKioqKiqaQSoAXFRUVFRVNIZUALyoqKioqmkIqAV5UVFRUVDSFVAK8qKioqKhoCqkEeFFRUVFR0RRSCfCioqKioqKpo2H4P/DbJK3p84GRAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="Description_Line_Lbl">
          <DataField>Description_Line_Lbl</DataField>
        </Field>
        <Field Name="Quantity_Line_Lbl">
          <DataField>Quantity_Line_Lbl</DataField>
        </Field>
        <Field Name="ItemNo_Line_Lbl">
          <DataField>ItemNo_Line_Lbl</DataField>
        </Field>
        <Field Name="LineAmount_Line_Lbl">
          <DataField>LineAmount_Line_Lbl</DataField>
        </Field>
        <Field Name="LineNoLbl">
          <DataField>LineNoLbl</DataField>
        </Field>
        <Field Name="CustPoLbl">
          <DataField>CustPoLbl</DataField>
        </Field>
        <Field Name="OurPOLbl">
          <DataField>OurPOLbl</DataField>
        </Field>
        <Field Name="UnitPrice_Lbl">
          <DataField>UnitPrice_Lbl</DataField>
        </Field>
        <Field Name="UnitOfMeasure_Lbl">
          <DataField>UnitOfMeasure_Lbl</DataField>
        </Field>
        <Field Name="TotalPackage">
          <DataField>TotalPackage</DataField>
        </Field>
        <Field Name="PackageDesc">
          <DataField>PackageDesc</DataField>
        </Field>
        <Field Name="GrandPallettotaltxt">
          <DataField>GrandPallettotaltxt</DataField>
        </Field>
        <Field Name="TotalGrossWeight">
          <DataField>TotalGrossWeight</DataField>
        </Field>
        <Field Name="TotalGrossWeightFormat">
          <DataField>TotalGrossWeightFormat</DataField>
        </Field>
        <Field Name="TotalNetWeight">
          <DataField>TotalNetWeight</DataField>
        </Field>
        <Field Name="TotalNetWeightFormat">
          <DataField>TotalNetWeightFormat</DataField>
        </Field>
        <Field Name="TotalTareWeight">
          <DataField>TotalTareWeight</DataField>
        </Field>
        <Field Name="TotalTareWeightFormat">
          <DataField>TotalTareWeightFormat</DataField>
        </Field>
        <Field Name="GrossWeightLbl">
          <DataField>GrossWeightLbl</DataField>
        </Field>
        <Field Name="TareWeightLbl">
          <DataField>TareWeightLbl</DataField>
        </Field>
        <Field Name="NetWeightLbl">
          <DataField>NetWeightLbl</DataField>
        </Field>
        <Field Name="ContainerNumberLbl">
          <DataField>ContainerNumberLbl</DataField>
        </Field>
        <Field Name="CountryOfOriginLbl">
          <DataField>CountryOfOriginLbl</DataField>
        </Field>
        <Field Name="ContainerNumber">
          <DataField>ContainerNumber</DataField>
        </Field>
        <Field Name="CountryOfOrigin">
          <DataField>CountryOfOrigin</DataField>
        </Field>
        <Field Name="plasticwooddesc">
          <DataField>plasticwooddesc</DataField>
        </Field>
        <Field Name="CurrencyCodeHD">
          <DataField>CurrencyCodeHD</DataField>
        </Field>
        <Field Name="PackagingLbl">
          <DataField>PackagingLbl</DataField>
        </Field>
        <Field Name="TotalChargesLbl">
          <DataField>TotalChargesLbl</DataField>
        </Field>
        <Field Name="NetGoodsLbl">
          <DataField>NetGoodsLbl</DataField>
        </Field>
        <Field Name="TotalAmountLbl">
          <DataField>TotalAmountLbl</DataField>
        </Field>
        <Field Name="CompInfoName">
          <DataField>CompInfoName</DataField>
        </Field>
        <Field Name="CompanyAddress1">
          <DataField>CompanyAddress1</DataField>
        </Field>
        <Field Name="CompanyAddress2">
          <DataField>CompanyAddress2</DataField>
        </Field>
        <Field Name="CompanyAddress3">
          <DataField>CompanyAddress3</DataField>
        </Field>
        <Field Name="CompanyAddress4">
          <DataField>CompanyAddress4</DataField>
        </Field>
        <Field Name="CompanyAddress5">
          <DataField>CompanyAddress5</DataField>
        </Field>
        <Field Name="CompanyAddress6">
          <DataField>CompanyAddress6</DataField>
        </Field>
        <Field Name="CompanyAddress7">
          <DataField>CompanyAddress7</DataField>
        </Field>
        <Field Name="CompanyAddress8">
          <DataField>CompanyAddress8</DataField>
        </Field>
        <Field Name="CompanyHomePage">
          <DataField>CompanyHomePage</DataField>
        </Field>
        <Field Name="CompanyEMail">
          <DataField>CompanyEMail</DataField>
        </Field>
        <Field Name="CompanyPicture">
          <DataField>CompanyPicture</DataField>
        </Field>
        <Field Name="CompanyPhoneNo">
          <DataField>CompanyPhoneNo</DataField>
        </Field>
        <Field Name="CompanyPhoneNo_Lbl">
          <DataField>CompanyPhoneNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyGiroNo">
          <DataField>CompanyGiroNo</DataField>
        </Field>
        <Field Name="CompanyGiroNo_Lbl">
          <DataField>CompanyGiroNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyBankName">
          <DataField>CompanyBankName</DataField>
        </Field>
        <Field Name="CompanyBankName_Lbl">
          <DataField>CompanyBankName_Lbl</DataField>
        </Field>
        <Field Name="CompanyBankBranchNo">
          <DataField>CompanyBankBranchNo</DataField>
        </Field>
        <Field Name="CompanyBankBranchNo_Lbl">
          <DataField>CompanyBankBranchNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyBankAccountNo">
          <DataField>CompanyBankAccountNo</DataField>
        </Field>
        <Field Name="CompanyBankAccountNo_Lbl">
          <DataField>CompanyBankAccountNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyIBAN">
          <DataField>CompanyIBAN</DataField>
        </Field>
        <Field Name="CompanyIBAN_Lbl">
          <DataField>CompanyIBAN_Lbl</DataField>
        </Field>
        <Field Name="CompanySWIFT">
          <DataField>CompanySWIFT</DataField>
        </Field>
        <Field Name="CompanySWIFT_Lbl">
          <DataField>CompanySWIFT_Lbl</DataField>
        </Field>
        <Field Name="CompanyLogoPosition">
          <DataField>CompanyLogoPosition</DataField>
        </Field>
        <Field Name="CompanyRegistrationNumber">
          <DataField>CompanyRegistrationNumber</DataField>
        </Field>
        <Field Name="CompanyRegistrationNumber_Lbl">
          <DataField>CompanyRegistrationNumber_Lbl</DataField>
        </Field>
        <Field Name="CompanyVATRegNo">
          <DataField>CompanyVATRegNo</DataField>
        </Field>
        <Field Name="CompanyVATRegNo_Lbl">
          <DataField>CompanyVATRegNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyVATRegistrationNo">
          <DataField>CompanyVATRegistrationNo</DataField>
        </Field>
        <Field Name="CompanyVATRegistrationNo_Lbl">
          <DataField>CompanyVATRegistrationNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyLegalOffice">
          <DataField>CompanyLegalOffice</DataField>
        </Field>
        <Field Name="CompanyLegalOffice_Lbl">
          <DataField>CompanyLegalOffice_Lbl</DataField>
        </Field>
        <Field Name="CompanyCustomGiro">
          <DataField>CompanyCustomGiro</DataField>
        </Field>
        <Field Name="CompanyCustomGiro_Lbl">
          <DataField>CompanyCustomGiro_Lbl</DataField>
        </Field>
        <Field Name="CompanyLegalStatement">
          <DataField>CompanyLegalStatement</DataField>
        </Field>
        <Field Name="DisplayAdditionalFeeNote">
          <DataField>DisplayAdditionalFeeNote</DataField>
        </Field>
        <Field Name="CustomerAddress1">
          <DataField>CustomerAddress1</DataField>
        </Field>
        <Field Name="CustomerAddress2">
          <DataField>CustomerAddress2</DataField>
        </Field>
        <Field Name="CustomerAddress3">
          <DataField>CustomerAddress3</DataField>
        </Field>
        <Field Name="CustomerAddress4">
          <DataField>CustomerAddress4</DataField>
        </Field>
        <Field Name="CustomerAddress5">
          <DataField>CustomerAddress5</DataField>
        </Field>
        <Field Name="CustomerAddress6">
          <DataField>CustomerAddress6</DataField>
        </Field>
        <Field Name="CustomerAddress7">
          <DataField>CustomerAddress7</DataField>
        </Field>
        <Field Name="CustomerAddress8">
          <DataField>CustomerAddress8</DataField>
        </Field>
        <Field Name="CustomerPostalBarCode">
          <DataField>CustomerPostalBarCode</DataField>
        </Field>
        <Field Name="YourReference">
          <DataField>YourReference</DataField>
        </Field>
        <Field Name="YourReference_Lbl">
          <DataField>YourReference_Lbl</DataField>
        </Field>
        <Field Name="ShipmentMethodDescription">
          <DataField>ShipmentMethodDescription</DataField>
        </Field>
        <Field Name="ShipmentMethodDescription_Lbl">
          <DataField>ShipmentMethodDescription_Lbl</DataField>
        </Field>
        <Field Name="ShipmentDate">
          <DataField>ShipmentDate</DataField>
        </Field>
        <Field Name="ShipmentDate_Lbl">
          <DataField>ShipmentDate_Lbl</DataField>
        </Field>
        <Field Name="Shipment_Lbl">
          <DataField>Shipment_Lbl</DataField>
        </Field>
        <Field Name="ShowShippingAddress">
          <DataField>ShowShippingAddress</DataField>
        </Field>
        <Field Name="ShipToAddress_Lbl">
          <DataField>ShipToAddress_Lbl</DataField>
        </Field>
        <Field Name="ShipToAddress1">
          <DataField>ShipToAddress1</DataField>
        </Field>
        <Field Name="ShipToAddress2">
          <DataField>ShipToAddress2</DataField>
        </Field>
        <Field Name="ShipToAddress3">
          <DataField>ShipToAddress3</DataField>
        </Field>
        <Field Name="ShipToAddress4">
          <DataField>ShipToAddress4</DataField>
        </Field>
        <Field Name="ShipToAddress5">
          <DataField>ShipToAddress5</DataField>
        </Field>
        <Field Name="ShipToAddress6">
          <DataField>ShipToAddress6</DataField>
        </Field>
        <Field Name="ShipToAddress7">
          <DataField>ShipToAddress7</DataField>
        </Field>
        <Field Name="ShipToAddress8">
          <DataField>ShipToAddress8</DataField>
        </Field>
        <Field Name="SellToContactPhoneNoLbl">
          <DataField>SellToContactPhoneNoLbl</DataField>
        </Field>
        <Field Name="SellToContactMobilePhoneNoLbl">
          <DataField>SellToContactMobilePhoneNoLbl</DataField>
        </Field>
        <Field Name="SellToContactEmailLbl">
          <DataField>SellToContactEmailLbl</DataField>
        </Field>
        <Field Name="BillToContactPhoneNoLbl">
          <DataField>BillToContactPhoneNoLbl</DataField>
        </Field>
        <Field Name="BillToContactMobilePhoneNoLbl">
          <DataField>BillToContactMobilePhoneNoLbl</DataField>
        </Field>
        <Field Name="BillToContactEmailLbl">
          <DataField>BillToContactEmailLbl</DataField>
        </Field>
        <Field Name="SellToContactPhoneNo">
          <DataField>SellToContactPhoneNo</DataField>
        </Field>
        <Field Name="SellToContactMobilePhoneNo">
          <DataField>SellToContactMobilePhoneNo</DataField>
        </Field>
        <Field Name="SellToContactEmail">
          <DataField>SellToContactEmail</DataField>
        </Field>
        <Field Name="BillToContactPhoneNo">
          <DataField>BillToContactPhoneNo</DataField>
        </Field>
        <Field Name="BillToContactMobilePhoneNo">
          <DataField>BillToContactMobilePhoneNo</DataField>
        </Field>
        <Field Name="BillToContactEmail">
          <DataField>BillToContactEmail</DataField>
        </Field>
        <Field Name="PaymentTermsDescription">
          <DataField>PaymentTermsDescription</DataField>
        </Field>
        <Field Name="PaymentTermsDescription_Lbl">
          <DataField>PaymentTermsDescription_Lbl</DataField>
        </Field>
        <Field Name="PaymentMethodDescription">
          <DataField>PaymentMethodDescription</DataField>
        </Field>
        <Field Name="PaymentMethodDescription_Lbl">
          <DataField>PaymentMethodDescription_Lbl</DataField>
        </Field>
        <Field Name="BilltoCustumerNo">
          <DataField>BilltoCustumerNo</DataField>
        </Field>
        <Field Name="BilltoCustomerNo_Lbl">
          <DataField>BilltoCustomerNo_Lbl</DataField>
        </Field>
        <Field Name="PostingDate">
          <DataField>PostingDate</DataField>
        </Field>
        <Field Name="DocumentDate_Lbl">
          <DataField>DocumentDate_Lbl</DataField>
        </Field>
        <Field Name="DueDate">
          <DataField>DueDate</DataField>
        </Field>
        <Field Name="DueDate_Lbl">
          <DataField>DueDate_Lbl</DataField>
        </Field>
        <Field Name="DocumentNo">
          <DataField>DocumentNo</DataField>
        </Field>
        <Field Name="DocumentNo_Lbl">
          <DataField>DocumentNo_Lbl</DataField>
        </Field>
        <Field Name="OurOrderNo">
          <DataField>OurOrderNo</DataField>
        </Field>
        <Field Name="OrderNo_Lbl">
          <DataField>OrderNo_Lbl</DataField>
        </Field>
        <Field Name="CustOrderNo">
          <DataField>CustOrderNo</DataField>
        </Field>
        <Field Name="PricesIncludingVAT">
          <DataField>PricesIncludingVAT</DataField>
        </Field>
        <Field Name="PricesIncludingVAT_Lbl">
          <DataField>PricesIncludingVAT_Lbl</DataField>
        </Field>
        <Field Name="PricesIncludingVATYesNo">
          <DataField>PricesIncludingVATYesNo</DataField>
        </Field>
        <Field Name="SalesPerson_Lbl">
          <DataField>SalesPerson_Lbl</DataField>
        </Field>
        <Field Name="SalesPersonBlank_Lbl">
          <DataField>SalesPersonBlank_Lbl</DataField>
        </Field>
        <Field Name="SalesPersonName">
          <DataField>SalesPersonName</DataField>
        </Field>
        <Field Name="SelltoCustomerNo">
          <DataField>SelltoCustomerNo</DataField>
        </Field>
        <Field Name="SelltoCustomerNo_Lbl">
          <DataField>SelltoCustomerNo_Lbl</DataField>
        </Field>
        <Field Name="VATRegistrationNo">
          <DataField>VATRegistrationNo</DataField>
        </Field>
        <Field Name="VATRegistrationNo_Lbl">
          <DataField>VATRegistrationNo_Lbl</DataField>
        </Field>
        <Field Name="GlobalLocationNumber">
          <DataField>GlobalLocationNumber</DataField>
        </Field>
        <Field Name="GlobalLocationNumber_Lbl">
          <DataField>GlobalLocationNumber_Lbl</DataField>
        </Field>
        <Field Name="SellToFaxNo">
          <DataField>SellToFaxNo</DataField>
        </Field>
        <Field Name="SellToPhoneNo">
          <DataField>SellToPhoneNo</DataField>
        </Field>
        <Field Name="PaymentReference">
          <DataField>PaymentReference</DataField>
        </Field>
        <Field Name="From_Lbl">
          <DataField>From_Lbl</DataField>
        </Field>
        <Field Name="BilledTo_Lbl">
          <DataField>BilledTo_Lbl</DataField>
        </Field>
        <Field Name="ChecksPayable_Lbl">
          <DataField>ChecksPayable_Lbl</DataField>
        </Field>
        <Field Name="PaymentReference_Lbl">
          <DataField>PaymentReference_Lbl</DataField>
        </Field>
        <Field Name="LegalEntityType">
          <DataField>LegalEntityType</DataField>
        </Field>
        <Field Name="LegalEntityType_Lbl">
          <DataField>LegalEntityType_Lbl</DataField>
        </Field>
        <Field Name="Copy_Lbl">
          <DataField>Copy_Lbl</DataField>
        </Field>
        <Field Name="EMail_Header_Lbl">
          <DataField>EMail_Header_Lbl</DataField>
        </Field>
        <Field Name="HomePage_Header_Lbl">
          <DataField>HomePage_Header_Lbl</DataField>
        </Field>
        <Field Name="InvoiceDiscountBaseAmount_Lbl">
          <DataField>InvoiceDiscountBaseAmount_Lbl</DataField>
        </Field>
        <Field Name="InvoiceDiscountAmount_Lbl">
          <DataField>InvoiceDiscountAmount_Lbl</DataField>
        </Field>
        <Field Name="LineAmountAfterInvoiceDiscount_Lbl">
          <DataField>LineAmountAfterInvoiceDiscount_Lbl</DataField>
        </Field>
        <Field Name="LocalCurrency_Lbl">
          <DataField>LocalCurrency_Lbl</DataField>
        </Field>
        <Field Name="ExchangeRateAsText">
          <DataField>ExchangeRateAsText</DataField>
        </Field>
        <Field Name="Page_Lbl">
          <DataField>Page_Lbl</DataField>
        </Field>
        <Field Name="SalesInvoiceLineDiscount_Lbl">
          <DataField>SalesInvoiceLineDiscount_Lbl</DataField>
        </Field>
        <Field Name="Questions_Lbl">
          <DataField>Questions_Lbl</DataField>
        </Field>
        <Field Name="Contact_Lbl">
          <DataField>Contact_Lbl</DataField>
        </Field>
        <Field Name="DocumentTitle_Lbl">
          <DataField>DocumentTitle_Lbl</DataField>
        </Field>
        <Field Name="YourDocumentTitle_Lbl">
          <DataField>YourDocumentTitle_Lbl</DataField>
        </Field>
        <Field Name="Thanks_Lbl">
          <DataField>Thanks_Lbl</DataField>
        </Field>
        <Field Name="ShowWorkDescription">
          <DataField>ShowWorkDescription</DataField>
        </Field>
        <Field Name="RemainingAmount">
          <DataField>RemainingAmount</DataField>
        </Field>
        <Field Name="RemainingAmountFormat">
          <DataField>RemainingAmountFormat</DataField>
        </Field>
        <Field Name="RemainingAmountText">
          <DataField>RemainingAmountText</DataField>
        </Field>
        <Field Name="Subtotal_Lbl">
          <DataField>Subtotal_Lbl</DataField>
        </Field>
        <Field Name="Total_Lbl">
          <DataField>Total_Lbl</DataField>
        </Field>
        <Field Name="VATAmount_Lbl">
          <DataField>VATAmount_Lbl</DataField>
        </Field>
        <Field Name="VATBase_Lbl">
          <DataField>VATBase_Lbl</DataField>
        </Field>
        <Field Name="VATAmountSpecification_Lbl">
          <DataField>VATAmountSpecification_Lbl</DataField>
        </Field>
        <Field Name="VATClauses_Lbl">
          <DataField>VATClauses_Lbl</DataField>
        </Field>
        <Field Name="VATIdentifier_Lbl">
          <DataField>VATIdentifier_Lbl</DataField>
        </Field>
        <Field Name="VATPercentage_Lbl">
          <DataField>VATPercentage_Lbl</DataField>
        </Field>
        <Field Name="VATClause_Lbl">
          <DataField>VATClause_Lbl</DataField>
        </Field>
        <Field Name="PackageTrackingNo">
          <DataField>PackageTrackingNo</DataField>
        </Field>
        <Field Name="PackageTrackingNo_Lbl">
          <DataField>PackageTrackingNo_Lbl</DataField>
        </Field>
        <Field Name="ShippingAgentCode">
          <DataField>ShippingAgentCode</DataField>
        </Field>
        <Field Name="ShippingAgentCode_Lbl">
          <DataField>ShippingAgentCode_Lbl</DataField>
        </Field>
        <Field Name="PaymentInstructions_Txt">
          <DataField>PaymentInstructions_Txt</DataField>
        </Field>
        <Field Name="ExternalDocumentNo">
          <DataField>ExternalDocumentNo</DataField>
        </Field>
        <Field Name="ExternalDocumentNo_Lbl">
          <DataField>ExternalDocumentNo_Lbl</DataField>
        </Field>
        <Field Name="Comment_SalesCommentLine">
          <DataField>Comment_SalesCommentLine</DataField>
        </Field>
        <Field Name="LineNo_Line">
          <DataField>LineNo_Line</DataField>
        </Field>
        <Field Name="LineNumberNo_Line">
          <DataField>LineNumberNo_Line</DataField>
        </Field>
        <Field Name="AmountExcludingVAT_Line">
          <DataField>AmountExcludingVAT_Line</DataField>
        </Field>
        <Field Name="AmountExcludingVAT_LineFormat">
          <DataField>AmountExcludingVAT_LineFormat</DataField>
        </Field>
        <Field Name="AmountExcludingVAT_Line_Lbl">
          <DataField>AmountExcludingVAT_Line_Lbl</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_Line">
          <DataField>AmountIncludingVAT_Line</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_LineFormat">
          <DataField>AmountIncludingVAT_LineFormat</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_Line_Lbl">
          <DataField>AmountIncludingVAT_Line_Lbl</DataField>
        </Field>
        <Field Name="Description_Line">
          <DataField>Description_Line</DataField>
        </Field>
        <Field Name="LineDiscountPercent_Line">
          <DataField>LineDiscountPercent_Line</DataField>
        </Field>
        <Field Name="LineDiscountPercent_LineFormat">
          <DataField>LineDiscountPercent_LineFormat</DataField>
        </Field>
        <Field Name="LineDiscountPercentText_Line">
          <DataField>LineDiscountPercentText_Line</DataField>
        </Field>
        <Field Name="LineAmount_Line">
          <DataField>LineAmount_Line</DataField>
        </Field>
        <Field Name="ItemNo_Line">
          <DataField>ItemNo_Line</DataField>
        </Field>
        <Field Name="ItemReferenceNo_Line">
          <DataField>ItemReferenceNo_Line</DataField>
        </Field>
        <Field Name="ItemReferenceNo_Line_Lbl">
          <DataField>ItemReferenceNo_Line_Lbl</DataField>
        </Field>
        <Field Name="ShipmentDate_Line">
          <DataField>ShipmentDate_Line</DataField>
        </Field>
        <Field Name="ShipmentDate_Line_Lbl">
          <DataField>ShipmentDate_Line_Lbl</DataField>
        </Field>
        <Field Name="Quantity_Line">
          <DataField>Quantity_Line</DataField>
        </Field>
        <Field Name="Type_Line">
          <DataField>Type_Line</DataField>
        </Field>
        <Field Name="UnitPrice">
          <DataField>UnitPrice</DataField>
        </Field>
        <Field Name="UnitOfMeasure">
          <DataField>UnitOfMeasure</DataField>
        </Field>
        <Field Name="VATIdentifier_Line">
          <DataField>VATIdentifier_Line</DataField>
        </Field>
        <Field Name="VATIdentifier_Line_Lbl">
          <DataField>VATIdentifier_Line_Lbl</DataField>
        </Field>
        <Field Name="VATPct_Line">
          <DataField>VATPct_Line</DataField>
        </Field>
        <Field Name="VATPct_Line_Lbl">
          <DataField>VATPct_Line_Lbl</DataField>
        </Field>
        <Field Name="TransHeaderAmount">
          <DataField>TransHeaderAmount</DataField>
        </Field>
        <Field Name="TransHeaderAmountFormat">
          <DataField>TransHeaderAmountFormat</DataField>
        </Field>
        <Field Name="JobTaskNo_Lbl">
          <DataField>JobTaskNo_Lbl</DataField>
        </Field>
        <Field Name="JobTaskNo">
          <DataField>JobTaskNo</DataField>
        </Field>
        <Field Name="JobTaskDescription">
          <DataField>JobTaskDescription</DataField>
        </Field>
        <Field Name="JobTaskDesc_Lbl">
          <DataField>JobTaskDesc_Lbl</DataField>
        </Field>
        <Field Name="JobNo_Lbl">
          <DataField>JobNo_Lbl</DataField>
        </Field>
        <Field Name="JobNo">
          <DataField>JobNo</DataField>
        </Field>
        <Field Name="Unit_Lbl">
          <DataField>Unit_Lbl</DataField>
        </Field>
        <Field Name="Qty_Lbl">
          <DataField>Qty_Lbl</DataField>
        </Field>
        <Field Name="Price_Lbl">
          <DataField>Price_Lbl</DataField>
        </Field>
        <Field Name="PricePer_Lbl">
          <DataField>PricePer_Lbl</DataField>
        </Field>
        <Field Name="DocumentNo_ShipmentLine">
          <DataField>DocumentNo_ShipmentLine</DataField>
        </Field>
        <Field Name="PostingDate_ShipmentLine">
          <DataField>PostingDate_ShipmentLine</DataField>
        </Field>
        <Field Name="PostingDate_ShipmentLine_Lbl">
          <DataField>PostingDate_ShipmentLine_Lbl</DataField>
        </Field>
        <Field Name="Quantity_ShipmentLine">
          <DataField>Quantity_ShipmentLine</DataField>
        </Field>
        <Field Name="Quantity_ShipmentLineFormat">
          <DataField>Quantity_ShipmentLineFormat</DataField>
        </Field>
        <Field Name="Quantity_ShipmentLine_Lbl">
          <DataField>Quantity_ShipmentLine_Lbl</DataField>
        </Field>
        <Field Name="LineNo_AssemblyLine">
          <DataField>LineNo_AssemblyLine</DataField>
        </Field>
        <Field Name="Description_AssemblyLine">
          <DataField>Description_AssemblyLine</DataField>
        </Field>
        <Field Name="Quantity_AssemblyLine">
          <DataField>Quantity_AssemblyLine</DataField>
        </Field>
        <Field Name="Quantity_AssemblyLineFormat">
          <DataField>Quantity_AssemblyLineFormat</DataField>
        </Field>
        <Field Name="UnitOfMeasure_AssemblyLine">
          <DataField>UnitOfMeasure_AssemblyLine</DataField>
        </Field>
        <Field Name="VariantCode_AssemblyLine">
          <DataField>VariantCode_AssemblyLine</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_Tariff_No">
          <DataField>TmpSalesLineGTIP_Tariff_No</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_Quantity">
          <DataField>TmpSalesLineGTIP_Quantity</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_QuantityFormat">
          <DataField>TmpSalesLineGTIP_QuantityFormat</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_QtytoShip">
          <DataField>TmpSalesLineGTIP_QtytoShip</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_QtytoShipFormat">
          <DataField>TmpSalesLineGTIP_QtytoShipFormat</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_NetWeight">
          <DataField>TmpSalesLineGTIP_NetWeight</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_NetWeightFormat">
          <DataField>TmpSalesLineGTIP_NetWeightFormat</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_GrossWeight">
          <DataField>TmpSalesLineGTIP_GrossWeight</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_GrossWeightFormat">
          <DataField>TmpSalesLineGTIP_GrossWeightFormat</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_LineAmount">
          <DataField>TmpSalesLineGTIP_LineAmount</DataField>
        </Field>
        <Field Name="TmpSalesLineGTIP_LineAmountFormat">
          <DataField>TmpSalesLineGTIP_LineAmountFormat</DataField>
        </Field>
        <Field Name="WorkDescriptionLineNumber">
          <DataField>WorkDescriptionLineNumber</DataField>
        </Field>
        <Field Name="WorkDescriptionLine">
          <DataField>WorkDescriptionLine</DataField>
        </Field>
        <Field Name="InvoiceDiscountAmount_VATAmountLine">
          <DataField>InvoiceDiscountAmount_VATAmountLine</DataField>
        </Field>
        <Field Name="InvoiceDiscountAmount_VATAmountLineFormat">
          <DataField>InvoiceDiscountAmount_VATAmountLineFormat</DataField>
        </Field>
        <Field Name="InvoiceDiscountAmount_VATAmountLine_Lbl">
          <DataField>InvoiceDiscountAmount_VATAmountLine_Lbl</DataField>
        </Field>
        <Field Name="InvoiceDiscountBaseAmount_VATAmountLine">
          <DataField>InvoiceDiscountBaseAmount_VATAmountLine</DataField>
        </Field>
        <Field Name="InvoiceDiscountBaseAmount_VATAmountLineFormat">
          <DataField>InvoiceDiscountBaseAmount_VATAmountLineFormat</DataField>
        </Field>
        <Field Name="InvoiceDiscountBaseAmount_VATAmountLine_Lbl">
          <DataField>InvoiceDiscountBaseAmount_VATAmountLine_Lbl</DataField>
        </Field>
        <Field Name="LineAmount_VatAmountLine">
          <DataField>LineAmount_VatAmountLine</DataField>
        </Field>
        <Field Name="LineAmount_VatAmountLineFormat">
          <DataField>LineAmount_VatAmountLineFormat</DataField>
        </Field>
        <Field Name="LineAmount_VatAmountLine_Lbl">
          <DataField>LineAmount_VatAmountLine_Lbl</DataField>
        </Field>
        <Field Name="VATAmount_VatAmountLine">
          <DataField>VATAmount_VatAmountLine</DataField>
        </Field>
        <Field Name="VATAmount_VatAmountLineFormat">
          <DataField>VATAmount_VatAmountLineFormat</DataField>
        </Field>
        <Field Name="VATAmount_VatAmountLine_Lbl">
          <DataField>VATAmount_VatAmountLine_Lbl</DataField>
        </Field>
        <Field Name="VATAmountLCY_VATAmountLine">
          <DataField>VATAmountLCY_VATAmountLine</DataField>
        </Field>
        <Field Name="VATAmountLCY_VATAmountLineFormat">
          <DataField>VATAmountLCY_VATAmountLineFormat</DataField>
        </Field>
        <Field Name="VATAmountLCY_VATAmountLine_Lbl">
          <DataField>VATAmountLCY_VATAmountLine_Lbl</DataField>
        </Field>
        <Field Name="VATBase_VatAmountLine">
          <DataField>VATBase_VatAmountLine</DataField>
        </Field>
        <Field Name="VATBase_VatAmountLineFormat">
          <DataField>VATBase_VatAmountLineFormat</DataField>
        </Field>
        <Field Name="VATBase_VatAmountLine_Lbl">
          <DataField>VATBase_VatAmountLine_Lbl</DataField>
        </Field>
        <Field Name="VATBaseLCY_VATAmountLine">
          <DataField>VATBaseLCY_VATAmountLine</DataField>
        </Field>
        <Field Name="VATBaseLCY_VATAmountLineFormat">
          <DataField>VATBaseLCY_VATAmountLineFormat</DataField>
        </Field>
        <Field Name="VATBaseLCY_VATAmountLine_Lbl">
          <DataField>VATBaseLCY_VATAmountLine_Lbl</DataField>
        </Field>
        <Field Name="VATIdentifier_VatAmountLine">
          <DataField>VATIdentifier_VatAmountLine</DataField>
        </Field>
        <Field Name="VATIdentifier_VatAmountLine_Lbl">
          <DataField>VATIdentifier_VatAmountLine_Lbl</DataField>
        </Field>
        <Field Name="VATPct_VatAmountLine">
          <DataField>VATPct_VatAmountLine</DataField>
        </Field>
        <Field Name="VATPct_VatAmountLineFormat">
          <DataField>VATPct_VatAmountLineFormat</DataField>
        </Field>
        <Field Name="VATPct_VatAmountLine_Lbl">
          <DataField>VATPct_VatAmountLine_Lbl</DataField>
        </Field>
        <Field Name="NoOfVATIdentifiers">
          <DataField>NoOfVATIdentifiers</DataField>
        </Field>
        <Field Name="VATClausesHeader">
          <DataField>VATClausesHeader</DataField>
        </Field>
        <Field Name="VATIdentifier_VATClauseLine">
          <DataField>VATIdentifier_VATClauseLine</DataField>
        </Field>
        <Field Name="Code_VATClauseLine">
          <DataField>Code_VATClauseLine</DataField>
        </Field>
        <Field Name="Code_VATClauseLine_Lbl">
          <DataField>Code_VATClauseLine_Lbl</DataField>
        </Field>
        <Field Name="Description_VATClauseLine">
          <DataField>Description_VATClauseLine</DataField>
        </Field>
        <Field Name="Description2_VATClauseLine">
          <DataField>Description2_VATClauseLine</DataField>
        </Field>
        <Field Name="VATAmount_VATClauseLine">
          <DataField>VATAmount_VATClauseLine</DataField>
        </Field>
        <Field Name="VATAmount_VATClauseLineFormat">
          <DataField>VATAmount_VATClauseLineFormat</DataField>
        </Field>
        <Field Name="NoOfVATClauses">
          <DataField>NoOfVATClauses</DataField>
        </Field>
        <Field Name="Description_ReportTotalsLine">
          <DataField>Description_ReportTotalsLine</DataField>
        </Field>
        <Field Name="Amount_ReportTotalsLine">
          <DataField>Amount_ReportTotalsLine</DataField>
        </Field>
        <Field Name="Amount_ReportTotalsLineFormat">
          <DataField>Amount_ReportTotalsLineFormat</DataField>
        </Field>
        <Field Name="AmountFormatted_ReportTotalsLine">
          <DataField>AmountFormatted_ReportTotalsLine</DataField>
        </Field>
        <Field Name="FontBold_ReportTotalsLine">
          <DataField>FontBold_ReportTotalsLine</DataField>
        </Field>
        <Field Name="FontUnderline_ReportTotalsLine">
          <DataField>FontUnderline_ReportTotalsLine</DataField>
        </Field>
        <Field Name="LineFeeCaptionText">
          <DataField>LineFeeCaptionText</DataField>
        </Field>
        <Field Name="LeftHeaderName">
          <DataField>LeftHeaderName</DataField>
        </Field>
        <Field Name="LeftHeaderValue">
          <DataField>LeftHeaderValue</DataField>
        </Field>
        <Field Name="RightHeaderName">
          <DataField>RightHeaderName</DataField>
        </Field>
        <Field Name="RightHeaderValue">
          <DataField>RightHeaderValue</DataField>
        </Field>
        <Field Name="GreetingText">
          <DataField>GreetingText</DataField>
        </Field>
        <Field Name="BodyText">
          <DataField>BodyText</DataField>
        </Field>
        <Field Name="ClosingText">
          <DataField>ClosingText</DataField>
        </Field>
        <Field Name="PmtDiscText">
          <DataField>PmtDiscText</DataField>
        </Field>
        <Field Name="TotalNetAmount">
          <DataField>TotalNetAmount</DataField>
        </Field>
        <Field Name="TotalVATBaseLCY">
          <DataField>TotalVATBaseLCY</DataField>
        </Field>
        <Field Name="TotalVATBaseLCYFormat">
          <DataField>TotalVATBaseLCYFormat</DataField>
        </Field>
        <Field Name="TotalAmountIncludingVAT">
          <DataField>TotalAmountIncludingVAT</DataField>
        </Field>
        <Field Name="TotalVATAmount">
          <DataField>TotalVATAmount</DataField>
        </Field>
        <Field Name="TotalVATAmountLCY">
          <DataField>TotalVATAmountLCY</DataField>
        </Field>
        <Field Name="TotalVATAmountLCYFormat">
          <DataField>TotalVATAmountLCYFormat</DataField>
        </Field>
        <Field Name="TotalInvoiceDiscountAmount">
          <DataField>TotalInvoiceDiscountAmount</DataField>
        </Field>
        <Field Name="TotalPaymentDiscountOnVAT">
          <DataField>TotalPaymentDiscountOnVAT</DataField>
        </Field>
        <Field Name="TotalPaymentDiscountOnVATFormat">
          <DataField>TotalPaymentDiscountOnVATFormat</DataField>
        </Field>
        <Field Name="TotalVATAmountText">
          <DataField>TotalVATAmountText</DataField>
        </Field>
        <Field Name="TotalExcludingVATText">
          <DataField>TotalExcludingVATText</DataField>
        </Field>
        <Field Name="TotalIncludingVATText">
          <DataField>TotalIncludingVATText</DataField>
        </Field>
        <Field Name="TotalSubTotal">
          <DataField>TotalSubTotal</DataField>
        </Field>
        <Field Name="TotalSubTotalMinusInvoiceDiscount">
          <DataField>TotalSubTotalMinusInvoiceDiscount</DataField>
        </Field>
        <Field Name="TotalText">
          <DataField>TotalText</DataField>
        </Field>
        <Field Name="TotalAmountExclInclVAT">
          <DataField>TotalAmountExclInclVAT</DataField>
        </Field>
        <Field Name="TotalAmountExclInclVATText">
          <DataField>TotalAmountExclInclVATText</DataField>
        </Field>
        <Field Name="TotalVATBaseOnVATAmtLine">
          <DataField>TotalVATBaseOnVATAmtLine</DataField>
        </Field>
        <Field Name="TotalVATBaseOnVATAmtLineFormat">
          <DataField>TotalVATBaseOnVATAmtLineFormat</DataField>
        </Field>
        <Field Name="TotalVATAmountOnVATAmtLine">
          <DataField>TotalVATAmountOnVATAmtLine</DataField>
        </Field>
        <Field Name="TotalVATAmountOnVATAmtLineFormat">
          <DataField>TotalVATAmountOnVATAmtLineFormat</DataField>
        </Field>
        <Field Name="CurrencyCode">
          <DataField>CurrencyCode</DataField>
        </Field>
        <Field Name="CurrencySymbol">
          <DataField>CurrencySymbol</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>
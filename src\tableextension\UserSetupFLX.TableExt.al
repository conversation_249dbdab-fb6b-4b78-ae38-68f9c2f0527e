tableextension 60019 "User Setup FLX" extends "User Setup"
{
    fields
    {
        field(60000; "Package Trns. Batch Name FLX"; Code[10])
        {
            Caption = 'Package Transfer Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Trns. Temp. Name FLX"));
            ToolTip = 'Specifies the value of the Package Transfer Batch Name field.';
        }
        field(60001; "Package Trns. Temp. Name FLX"; Code[10])
        {
            Caption = 'Package Transfer Template Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Flexati Setup FLX"."Package Transfer Template Name");
            ToolTip = 'Specifies the value of the Package Transfer Template Name field.';
        }
    }
}
page 60014 "Quality Control FLX"
{
    ApplicationArea = All;
    Caption = 'Quality Control';
    PageType = Document;
    SourceTable = "Quality Control Header FLX";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus FLX")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Label Text');
                end;
            }

            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field(Date; Rec.Date)
                {
                }
                field(Posted; Rec.Posted)
                {
                }
                field("Package Count"; Rec."Package Count")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
            group(LabelReading)
            {
                Caption = 'Label Reading';
                Editable = not Rec.Posted;
                field("Label Text"; Rec."Label Text")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Label Text');
                    end;
                }
            }
            part(Lines; "Quality Control Subpage FLX")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = not Rec.Posted;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Post)
            {
                ApplicationArea = All;
                Caption = 'Post';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostDocument;
                ToolTip = 'Executes the Post action.';
                PromotedOnly = true;
                trigger OnAction()
                begin
                    QualityControlManagement.PostQualityControlDocument(Rec);
                    CurrPage.Close();
                end;
            }
        }
    }
    var
        QualityControlManagement: Codeunit "Quality Control Management FLX";
}
page 60010 "Combined Shipment Lines FLX"
{
    ApplicationArea = All;
    Caption = 'Lines';
    PageType = ListPart;
    SourceTable = "Combined Shipment Line FLX";
    DeleteAllowed = false;
    InsertAllowed = false;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Source Document No."; Rec."Source Document No.")
                {
                    Editable = false;
                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                    begin
                        SalesHeader.Get(SalesHeader."Document Type"::Order, Rec."Source Document No.");
                        PageManagement.PageRun(SalesHeader);
                        //Page.Run(Page::"Sales Order", SalesHeader);
                    end;
                }
                field("Source Document Line No."; Rec."Source Document Line No.")
                {
                    Editable = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                    Visible = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field("Unit Price"; Rec."Unit Price")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                    Editable = false;
                }
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field("Quantity (Base)"; Rec."Quantity (Base)")
                {
                    Editable = false;
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                }
                field("Qty. Shipped (Base)"; Rec."Qty. Shipped (Base)")
                {
                    Editable = false;
                }
                field("Outstanding Qty. (Base)"; Rec."Outstanding Qty. (Base)")
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    Visible = false;
                }
                field("Qty. to Ship (Base)"; Rec."Qty. to Ship (Base)")
                {
                    Editable = false;
                }
                field("Package Count"; Rec."Package Count")
                {
                    Editable = false;
                }
            }
        }
    }
    var
        PageManagement: Codeunit "Page Management";
}
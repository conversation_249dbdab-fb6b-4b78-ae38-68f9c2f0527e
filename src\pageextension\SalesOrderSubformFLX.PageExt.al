pageextension 60009 "Sales Order Subform FLX" extends "Sales Order Subform"
{
    layout
    {
        addafter("No.")
        {
            field("Custom Code FLX"; Rec."Custom Code FLX")
            {
                ApplicationArea = All;
            }
        }
        addbefore(Quantity)
        {
            field("Piece FLX"; Rec."Piece FLX")
            {
                ApplicationArea = All;
            }
            field("Hose Lenght FLX"; Rec."Hose Length FLX")
            {
                ApplicationArea = All;
            }
            field("Coil Weight FLX"; Rec."Unit Weight (Base) FLX")
            {
                ApplicationArea = All;
            }
            field("Flexati Shipment Date FLX"; Rec."Flexati Shipment Date FLX")
            {
                ApplicationArea = All;
            }
            field("Your Reference FLX"; Rec."Your Reference FLX")
            {
                ApplicationArea = All;
            }
        }
    }
}
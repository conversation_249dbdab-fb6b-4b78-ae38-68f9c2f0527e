page 60039 "Item Attribute Value Mapping"
{
    ApplicationArea = All;
    Caption = 'Item Attribute Value Mapping';
    PageType = List;
    SourceTable = "Item Attribute Value Mapping";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Table ID"; Rec."Table ID")
                {
                    ToolTip = 'Specifies the table ID.';
                }
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the number of the item attribute value mapping.';
                }
                field("Item Attribute ID"; Rec."Item Attribute ID")
                {
                    ToolTip = 'Specifies the item attribute ID.';
                }
                field("Item Attribute Name FLX"; Rec."Item Attribute Name FLX")
                {
                }
                field("Item Attribute Value ID"; Rec."Item Attribute Value ID")
                {
                    ToolTip = 'Specifies the item attribute value ID.';
                }
                field("Item Attribute Value FLX"; Rec."Item Attribute Value FLX")
                {
                }
            }
        }
    }
}
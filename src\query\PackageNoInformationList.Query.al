query 60001 "Package No. Information List"
{
    Caption = 'Package No. Information List';
    QueryType = Normal;
    DataAccessIntent = ReadOnly;

    elements
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            column(Package_No_; "Package No.")
            {

            }

            column(Parent_Package_No__FLX; "Parent Package No. FLX")
            {

            }

            column(Lot_No__FLX; "Lot No. FLX")
            {

            }

            column(Quality_Control_Status_FLX; "Quality Control Status FLX")
            {

            }

            column(Inventory; Inventory)
            {
                ColumnFilter = Inventory = filter(> 0);
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}
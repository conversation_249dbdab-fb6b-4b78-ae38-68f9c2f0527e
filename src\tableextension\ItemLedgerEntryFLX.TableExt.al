tableextension 60012 "Item Ledger Entry FLX" extends "Item Ledger Entry"
{
    fields
    {
        field(60000; "Include In Calcualtion FLX"; Boolean)
        {
            AllowInCustomizations = Always;
            Caption = 'Include In Calcualtion';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Include In Calculation FLX" where(Code = field("Location Code")));
        }
        field(60001; "Subcontractor Location FLX"; Boolean)
        {
            AllowInCustomizations = Always;
            Caption = 'Subcontractor Location';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Subcontractor Location FLX" where(Code = field("Location Code")));
        }
        field(60002; "Exclude From Shortage List FLX"; Boolean)
        {
            AllowInCustomizations = Always;
            Caption = 'Exclude From Shortage List';
        }
        field(60003; "Invoice External Doc. No. FLX"; Code[50])
        {
            Caption = 'Invoice External Doc. No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Value Entry"."External Document No." where("Item Ledger Entry No." = field("Entry No."), "Document Type" = const("Sales Invoice")));
            ToolTip = 'Specifies the value of the Invoice External Doc. No. field.';
        }
        field(60004; "Source Package No. FLX"; Code[50])
        {
            Caption = 'Source Package No.';
            ToolTip = 'Specifies the value of the Source Package No. field.';
        }
    }

    keys
    {
        key("key26 INF"; "Item No.", "Variant Code", "Package No.", Open)
        {
        }

        key("Key27 INF"; "Item No.", "Variant Code", "Package No.", "Location Code")
        {
            SumIndexFields = Quantity;
        }
    }
}
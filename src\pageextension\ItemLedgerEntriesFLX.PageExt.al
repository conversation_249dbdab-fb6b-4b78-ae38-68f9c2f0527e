pageextension 60015 "Item Ledger Entries FLX" extends "Item Ledger Entries"
{
    layout
    {
        modify("Lot No.")
        {
            Visible = true;
        }
        addafter("Document No.")
        {
            field("Invoice External Doc. No. FLX"; Rec."Invoice External Doc. No. FLX")
            {
                ApplicationArea = All;
            }
            field("Item Category Code FLX"; Rec."Item Category Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Item Category Code field.';
            }
            field("Source Package No. FLX"; Rec."Source Package No. FLX")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        addafter("&Navigate")
        {
            action("Item Lot No Barcode FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Item Lot No Label';
                Image = Print;
                ToolTip = 'Item Lot No Label.';

                trigger OnAction()
                var
                    rpTrSpec: Report "Tracking Spec Barcode FLX";
                begin
                    rpTrSpec.SetVar(Rec."Item No.", Rec."Lot No.");
                    rpTrSpec.RunModal();
                end;
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey("Posting Date", "Document No.", "Order Line No.");
        Rec.Ascending(false);
    end;
}
pageextension 60001 "Package No. Info. Card FLX" extends "Package No. Information Card"
{
    layout
    {
        modify(General)
        {
            Editable = false;
        }
        addafter(General)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus FLX")
            {
                ApplicationArea = All;
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode Text FLX');
                end;
            }

            group("LabelReading FLX")
            {
                Caption = 'Label Reading';
                Visible = (Rec."Package Type FLX" = Rec."Package Type FLX"::Palette) or
                            (Rec."Package Type FLX" = Rec."Package Type FLX"::Bulk);
                field("Remove Package FLX"; Rec."Remove Package FLX")
                {
                    ApplicationArea = All;
                }
                field("Barcode Text FLX"; Rec."Barcode Text FLX")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode Text FLX');
                    end;
                }
            }
            part(Packages; "Package No. Info. Subpage FLX")
            {
                Caption = 'Lines';
                SubPageLink = "Parent Package No. FLX" = field("Package No.");
                ApplicationArea = All;
                Visible = (Rec."Package Type FLX" = Rec."Package Type FLX"::Palette)
                        or (Rec."Package Type FLX" = Rec."Package Type FLX"::Bulk);
            }
        }
        addafter("Package No.")
        {

            field("Old Package No. FLX"; Rec."Old Package No. FLX")
            {
                ApplicationArea = All;
            }
            field("Package Type FLX"; Rec."Package Type FLX")
            {
                ApplicationArea = All;
            }

            field("Child Package Count FLX"; Rec."Child Package Count FLX")
            {
                ApplicationArea = All;
            }
            field("Parent Package No. FLX"; Rec."Parent Package No. FLX")
            {
                ApplicationArea = All;
            }


        }
        addlast(content)
        {
            group("Flexati FLX")
            {
                Caption = 'Flexati';
                field("Shipment No FLX"; Rec."Shipment No FLX")
                {
                    ApplicationArea = All;
                }
                field("Palette Item No. FLX"; Rec."Palette Item No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Description FLX"; Rec.Description)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Description field.';
                }
                field("Production Order No. FLX"; Rec."Production Order No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Production Order Line No. FLX"; Rec."Production Order Line No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Work Center No. FLX"; Rec."Work Center No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Sales Order Line No. FLX"; Rec."Sales Order Line No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Sell-to Customer Name FLX"; Rec."Sell-to Customer Name FLX")
                {
                    ApplicationArea = All;
                }
                field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
                {
                    ApplicationArea = All;
                }
                field("Your Reference FLX"; Rec."Your Reference FLX")
                {
                    ApplicationArea = All;
                }
                field("Inventory FLX"; Rec.Inventory)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the quantity on inventory with this line.';
                }
                field("Calculated Weight FLX"; FlexSalesMng.CalculatePackageWeight(Rec))
                {
                    ApplicationArea = All;
                    Caption = 'Calculated Weight';
                    Editable = false;
                    ToolTip = 'Specifies the value of the Calculated Weight field.';
                }
                field("Scaled Weight FLX"; Rec."Scaled Weight FLX")
                {
                    ApplicationArea = All;
                }
                field("Hose Lenght FLX"; Rec."Hose Lenght FLX")
                {
                    ApplicationArea = All;
                }
                field("ID (mm) FLX"; Rec."ID (mm) FLX")
                {
                    ApplicationArea = All;
                }
                field("OD (mm) FLX"; Rec."OD (mm) FLX")
                {
                    ApplicationArea = All;
                }
                field("WP (bar) FLX"; Rec."WP (bar) FLX")
                {
                    ApplicationArea = All;
                }
                field("BP (bar) FLX"; Rec."BP (bar) FLX")
                {
                    ApplicationArea = All;
                }
                field("Lot No. FLX"; Rec."Lot No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Quality Control Date FLX"; Rec."Quality Control Date FLX")
                {
                    ApplicationArea = All;
                }
                field("Quality Control Status FLX"; Rec."Quality Control Status FLX")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Quality Controller ID FLX"; Rec."Quality Controller ID FLX")
                {
                    ApplicationArea = All;
                }
                field("Reject Reason Code FLX"; Rec."Reject Reason Code FLX")
                {
                    ApplicationArea = All;
                    ShowMandatory = Rec."Quality Control Status FLX" = Rec."Quality Control Status FLX"::Reject;
                }
                field("Location Code FLX"; Rec."Location Code FLX")
                {
                    ApplicationArea = All;
                }
                // field("Bin Code FLX"; FlexatiProductionMngt.GetBinCodeFromPackageNo(Rec."Package No."))
                // {
                //     ApplicationArea = All;
                //     Caption = 'Bin Code';
                //     ToolTip = 'Specifies the value of the Bin Code field.';
                // }
                // field("Combined Shipment No. FLX"; Rec."Combined Shipment No. FLX")
                // {
                //     ApplicationArea = All;
                // }
                // field("Parent Combined Shpmt. No. FLX"; Rec."Parent Combined Shpmt. No. FLX")
                // {
                //     ApplicationArea = All;
                // }
                field("Sell-to Customer No. FLX"; Rec."Sell-to Customer No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Sales Order No. FLX"; Rec."Sales Order No. FLX")
                {
                    ApplicationArea = All;
                }
                field("Label Lenght FLX"; Rec."Label Lenght FLX")
                {
                    ApplicationArea = All;
                }
                field("Produced At FLX"; Rec."Produced At FLX")
                {
                    ApplicationArea = All;
                }
                field("SystemCreatedAt FLX"; Rec.SystemCreatedAt)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        addfirst(Reporting)
        {
            action("PrintLabel FLX")
            {
                ApplicationArea = All;
                Caption = 'Print Label';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    //Rec.SetRecFilter();
                    PackageNoInformation.SetRange("Package No.", Rec."Package No.");
                    PackageNoInformation.FindFirst();
                    case Rec."Package Type FLX" of
                        "Package Type FLX"::Coil, "Package Type FLX"::Head:
                            Report.Run(Report::"QR Label FLX", true, true, PackageNoInformation);
                        "Package Type FLX"::Palette, "Package Type FLX"::Bulk:
                            Report.Run(Report::"Palette Label FLX", true, true, PackageNoInformation);
                    end;
                    CurrPage.Close();
                end;
            }
            action("Delete FLX")
            {
                ApplicationArea = All;
                Caption = 'Delete';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Visible = false;
                Image = Delete;
                ToolTip = 'Executes the Delete action.';
                trigger OnAction()
                begin
                    Rec.Delete(false);
                end;
            }
        }
    }

    var
        //     FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
        FlexSalesMng: Codeunit "Flexati Sales Management FLX";
}
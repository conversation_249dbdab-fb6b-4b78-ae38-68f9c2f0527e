{"id": "6063af83-0f2e-4915-bc54-95c136ffb4c9", "name": "Flexati Customizations", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "*********", "brief": "", "description": "", "privacyStatement": "", "EULA": "", "help": "", "url": "", "logo": "", "dependencies": [{"id": "48EC6A78-9CB7-47C4-9DFA-771457EA247B", "name": "İnfotek Add-On Infrastructure", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "*********"}, {"id": "3832f3fb-ad4c-4cc2-8fe4-98551ffbf2c2", "name": "E-Invoice by İnfotek", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "********"}, {"id": "d2942c73-3e0b-4ae3-a441-1fe409524c6b", "name": "Business Central Printer Service", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "********"}, {"id": "93e98f51-22d1-445e-917e-7edc619fb125", "name": "Importation File Management by İnfotek", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "********"}], "screenshots": [], "platform": "*******", "application": "********", "idRanges": [{"from": 60000, "to": 60999}], "resourceExposurePolicy": {"allowDebugging": true, "allowDownloadingSource": true, "includeSourceInSymbolFile": true}, "runtime": "14.2", "features": ["NoImplicitWith", "TranslationFile"]}
page 60009 "Combined Shipment FLX"
{
    ApplicationArea = All;
    Caption = 'Combined Shipment';
    PageType = Document;
    SourceTable = "Combined Shipment Header FLX";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus FLX")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }
            group(General)
            {
                Caption = 'General';
                //Editable = not Rec."Transferred to Sales Order";
                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Calcualted Shipment Weight"; Rec."Calcualted Shipment Weight")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Posted Sls. Invoice No."; Rec."Posted Sls. Invoice No.")
                {
                    trigger OnDrillDown()
                    var
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                    begin
                        SalesInvoiceHeader.Get(Rec."Posted Sls. Invoice No.");
                        //Page.Run(Page::"Posted Sales Invoice", SalesInvoiceHeader);
                        PageManagement.PageRun(SalesInvoiceHeader);
                    end;
                }
                field("Sales Invoice No."; Rec."Sales Invoice No.")
                {
                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                    begin
                        SalesHeader.Get(SalesHeader."Document Type"::Invoice, Rec."Sales Invoice No.");
                        //Page.Run(Page::"Sales Invoice", SalesHeader);
                        PageManagement.PageRun(SalesHeader);
                    end;
                }
                field("Scaled Shipment Weight"; Rec."Scaled Shipment Weight")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Ship-to Address"; Rec."Ship-to Address")
                {
                }
                field("Ship-to Address 2"; Rec."Ship-to Address 2")
                {
                }
                field("Ship-to City"; Rec."Ship-to City")
                {
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                }
                field("Ship-to Country/Region Code"; Rec."Ship-to Country/Region Code")
                {
                }
                field("Ship-to County"; Rec."Ship-to County")
                {
                }
                field("Ship-to Name"; Rec."Ship-to Name")
                {
                }
                field("Ship-to Name 2"; Rec."Ship-to Name 2")
                {
                }
                field("Ship-to Post Code"; Rec."Ship-to Post Code")
                {
                }
                field("Voyage No."; Rec."Voyage No.")
                {
                }
                field("Shipment Method Code"; Rec."Shipment Method Code")
                {
                }
                field("Total Package Quantity"; Rec."Total Package Count")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
            group(BarcodeReadingArea)
            {
                Caption = 'Barcode Reading';
                field("Remove Package"; Rec."Remove Package")
                {
                }
                field(Barcode; Rec.Barcode)
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;
                }
            }
            part(Lines; "Combined Shipment Lines FLX")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PrintPackingList)
            {
                ApplicationArea = All;
                Caption = 'Print Packing List';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Print;
                PromotedOnly = true;
                ToolTip = 'Executes the Print Packing List action.';
                trigger OnAction()
                var
                    CombineShipmentHeader: Record "Combined Shipment Header FLX";
                begin
                    CurrPage.SetSelectionFilter(CombineShipmentHeader);
                    Report.Run(Report::"Combined Shipment Header FLX", true, true, CombineShipmentHeader);
                end;
            }
            action(PrintHansePackingList)
            {
                ApplicationArea = All;
                Caption = 'Print Hanse Packing List';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Print;
                PromotedOnly = true;
                ToolTip = 'Executes the Print Hanse Packing List action.';
                trigger OnAction()
                var
                    CombineShipmentHeader: Record "Combined Shipment Header FLX";
                begin
                    CurrPage.SetSelectionFilter(CombineShipmentHeader);
                    Report.Run(Report::"Hanse Packing List FLX", true, true, CombineShipmentHeader);
                end;
            }
            action(GetOrderLines)
            {
                ApplicationArea = All;
                Caption = 'Get Order Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = GetLines;
                PromotedOnly = true;
                ToolTip = 'Executes the Get Order Lines action.';

                trigger OnAction()
                begin
                    FlexatiSalesManagement.GetSalesOrderLines(Rec);
                end;
            }
            action(TransferShipReadPackagesToSalesOrder)
            {
                ApplicationArea = All;
                Caption = 'Transfer & Ship Read Packages';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReleaseShipment;
                PromotedOnly = true;
                ToolTip = 'Executes the Transfer & Ship Read Packages.';

                trigger OnAction()
                begin
                    if not Confirm(ConfirmTransferAndShipMsg) then
                        exit;

                    FlexatiSalesManagement.TransferAndShipReadPackages(Rec);
                end;
            }
            action(CreateSalesInvoice)
            {
                ApplicationArea = All;
                Caption = 'Create Sales Invoice';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = SalesInvoice;
                ToolTip = 'Executes the Create Sales Invoice action.';
                trigger OnAction()
                begin
                    FlexatiSalesManagement.CreateSalesInvoiceFromCombinedShipmentHeader(Rec);
                end;
            }
            action(PostedShipment)
            {
                ApplicationArea = All;
                Caption = 'Posted Shipments';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = SalesShipment;
                PromotedOnly = true;
                ToolTip = 'Executes the Posted Shipments action.';
                trigger OnAction()
                var
                    SalesShipmentHeader: Record "Sales Shipment Header";
                begin
                    SalesShipmentHeader.SetRange("External Document No.", Rec."No.");
                    Page.Run(Page::"Posted Sales Shipments", SalesShipmentHeader);
                end;
            }
            // action(TransferReadPackagesToSalesOrder)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Transfer Read Packages To Sales Order';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = ReleaseShipment;
            //     PromotedOnly = true;
            //     ToolTip = 'Executes the Transfer Read Packages To SalesOrder action.';

            //     trigger OnAction()
            //     begin
            //         FlexatiSalesManagement.TransferReadPackagesToSalesOrder(Rec);
            //     end;
            // }
        }
    }
    // trigger OnOpenPage()
    // begin
    //     ShipmentLocationAndBinEditable := true;

    //     Rec.CalcFields("Total Package Quantity");
    //     if Rec."Total Package Quantity" <> 0 then
    //         ShipmentLocationAndBinEditable := false;
    // end;

    // trigger OnAfterGetCurrRecord()
    // begin
    //     Rec.CalcFields("Total Package Quantity");
    //     if Rec."Total Package Quantity" <> 0 then
    //         ShipmentLocationAndBinEditable := false;
    // end;

    var
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
        PageManagement: Codeunit "Page Management";
        ConfirmTransferAndShipMsg: Label 'Do you want to transfer and ship the read packages?';
    //ShipmentLocationAndBinEditable: Boolean;
}
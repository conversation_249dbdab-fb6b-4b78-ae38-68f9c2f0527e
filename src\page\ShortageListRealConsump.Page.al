page 60023 "Shortage List - Real Consump"
{
    ApplicationArea = All;
    Caption = 'Shortage List - Real Consump';
    PageType = List;
    Editable = false;
    SourceTable = "Shortage List - Cumulative Qty";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Real Consump Qty"; Rec."Real Consump Qty")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
    end;
}

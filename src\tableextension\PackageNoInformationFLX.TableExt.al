tableextension 60000 "Package No. Information FLX" extends "Package No. Information"
{
    fields
    {
        field(60000; "Package Type FLX"; Enum "Package Type FLX")
        {
            Caption = 'Package Type';
            Editable = false;
            ToolTip = 'Specifies the value of the Package Type field.';
        }
        field(60001; "Your Reference FLX"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(60002; "ID (mm) FLX"; Decimal)
        {
            Caption = 'ID (mm)';
            DecimalPlaces = 0 : 2;
            Editable = false;
            ToolTip = 'Specifies the value of the ID (mm) field.';
        }
        field(60003; "Parent Package No. FLX"; Code[50])
        {
            Caption = 'Parent Package No.';
            TableRelation = "Package No. Information"."Package No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Parent Package No. field.';
        }
        field(60004; "WP (bar) FLX"; Decimal)
        {
            Caption = 'WP (bar)';
            DecimalPlaces = 0 : 2;
            Editable = false;
            ToolTip = 'Specifies the value of the WP (bar) field.';
        }
        field(60005; "Sell-to Customer No. FLX"; Code[20])
        {
            Caption = 'Sell-to Customer No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Sell-to Customer No. field.';
        }
        field(60006; "OD (mm) FLX"; Decimal)
        {
            Caption = 'OD (mm)';
            DecimalPlaces = 0 : 2;
            Editable = false;
            ToolTip = 'Specifies the value of the OD (mm) field.';
        }
        field(60007; "BP (bar) FLX"; Decimal)
        {
            Caption = 'BP (bar)';
            DecimalPlaces = 0 : 2;
            Editable = false;
            ToolTip = 'Specifies the value of the BP (bar) field.';
        }
        field(60008; "Location Code FLX"; Code[10])
        {
            Caption = 'Location Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Ledger Entry"."Location Code" where("Item No." = field("Item No."),
                                                                  "Variant Code" = field("Variant Code"),
                                                                  "Package No." = field("Package No."),
                                                                  Open = const(true)));
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(60009; "Sell-to Customer Name FLX"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        field(60010; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Sell-to Customer No. FLX"));
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(60011; "Sales Order No. FLX"; Code[20])
        {
            Caption = 'Sales Order No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Sales Order No. field.';
        }
        field(60012; "Lot No. FLX"; Code[50])
        {
            Caption = 'Lot No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(60013; "Sales Order Line No. FLX"; Integer)
        {
            Caption = 'Sales Order Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Sales Order Line No. field.';
        }
        field(60014; "Production Order No. FLX"; Code[20])
        {
            Caption = 'Production Order No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(60015; "Hose Lenght FLX"; Decimal)
        {
            Caption = 'Hose Lenght';
            Editable = false;
            ToolTip = 'Specifies the value of the Hose Lenght field.';
        }
        field(60016; "Old Package No. FLX"; Code[50])
        {
            Caption = 'Old Package No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Old Package No. field.';
        }
        field(60017; "Produced At FLX"; DateTime)
        {
            Caption = 'Produced At';
            Editable = false;
            ToolTip = 'Specifies the value of the Produced At field.';
        }
        field(60018; "Label Lenght FLX"; Decimal)
        {
            Caption = 'Label Lenght';
            Editable = false;
            ToolTip = 'Specifies the value of the Label Lenght field.';
        }
        field(60019; "Work Center No. FLX"; Code[20])
        {
            Caption = 'Work Center No.';
            TableRelation = "Work Center"."No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Work Center No. field.';
        }
        field(60020; "Production Order Line No. FLX"; Integer)
        {
            Caption = 'Production Order Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Production Order Line No. field.';
        }
        field(60021; "Barcode Text FLX"; Code[50])
        {
            Caption = 'Barcode Text';
            ToolTip = 'Specifies the value of the Barcode Text field.';
            trigger OnValidate()
            begin
                FlexatiSalesManagement.InsertRemovePackageToPaletteWithLabelReading(Rec);
            end;
        }
        field(60022; "Remove Package FLX"; Boolean)
        {
            Caption = 'Remove Package';
            ToolTip = 'Specifies the value of the Remove Package field.';
        }
        field(60023; "Combined Shipment No. FLX"; Code[20])
        {
            Caption = 'Combined Shipment No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("CombinedShipmentLineDtl FLX"."Document No." where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Combined Shipment No. field.';
        }
        field(60024; "Parent Package Loc. Code FLX"; Code[10])
        {
            AllowInCustomizations = Always;
            Caption = 'Parent Package Location Code';
            TableRelation = Location.Code;
        }
        field(60025; "Parent Package Bin Code FLX"; Code[20])
        {
            AllowInCustomizations = Always;
            Caption = 'Parent Package Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Parent Package Loc. Code FLX"));
        }
        field(60026; "Quality Control Status FLX"; Enum "Quality Control Status FLX")
        {
            Caption = 'Quality Control Status';
            ToolTip = 'Specifies the value of the Quality Control Status field.';
            trigger OnValidate()
            begin
                case Rec."Quality Control Status FLX" of
                    "Quality Control Status FLX"::Accept, "Quality Control Status FLX"::Reject:
                        begin
                            Rec."Quality Control Date FLX" := WorkDate();
                            Rec."Quality Controller ID FLX" := CopyStr(UserId(), 1, MaxStrLen(Rec."Quality Controller ID FLX"));
                            Rec."Reject Reason Code FLX" := '';
                        end;
                    "Quality Control Status FLX"::" ":
                        begin
                            Rec."Quality Control Date FLX" := 0D;
                            Rec."Quality Controller ID FLX" := '';
                            Rec."Reject Reason Code FLX" := '';
                        end;
                end;
            end;
        }
        field(60027; "Reject Reason Code FLX"; Code[10])
        {
            Caption = 'Reject Reason Code';
            TableRelation = "Reason Code".Code;
            ToolTip = 'Specifies the value of the Reject Reason Code field.';
            //Editable = false;
        }
        field(60028; "Quality Control Date FLX"; Date)
        {
            Caption = 'Quality Control Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Quality Control Date field.';
        }
        field(60029; "Child Package Count FLX"; Integer)
        {
            Caption = 'Child Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package No. Information" where("Parent Package No. FLX" = field("Package No.")));
            ToolTip = 'Specifies the value of the Child Package Count field.';
        }
        field(60030; "Palette Item No. FLX"; Code[20])
        {
            Caption = 'Palette Item No.';
            TableRelation = Item."No." where("Item Category Code" = filter('PALET'));
            ToolTip = 'Specifies the value of the Palette Item No. field.';
        }
        field(60031; "Quality Controller ID FLX"; Code[50])
        {
            Caption = 'Quality Controller ID';
            Editable = false;
            ToolTip = 'Specifies the value of the Quality Controller ID field.';
        }
        field(60032; "Scaled Weight FLX"; Decimal)
        {
            Caption = 'Scaled Weight';
            ToolTip = 'Specifies the value of the Scaled Weight field.';
        }
        field(60033; "Shipment No FLX"; Code[20])
        {
            Caption = 'Shipment No';
            Editable = true;
            ToolTip = 'Specifies the value of the Shipment No field.';
        }
        field(60034; "Parent Combined Shpmt. No. FLX"; Code[20])
        {
            Caption = 'Parent Combined Shipment No.';
            ToolTip = 'Specifies the value of the Parent Combined Shipment No. field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("CombinedShipmentLineDtl FLX"."Document No." where("Parent Package No." = field("Package No.")));
        }
        field(60035; "Sales Order - Line No. FLX"; Code[50])
        {
            Caption = 'Sales Order - Line No.';
            ToolTip = 'Specifies the value of the Sales Order - Line No. field.';
            AllowInCustomizations = Always;
        }
    }

    keys
    {
        key("Key3 INF"; "Parent Package No. FLX")
        {
        }

        key("Key4 INF"; "Package No.")
        {
        }
    }

    trigger OnBeforeDelete()
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        PackageNoInformation: Record "Package No. Information";
        AlreadyInCombinedShipmentErr: Label 'You can not delete any package that read in a combined shipment.';
    begin
        Rec.TestField("Produced At FLX", 0DT);

        if ("Package Type FLX" = "Package Type FLX"::Palette) or ("Package Type FLX" = Rec."Package Type FLX"::Bulk) then begin
            PackageNoInformation.SetRange("Parent Package No. FLX", Rec."Package No.");
            PackageNoInformation.ModifyAll("Parent Package No. FLX", '', true);
        end;

        CombinedShipmentLineDtl.SetRange("Parent Package No.", Rec."Package No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(AlreadyInCombinedShipmentErr);
    end;

    var
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
}
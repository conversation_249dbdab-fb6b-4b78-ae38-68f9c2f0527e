page 60011 "Combined Shipment Line Dtl FLX"
{
    ApplicationArea = All;
    Caption = 'Combined Shipment Line Details';
    PageType = List;
    SourceTable = "CombinedShipmentLineDtl FLX";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Source Document No."; Rec."Source Document No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Package No."; Rec."Package No.")
                {
                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.");
                        Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Package Weight (KG)"; Rec."Package Weight (KG)")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Parent Package No."; Rec."Parent Package No.")
                {
                }
                field("Parent Package Type"; Rec."Parent Package Type")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
tableextension 60021 "Item Attribute Value Mapping" extends "Item Attribute Value Mapping"
{
    fields
    {
        field(60000; "Item Attribute Name FLX"; Text[250])
        {
            Caption = 'Item Attribute Name';
            ToolTip = 'Specifies the item attribute name.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Attribute"."Name" where("ID" = field("Item Attribute ID")));
        }
        field(60001; "Item Attribute Value FLX"; Text[250])
        {
            Caption = 'Item Attribute Value';
            ToolTip = 'Specifies the item attribute value.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Attribute Value".Value where("Attribute ID" = field("Item Attribute ID"), ID = field("Item Attribute Value ID")));
        }
    }
}
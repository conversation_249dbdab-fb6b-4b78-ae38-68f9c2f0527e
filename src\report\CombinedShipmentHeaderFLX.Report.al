report 60004 "Combined Shipment Header FLX"
{
    ApplicationArea = All;
    Caption = 'Packing List Combine Shipment';
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/PackingListCombineShipment.rdlc';
    //ApplicationArea = Basic, Suite;
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(CombinedShipmentHeaderFLX; "Combined Shipment Header FLX")
        {
            column(CompanyInfoPicture; CompanyInfo.Picture)
            {
            }
            column(CompInfoPhoneNo; CompanyInfo."Phone No.")
            {
            }
            column(CompInfoPostCodeCountyCityContry; CompanyInfo."Post Code" + ' ' + CompanyInfo.County + ' ' + CompanyInfo.City + ' ' + CompanyInfo."Country/Region Code")
            {
            }
            column(CompInfoAddress; CompanyInfo.Address + ' ' + CompanyInfo."Address 2")
            {
            }
            column(CompInfoName; CompanyInfo.Name + ' ' + CompanyInfo."Name 2")
            {
            }
            column(ShipToName; ShipToName)
            {
            }
            column(ShipToAdress; ShipToAdress)
            {
            }
            column(ShipToPostcode; ShipToPostcode)
            {
            }
            column(No; "No.")
            {
            }
            column(SalesInvoiceNo; "Sales Invoice No.")
            {
            }
            column(ContainerNo; "Container No.")
            {
            }
            column(VesselName; VesselName)
            {
            }
            column(CustomerName; "Customer Name")
            {
            }
            column(CustomerNo; "Customer No.")
            {
            }
            column(PostedSlsInvoiceNo; "Posted Sls. Invoice No.")
            {
            }
            column(PostingDate; Format("Posting Date"))
            {
            }
            column(DateText; DateText)
            {
            }
            column(ScaledShipmentWeight; "Scaled Shipment Weight")
            {
            }
            column(ShipName; "Ship Name")
            {
            }
            column(ShiptoAddress; "Ship-to Address")
            {
            }
            column(ShiptoAddress2; "Ship-to Address 2")
            {
            }
            column(ShiptoCity; "Ship-to City")
            {
            }
            column(ShiptoCode; "Ship-to Code")
            {
            }
            column(ShiptoCountryRegionCode; "Ship-to Country/Region Code")
            {
            }
            column(ShiptoCounty; "Ship-to County")
            {
            }
            column(Barcode; Barcode)
            {
            }
            column(CalcualtedShipmentWeight; "Calcualted Shipment Weight")
            {
            }
            column(TotalGrossWeight; TotalGrossWeight)
            {
            }
            column(TotalNetWeight; TotalNetWeight)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(NoSeries; "No. Series")
            {
            }
            column(RemovePackage; "Remove Package")
            {
            }
            column(ShiptoName2; "Ship-to Name 2")
            {
            }
            column(Status; Status)
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
            column(TotalPackageCount; "Total Package Count")
            {
            }
            column(TotalPackage; TotalPackage)
            {
            }
            column(PackageDesc; PackageDesc)
            {
            }
            column(GrandPallettotaltxt; GrandPallettotaltxt)
            {
            }
            column(VoyageNo; "Voyage No.")
            {
            }
            column(CustShipToLbl; CustShipToLbl)
            {
            }
            dataitem(CombinedShipmentPackageDetailFLX; "Combined Ship Package Det. FLX")
            {
                DataItemLink = "Combined Shipment No." = field("No.");
                DataItemLinkReference = CombinedShipmentHeaderFLX;
                DataItemTableView = sorting("Pallet Line Number", "No.", "Hose Length");
                column(OutputNo; OutputNo)
                {
                }
                column(PalletLineNumber; "Pallet Line Number")
                {
                }
                column(PalletDesc; PalletDesc)
                {
                }
                column(palletgrossweight; palletgrossweight)
                {
                }
                column(palletnetweight; palletnetweight)
                {
                }
                column(LineNumber; LineNumberNo)
                {
                }
                column(PackageNo; "Package No.")
                {
                }
                column(Item_No_; "No.")
                {
                }
                column(Item_Description; Description)
                {
                }
                column(LotNo; "Lot No.")
                {
                }
                column(CustOrderNo; CustOrderNo)
                {
                }
                column(palletmetretxt; palletmetretxt)
                {
                }
                column(palletfeettxt; palletfeettxt)
                {
                }
                column(pallettotaltxt; pallettotaltxt)
                {
                }
                column(Quantity_LineDetail; Quantity)
                {
                }
                column(Source_Document_No_LineDetail; "Source Document No.")
                {
                }
                column(Source_Document_Line_No_LineDetail; "Source Document Line No.")
                {
                }
                trigger OnPreDataItem()
                begin
                    //combineshipmentpackdetail
                    //FLEX-233 BEGIN
                    CombineShipPackDetail.Reset();
                    CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeaderFLX."No.");
                    if CombineShipPackDetail.FindFirst() then
                        repeat
                            OrderNo := '';
                            CustOrderNo := '';
                            SalesShipmentHD.Reset();
                            if (CombineShipPackDetail."Source Document No." <> '') and (SalesShipmentHD.Get(CopyStr(CombineShipPackDetail."Source Document No.", 1, 20))) then begin
                                OrderNo := SalesShipmentHD."Order No.";
                                CustOrderNo := SalesShipmentHD."Your Reference";
                            end else
                                if (CombineShipPackDetail."Source Document No." <> '') and (SalesOrder.Get(SalesOrder."Document Type"::Order, CopyStr(CombineShipPackDetail."Source Document No.", 1, 20))) then begin
                                    OrderNo := SalesOrder."No.";
                                    CustOrderNo := SalesOrder."Your Reference";
                                end else begin
                                    OrderNo := CombineShipPackDetail."Source Document No.";
                                    CustOrderNo := CombineShipPackDetail."Your Reference";
                                end;

                            if CombineShipPackDetail."Source Document No." = '' then
                                CombineShipPackDetail."Source Document No." := CopyStr(OrderNo, 1, 20);
                            if CombineShipPackDetail."Your Reference" = '' then
                                CombineShipPackDetail."Your Reference" := CopyStr(CustOrderNo, 1, 35);
                            CombineShipPackDetail.Modify(true);
                        until CombineShipPackDetail.Next() = 0;
                    custordernogrup := 'ZZZ';
                    //FLEX-233 END
                end;

                trigger OnAfterGetRecord()
                var
                    feetvaluetxt: Text[1024];
                    metrevaluetxt: Text[1024];
                    tare: Decimal;
                begin
                    //combineshipmentpackdetail
                    OrderNo := CombinedShipmentPackageDetailFLX."Source Document No."; //FLEX-233 added
                    CustOrderNo := CombinedShipmentPackageDetailFLX."Your Reference";  //FLEX-233 added
                                                                                       //FLEX-249 BEGIN
                    itemdesc := CombinedShipmentPackageDetailFLX.Description;
                    SalesOrder.Reset();
                    if (OrderNo <> '') and (SalesOrder.Get(SalesOrder."Document Type"::Order, CopyStr(OrderNo, 1, 20))) then begin
                        SalesOrderLine.Reset();
                        SalesOrderLine.SetRange("Document Type", SalesOrder."Document Type");
                        SalesOrderLine.SetRange("Document No.", SalesOrder."No.");
                        SalesOrderLine.SetRange("No.", CombinedShipmentPackageDetailFLX."No.");
                        if SalesOrderLine.FindFirst() then
                            itemdesc := SalesOrderLine.Description;
                    end;
                    //FLEX-249 END
                    //IF CombinedShipmentPackageDetailFLX."Pallet Line Number" = '' THEN
                    //   CombinedShipmentPackageDetailFLX."Pallet Line Number":='BULK';
                    if PalletLineNo <> CombinedShipmentPackageDetailFLX."Pallet Line Number" then begin
                        PalletLineNo := CombinedShipmentPackageDetailFLX."Pallet Line Number";
                        LineNumberNo := 0;
                        numtext := '';
                        palletmetretxt := '';
                        palletfeettxt := '';
                        pallettotaltxt := '';
                        palletnetweight := 0;
                        palletgrossweight := 0;
                        pallettotalfeet := 0;
                        pallettotalmetre := 0;
                        farksonuc_th := 0;
                        CombineShipPackDetail.Reset();
                        CombineShipPackDetail.Reset();
                        CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeaderFLX."No.");
                        CombineShipPackDetail.SetRange("Pallet Line Number", CombinedShipmentPackageDetailFLX."Pallet Line Number");
                        if CombineShipPackDetail.FindFirst() then
                            repeat
                                palletnetweight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                            until CombineShipPackDetail.Next() = 0;
                        if (farkmiktar_th <> 0) and (CalcTotalNetKG <> 0) then begin
                            farkoran_th := palletnetweight / CalcTotalNetKG;
                            farksonuc_th := Round(farkmiktar_th * farkoran_th, 0.01);
                            palletnetweight += farksonuc_th;
                        end;
                        tare := CombinedShipmentPackageDetailFLX."Tare Weight";
                        //if (tare = 0) and (Item.Get(CombinedShipmentPackageDetailFLX."No.")) then
                        //    tare := Item."Net Weight";
                        palletgrossweight := palletnetweight + tare;
                    end;
                    if numtext <> CombinedShipmentPackageDetailFLX."No." then begin//FLEX-233 cancel
                                                                                   //IF (numtext <> CombinedShipmentPackageDetailFLX."No.") AND (custordernogrup <> CustOrderNo) THEN //FLEX-233 added
                        numtext := CombinedShipmentPackageDetailFLX."No.";
                        custordernogrup := CustOrderNo;
                        palletmetretxt := '';
                        palletfeettxt := '';
                        pallettotaltxt := '';
                        pallettotalfeet := 0;
                        pallettotalmetre := 0;
                        TempPallets.Reset();
                        TempPallets.DeleteAll(false);
                        CombineShipPackDetail.Reset();
                        CombineShipPackDetail.Reset();
                        CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeaderFLX."No.");
                        CombineShipPackDetail.SetRange("Pallet Line Number", CombinedShipmentPackageDetailFLX."Pallet Line Number");
                        CombineShipPackDetail.SetRange("No.", CombinedShipmentPackageDetailFLX."No.");
                        CombineShipPackDetail.SetRange("Your Reference", CustOrderNo); //FLEX-233 added
                        if CombineShipPackDetail.FindFirst() then
                            repeat
                                //message('qty:' + Format(CombineShipPackDetail.Quantity) + 'metrevaletext:' + metrevaluetxt + ' feetvaletext:' + feetvaluetxt);
                                if palletmetretxt = '' then begin
                                    metrevaluetxt := '(' + Format(CombineShipPackDetail.Quantity) + ')';
                                    feetvaluetxt := '(' + Format(Round(CombineShipPackDetail.Quantity / 0.305, 0.01)) + ')';
                                    //MetreFeetTextEsitle(metrevaluetxt,feetvaluetxt);
                                    //FLEX-230 BEGIN
                                    TempPallets.Init();
                                    TempPallets."Pallet Code" := Format(CombineShipPackDetail.Quantity);
                                    TempPallets."Coil Qty" := 1;
                                    TempPallets."Pallet Meter Text" := CopyStr('1 X ' + metrevaluetxt, 1, 1024);
                                    TempPallets."Pallet Feet Text" := CopyStr('1 X ' + feetvaluetxt, 1, 1024);
                                    TempPallets.Insert(false);
                                    //FLEX-230 END
                                    palletmetretxt := metrevaluetxt;
                                    palletfeettxt := feetvaluetxt;
                                end else begin
                                    metrevaluetxt := '(' + Format(CombineShipPackDetail.Quantity) + ')';
                                    feetvaluetxt := '(' + Format(Round(CombineShipPackDetail.Quantity / 0.305, 0.01)) + ')';
                                    //MetreFeetTextEsitle(metrevaluetxt,feetvaluetxt);
                                    //FLEX-230 BEGIN
                                    TempPallets.Reset();
                                    TempPallets.SetRange("Pallet Code", Format(CombineShipPackDetail.Quantity));
                                    if not TempPallets.FindFirst() then begin
                                        TempPallets.Init();
                                        TempPallets."Pallet Code" := Format(CombineShipPackDetail.Quantity);
                                        TempPallets."Coil Qty" := 1;
                                        TempPallets."Pallet Meter Text" := CopyStr('  1 X ' + metrevaluetxt, 1, 1024);
                                        TempPallets."Pallet Feet Text" := CopyStr('  1 X ' + feetvaluetxt, 1, 1024);
                                        TempPallets.Insert(false);
                                    end else begin
                                        TempPallets."Coil Qty" += 1;
                                        TempPallets."Pallet Meter Text" := CopyStr('  ' + Format(TempPallets."Coil Qty") + ' X ' + metrevaluetxt, 1, 1024);
                                        TempPallets."Pallet Feet Text" := CopyStr('  ' + Format(TempPallets."Coil Qty") + ' X ' + feetvaluetxt, 1, 1024);
                                        TempPallets.Modify(false);
                                    end;
                                    //FLEX-230 END
                                    metrevaluetxt := ' (' + Format(CombineShipPackDetail.Quantity) + ')';
                                    feetvaluetxt := ' (' + Format(Round(CombineShipPackDetail.Quantity / 0.305, 0.01)) + ')';
                                    MetreFeetTextEsitle(metrevaluetxt, feetvaluetxt);
                                    //palletmetretxt += metrevaluetxt;
                                    //palletfeettxt += feetvaluetxt;
                                end;
                                pallettotalmetre += CombineShipPackDetail.Quantity;
                                pallettotalfeet += Round(CombineShipPackDetail.Quantity / 0.305, 0.01);
                            until CombineShipPackDetail.Next() = 0;
                        //FLEX-230 BEGIN
                        palletmetretxt := '';
                        palletfeettxt := '';
                        TempPallets.Reset();
                        if TempPallets.FindFirst() then
                            repeat
                                MetreFeetTextEsitle(palletmetretxt, palletfeettxt);
                                palletmetretxt += TempPallets."Pallet Meter Text";
                                palletfeettxt += TempPallets."Pallet Feet Text";
                            until TempPallets.Next() = 0;
                        //FLEX-230 END
                        pallettotaltxt := 'MT ' + Format(pallettotalmetre) + ' (FT ' + Format(pallettotalfeet) + ')';
                        //GrandPallettotalfeet += pallettotalfeet;
                        //GrandPallettotalmetre += pallettotalmetre;
                    end;

                    //IF (CombinedShipmentPackageDetailFLX.Type = CombinedShipmentPackageDetailFLX.Type::Item) AND
                    if (CombinedShipmentPackageDetailFLX."No." <> '') and
                       //(numtext <> CombinedShipmentPackageDetailFLX."No."+FORMAT(CombinedShipmentPackageDetailFLX."Hose Length")) THEN //FLEX-233 cancel
                       (numtext <> CombinedShipmentPackageDetailFLX."No." + CustOrderNo + Format(CombinedShipmentPackageDetailFLX."Hose Length")) then begin//FLEX-233 added
                        LineNumberNo += 1;
                        //numtext:=CombinedShipmentPackageDetailFLX."No."+FORMAT(CombinedShipmentPackageDetailFLX."Hose Length"); //FLEX-233 cancel
                        numtext := CombinedShipmentPackageDetailFLX."No." + CustOrderNo + Format(CombinedShipmentPackageDetailFLX."Hose Length"); //FLEX-233 added
                    end;

                    PalletDesc := '';
                    //PalletTare := 0;
                    if CombinedShipmentPackageDetailFLX."Pallet Code" <> '' then //begin
                        PalletDesc := CombinedShipmentPackageDetailFLX."Pallet Description" + ' PALLET';
                    //PalletTare := CombinedShipmentPackageDetailFLX."Tare Weight";
                    //end;
                    //NetWeight := Round(CombinedShipmentPackageDetailFLX."Coil Weight" * CombinedShipmentPackageDetailFLX.Piece, 0.01);
                    //NetWeight:=ROUND(CombinedShipmentPackageDetailFLX."Coil Weight"*CombinedShipmentPackageDetailFLX.Quantity,0.01);
                end;
            }
            trigger OnAfterGetRecord()
            var
                InvNoErr: Label 'Sales Invoice No Not Found';
                paletcode: Code[20];
            begin
                //combine shipment header
                if (ShowNotShip1 = false) and (CombinedShipmentHeaderFLX."Sales Invoice No." = '') then
                    Error(InvNoErr);

                EntryNo := 0;
                LineNumberNo := 0;
                PackageDesc := '';
                BulkPieces := 0;
                PalletQty := 0;
                TotalTareWeight := 0;
                TotalGrossWeight := 0;
                TotalNetWeight := 0;
                //TotalVolume := 0;
                pallettotalmetre := 0;
                pallettotalfeet := 0;
                farkmiktar_th := 0;
                paletcode := '';
                DateText := GetDateText(CombinedShipmentHeaderFLX."Posting Date");

                CombinedShipmentHeaderFLX.CalcFields("Calcualted Shipment Weight");
                if CombinedShipmentHeaderFLX."Scaled Shipment Weight" > 0 then
                    farkmiktar_th := CombinedShipmentHeaderFLX."Scaled Shipment Weight" - CombinedShipmentHeaderFLX."Calcualted Shipment Weight";

                CombineShipPackDetail.Reset();
                CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeaderFLX."No.");
                if CombineShipPackDetail.FindFirst() then
                    CombineShipPackDetail.DeleteAll(true);

                CombineShipLineDetail.Reset();
                CombineShipLineDetail.SetCurrentKey("Parent Package No.");
                CombineShipLineDetail.SetRange("Document No.", CombinedShipmentHeaderFLX."No.");
                if CombineShipLineDetail.FindFirst() then
                    repeat
                        CombineShipLineDetail.CalcFields("Parent Package Type");
                        if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) or
                           (CombineShipLineDetail."Parent Package No." = '') then begin
                            TareDesc := '';
                            TareWeight := 0;
                            BulkPieces += 1;
                        end else begin
                            CombineShipPackDetail.Reset();
                            CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeaderFLX."No.");
                            CombineShipPackDetail.SetRange("Transfer-from Package No.", CombineShipLineDetail."Parent Package No.");
                            if not CombineShipPackDetail.FindFirst() then begin
                                PalletQty += 1;
                                PackageTransferOut.Reset();
                                PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                                if PackageTransferOut.FindFirst() then
                                    if PackageTransferOut."Palette Item No. FLX" <> '' then
                                        if Item.Get(PackageTransferOut."Palette Item No. FLX") then begin
                                            TareDesc := Item.Description;
                                            TareWeight := Item."Net Weight";
                                            TotalTareWeight += Item."Net Weight";
                                            TotalGrossWeight += Item."Net Weight";
                                            paletcode := Item."No.";
                                        end;
                            end;
                        end;

                        /*if CombineShipLineDetail."Item No." <> '' then
                            if Item.Get(CombineShipLineDetail."Item No.") then begin
                                TareDesc := Item.Description;
                                TareWeight := Item."Net Weight";
                                TotalTareWeight += Item."Net Weight";
                                TotalGrossWeight += Item."Net Weight";
                            end;
                        */
                        Package.Reset();
                        Package.SetRange(Package."Package No.", CombineShipLineDetail."Package No.");
                        Package.SetRange(Package."Combined Shipment No. FLX", CombinedShipmentHeaderFLX."No.");
                        Package.FindFirst();
                        //FLEX-230 BEGIN
                        OrderNo := Package."Sales Order No. FLX";
                        CustOrderNo := Package."Your Reference FLX";
                        if CombineShipLineDetail."Parent Package No." <> '' then begin
                            PackageTransferOut.Reset();
                            PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                            if PackageTransferOut.FindFirst() then begin
                                if PackageTransferOut."Sales Order No. FLX" <> '' then
                                    OrderNo := PackageTransferOut."Sales Order No. FLX";
                                if PackageTransferOut."Your Reference FLX" <> '' then
                                    CustOrderNo := PackageTransferOut."Your Reference FLX";
                            end;
                        end;
                        //FLEX-230 END
                        EntryNo += 1;
                        CombineShipPackDetail.Init();
                        CombineShipPackDetail."Combined Shipment No." := CombinedShipmentHeaderFLX."No.";
                        CombineShipPackDetail."Package Type" := CombineShipLineDetail."Parent Package Type";
                        CombineShipPackDetail."Package No." := CombineShipLineDetail."Package No.";
                        CombineShipPackDetail."Transfer-from Package No." := CombineShipLineDetail."Parent Package No.";
                        CombineShipPackDetail."Source Document No." := OrderNo;//FLEX-230 added line
                        CombineShipPackDetail."Your Reference" := CustOrderNo; //FLEX-230 added line
                        CombineShipPackDetail."Entry No" := EntryNo;
                        CombineShipPackDetail.Type := CombineShipPackDetail.Type::Item;
                        CombineShipPackDetail."No." := CombineShipLineDetail."Item No.";
                        CombineShipPackDetail.Description := CombineShipLineDetail."Item Description";
                        CombineShipPackDetail.Quantity := CombineShipLineDetail.Quantity;
                        CombineShipPackDetail."Coil Lenght" := CombineShipLineDetail.Quantity;
                        CombineShipPackDetail."Hose Length" := CombineShipLineDetail.Quantity;
                        //CombineShipPackDetail."Serial No." := CombineShipLineDetail."Serial No.";
                        CombineShipPackDetail."Lot No." := CombineShipLineDetail."Lot No.";
                        //LotNoInf.Reset();
                        //LotNoInf.SetRange("Item No.", CombineShipLineDetail."Item No.");
                        //LotNoInf.SetRange("Lot No.", CombineShipLineDetail."Lot No.");
                        //if LotNoInf.FindFirst() then
                        //CombineShipPackDetail."Coil Weight" := ROUND(LotNoInf."Weight per Qty." * CombineShipPackDetail.Quantity, 0.01);
                        CombineShipPackDetail."Coil Weight" := CombineShipLineDetail."Package Weight (KG)";
                        //CombineShipPackDetail."Expiration Date" := PackageContentTreeView."Expiration Date";
                        //CombineShipPackDetail."Pallet Code" := Package."Package Item No.";
                        CombineShipPackDetail.Piece := 1;
                        if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or
                           (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) or
                           (CombineShipLineDetail."Parent Package No." = '') then
                            CombineShipPackDetail."Pallet Line Number" := 'BULK'
                        else begin
                            CombineShipPackDetail."Pallet Line Number" := Format(PalletQty);
                            if StrLen(CombineShipPackDetail."Pallet Line Number") = 1 then
                                CombineShipPackDetail."Pallet Line Number" := CopyStr('0' + CombineShipPackDetail."Pallet Line Number", 1, 100);
                            CombineShipPackDetail."Pallet Line Number" += CopyStr('-' + CombineShipLineDetail."Parent Package No.", 1, 100);
                            CombineShipPackDetail."Pallet Description" := TareDesc;
                            CombineShipPackDetail."Tare Weight" := TareWeight;
                            CombineShipPackDetail."Pallet Code" := paletcode;//CombineShipLineDetail."Item No.";
                        end;
                        CombineShipPackDetail.Insert(true);
                        pallettotalmetre += CombineShipPackDetail.Quantity;
                        pallettotalfeet += Round(CombineShipPackDetail.Quantity / 0.305, 0.01);
                        TotalGrossWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                        TotalNetWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                    //if Item.Get(CombineShipPackDetail."No.") then
                    //    TotalVolume += Round(Item."Unit Volume" * CombineShipPackDetail.Piece, 0.01);
                    until CombineShipLineDetail.Next() = 0;

                CalcTotalNetKG := TotalNetWeight;
                if farkmiktar_th <> 0 then begin
                    TotalNetWeight := CombinedShipmentHeaderFLX."Scaled Shipment Weight";
                    TotalGrossWeight := CombinedShipmentHeaderFLX."Scaled Shipment Weight" + TotalTareWeight;
                end;

                TotalPackage := Format(PalletQty + BulkPieces) + ' PACKAGES';
                if PalletQty > 0 then
                    PackageDesc := '( ' + Format(PalletQty) + ' PALLETS';
                if BulkPieces > 0 then begin
                    if PackageDesc = '' then
                        PackageDesc += '( '
                    else
                        PackageDesc += ' / ';
                    PackageDesc += Format(BulkPieces) + ' BULK )';
                end else
                    PackageDesc += ' )';

                GrandPallettotaltxt := 'MT ' + Format(pallettotalmetre) + ' (FT ' + Format(pallettotalfeet) + ')';

                //if SalesHeader.Get(SalesHeader."Document Type"::Invoice, CombinedShipmentHeaderFLX."Sales Invoice No.") then begin
                /*IF SalesHeader."Ship-to Code" = '' THEN BEGIN
                              ShipToName := SalesHeader."Sell-to Customer Name";
                              ShipToAdress := SalesHeader."Sell-to Address" + ' ' + SalesHeader."Sell-to Address 2";
                              ShipToPostcode := SalesHeader."Sell-to Post Code" + ' ' + SalesHeader."Sell-to County" + ' ' +
                                            SalesHeader."Sell-to City" + ' ' + SalesHeader."Sell-to Country/Region Code";
                          END ELSE BEGIN
                              ShipToName := SalesHeader."Ship-to Name";
                              ShipToAdress := SalesHeader."Ship-to Address" + ' ' + SalesHeader."Ship-to Address 2";
                              ShipToPostcode := SalesHeader."Ship-to Post Code" + ' ' + SalesHeader."Ship-to County" + ' ' +
                                              SalesHeader."Ship-to City" + ' ' + SalesHeader."Ship-to Country/Region Code";
                          END;
                */
                //end else
                if not SalesHeader.Get(SalesHeader."Document Type"::Invoice, CombinedShipmentHeaderFLX."Sales Invoice No.") then
                    SalesHeader.Init();

                ShipToName := CombinedShipmentHeaderFLX."Ship-to Name";
                ShipToAdress := CombinedShipmentHeaderFLX."Ship-to Address" + ' ' + CombinedShipmentHeaderFLX."Ship-to Address 2";
                ShipToPostcode := CombinedShipmentHeaderFLX."Ship-to Post Code" + ' ' + CombinedShipmentHeaderFLX."Ship-to County" + ' ' +
                                CombinedShipmentHeaderFLX."Ship-to City" + ' ' + CombinedShipmentHeaderFLX."Ship-to Country/Region Code";
            end;
        }
    }
    requestpage
    {
        layout
        {
            area(Content)
            {
                group(GroupName)
                {
                    Caption = 'Options';
                    field(ShowNotship; ShowNotShip1)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Show Not Ship';
                        ToolTip = 'Specifies that Show Not ship.';
                    }
                }
            }
        }
        actions
        {
            area(Processing)
            {
            }
        }
    }
    trigger OnPreReport()
    begin
        CompanyInfo.Get();
        CompanyInfo.CalcFields(Picture);
    end;

    var
        CombineShipLineDetail: Record "CombinedShipmentLineDtl FLX";
        CombineShipPackDetail: Record "Combined Ship Package Det. FLX";
        CompanyInfo: Record "Company Information";
        Item: Record Item;
        Package: Record "Package No. Information";
        PackageTransferOut: Record "Package No. Information";
        TempPallets: Record "Pallets FLX" temporary;
        SalesHeader: Record "Sales Header";
        SalesOrder: Record "Sales Header";
        SalesOrderLine: Record "Sales Line";
        SalesShipmentHD: Record "Sales Shipment Header";
        //ekle: Boolean;
        ShowNotShip1: Boolean;
        BulkPieces: Decimal;
        CalcTotalNetKG: Decimal;
        farkmiktar_th: Decimal;
        farkoran_th: Decimal;
        farksonuc_th: Decimal;
        //GrandPallettotalfeet: Decimal;
        //GrandPallettotalmetre: Decimal;
        //NetWeight: Decimal;
        palletgrossweight: Decimal;
        palletnetweight: Decimal;
        PalletQty: Decimal;
        //PalletTare: Decimal;
        pallettotalfeet: Decimal;
        pallettotalmetre: Decimal;
        TareWeight: Decimal;
        TotalGrossWeight: Decimal;
        TotalNetWeight: Decimal;
        TotalTareWeight: Decimal;
        //TotalVolume: Decimal;
        EntryNo: Integer;
        LineNumberNo: Integer;
        OutputNo: Integer;
        CustShipToLbl: Label 'CUSTOMER / SHIP TO : ';
        DateText: Text[30];
        CustOrderNo: Text[50];
        custordernogrup: Text[50];
        OrderNo: Text[50];
        VesselName: Text[50];
        itemdesc: Text[100];
        PalletLineNo: Text[100];
        ShipToName: Text[100];
        TareDesc: Text[100];
        numtext: Text[250];
        PalletDesc: Text[250];
        ShipToAdress: Text[250];
        ShipToPostcode: Text[250];
        TotalPackage: Text[250];
        GrandPallettotaltxt: Text[1024];
        PackageDesc: Text[1024];
        palletfeettxt: Text[1024];
        palletmetretxt: Text[1024];
        pallettotaltxt: Text[1024];

    local procedure GetDateText(ComingDate: Date) result: Text[30]
    var
        month: Integer;
        monthtext: Text[3];
    begin
        result := Format(Date2DMY(ComingDate, 1)) + '-';
        month := Date2DMY(ComingDate, 2);
        case month of
            1:
                monthtext := 'Jan';
            2:
                monthtext := 'Feb';
            3:
                monthtext := 'Mar';
            4:
                monthtext := 'Apr';
            5:
                monthtext := 'May';
            6:
                monthtext := 'Jun';
            7:
                monthtext := 'Jul';
            8:
                monthtext := 'Aug';
            9:
                monthtext := 'Sep';
            10:
                monthtext := 'Oct';
            11:
                monthtext := 'Nov';
            12:
                monthtext := 'Dec';
        end;
        result += monthtext + '-' + Format(Date2DMY(ComingDate, 3));
    end;

    procedure MetreFeetTextEsitle(var metrevaluetxt: Text[1024]; var feetvaluetxt: Text[1024])
    var
        fark: Integer;
        feetlength: Integer;
        i: Integer;
        metrelength: Integer;
        solaekle: Text[1024];
    begin
        metrelength := StrLen(metrevaluetxt);
        feetlength := StrLen(feetvaluetxt);
        fark := Abs(feetlength - metrelength);
        solaekle := '';
        for i := 1 to fark - 1 do
            solaekle += ' ';
        if fark = 1 then
            solaekle := ' ';

        if feetlength > metrelength then
            metrevaluetxt := CopyStr(solaekle + metrevaluetxt, 1, 1024)
        else
            if feetlength < metrelength then
                feetvaluetxt := CopyStr(solaekle + feetvaluetxt, 1, 1024);
    end;
}
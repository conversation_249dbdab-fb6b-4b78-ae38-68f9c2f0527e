table 60000 "Flexati Setup FLX"
{
    Caption = 'Flexati Setup';

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Always;
        }
        field(2; "Work Center Group for Cost"; Code[10])
        {
            Caption = 'Work Center Group Code for Cost';
            TableRelation = "Work Center Group".Code;
            ToolTip = 'Specifies the value of the Work Center Group Code for Cost field.';
        }
        // field(3; "Default Prod. Order Loc. Code"; Code[10])
        // {
        //     Caption = 'Default Production Order Location Code';
        //     TableRelation = Location.Code;
        // }
        field(4; "Palette Label No. Series"; Code[20])
        {
            Caption = 'Palette Label No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Palette No. Series field.';
        }
        field(5; "Head Label No. Series"; Code[20])
        {
            Caption = 'Head Label No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Head No. Series field.';
        }
        field(6; "Consumption Jnl. Template Name"; Code[10])
        {
            Caption = 'Consumption Journal Template Name';
            TableRelation = "Item Journal Template".Name where(Type = const(Consumption));
            ToolTip = 'Specifies the value of the Consumption Journal Template Name field.';
        }
        field(7; "Output Jnl. Template Name"; Code[10])
        {
            Caption = 'Output Journal Template Name';
            TableRelation = "Item Journal Template".Name where(Type = const(Output));
            ToolTip = 'Specifies the value of the Output Journal Template Name field.';
        }
        field(8; "Package Split No. Series"; Code[20])
        {
            Caption = 'Package Split No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Package Split No. Series field.';
        }
        field(9; "Package Split Jnl. Tmpl. Name"; Code[10])
        {
            Caption = 'Package Split Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Transfer));
            ToolTip = 'Specifies the value of the Package Split Journal Template Name field.';
        }
        field(10; "Package Split Jnl. Batch Name"; Code[10])
        {
            Caption = 'Package Split Journal Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Split Jnl. Tmpl. Name"));
            ToolTip = 'Specifies the value of the Package Split Journal Batch Name field.';
        }
        field(11; "Combined Shipment No. Series"; Code[20])
        {
            Caption = 'Combined Shipment No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Combined Shipment No. Series field.';
        }
        field(12; "Quality Control No. Series"; Code[20])
        {
            Caption = 'Quality Control No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Quality Control No. Series field.';
        }
        field(13; "Package Transfer Template Name"; Code[10])
        {
            Caption = 'Package Transfer Template Name';
            TableRelation = "Item Journal Template";
            ToolTip = 'Specifies the value of the Package Transfer Template Name field.';
        }
        // field(14; "Package Transfer Batch Name"; Code[10])
        // {
        //     Caption = 'Package Transfer Batch Name';
        //     TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Transfer Template Name"));
        // }
        field(15; "Package Transfer No. Series"; Code[20])
        {
            Caption = 'Package Transfer No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Package Transfer No. Series field.';
        }
        field(16; "Shipment Location Code"; Code[10])
        {
            Caption = 'Shipment Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Shipment Location Code field.';
        }
        field(17; "Shipment Bin Code"; Code[20])
        {
            Caption = 'Shipment Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Shipment Location Code"));
            ToolTip = 'Specifies the value of the Shipment Bin Code field.';
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;
}
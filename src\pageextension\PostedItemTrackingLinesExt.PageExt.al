pageextension 60031 "Posted Item Tracking Lines Ext" extends "Posted Item Tracking Lines"
{
    /*actions
    {
        addafter(Navigate)
        {
            action("Item Lot No Barcode FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Item Lot No Barcode';
                Image = Print;
                ToolTip = 'Item Lot No Barcode.';

                trigger OnAction()
                var
                    TrackingSpec: Record "Tracking Specification";
                    rpTrSpec: Report "Tracking Spec Barcode FLX";
                begin
                    CurrPage.SetSelectionFilter(TrackingSpec);
                    //Report.RunModal(Report::"Tracking Spec Barcode FLX", true, false, TrackingSpec);
                    rpTrSpec.SetVar(Rec."Item No.", Rec."Lot No.");
                    //rpTrSpec.SetTableView(TrackingSpec);
                    rpTrSpec.RunModal();
                end;
            }
        }
    }
    */
}
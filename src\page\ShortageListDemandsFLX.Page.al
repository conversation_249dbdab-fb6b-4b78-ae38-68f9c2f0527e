page 60024 "Shortage List - Demands FLX"
{
    ApplicationArea = All;
    Caption = 'Shortage List - Demands';
    PageType = List;
    Editable = false;
    SourceTable = "Shortage List - Cumulative Qty";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Negative Qty."; Rec."Negative Qty.")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
    end;
}

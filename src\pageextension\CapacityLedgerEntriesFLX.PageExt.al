pageextension 60020 "Capacity Ledger Entries FLX" extends "Capacity Ledger Entries"
{
    layout
    {
        addafter("Order No.")
        {
            field("Order Line No. FLX"; Rec."Order Line No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the line number of the order line.';
            }
        }
        addlast(Control1)
        {
            field("Production Line No. FLX"; Rec."Production Line No. FLX")
            {
                ApplicationArea = All;
            }
            field("Package No. FLX"; Rec."Package No. FLX")
            {
                ApplicationArea = All;
            }
            field("Item Journal Batch Name FLX"; Rec."Item Journal Batch Name FLX")
            {
                ApplicationArea = All;
            }
            field("Starting DateTime FLX"; Rec."Starting DateTime FLX")
            {
                ApplicationArea = All;
            }
            field("Ending DateTime FLX"; Rec."Ending DateTime FLX")
            {
                ApplicationArea = All;
            }
        }
    }
}
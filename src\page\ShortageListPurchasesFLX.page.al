page 60025 "Shortage List - Purchases FLX"
{
    ApplicationArea = All;
    Caption = 'Shortage List - Purchases';
    PageType = List;
    Editable = false;
    SourceTable = "Shortage List - Cumulative Qty";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Positive Qty."; Rec."Positive Qty.")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
    end;
}

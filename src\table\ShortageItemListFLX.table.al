table 60016 "Shortage Item List FLX"
{
    Caption = 'Shortage Item List';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(2; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(3; "Date Filter"; Date)
        {
            Caption = 'Date Filter';
            FieldClass = FlowFilter;
        }
        field(4; "Location Filter"; Code[10])
        {
            Caption = 'Location Filter';
            FieldClass = FlowFilter;
        }
        field(5; "Cumulative Net Qty."; Decimal)
        {
            Caption = 'Cumulative Net Qty.';
            AllowInCustomizations = Always;
        }
        field(6; "Quantity On Reject Loc."; Decimal)
        {
            Caption = 'Quantity On Reject Loc.';
            AllowInCustomizations = Always;
        }
        field(7; "Quantity On Subcontractors"; Decimal)
        {
            Caption = 'Quantity On Subcontractors';
            AllowInCustomizations = Always;
        }
        field(8; "Lead Time Calculation"; DateFormula)
        {
            Caption = 'Lead Time Calculation';
            ToolTip = 'Specifies the value of the Lead Time Calculation field.';
        }
        field(9; "Inventory Posting Group"; Code[20])
        {
            Caption = 'Inventory Posting Group';
            TableRelation = "Inventory Posting Group";
            AllowInCustomizations = Always;
        }
        field(10; "Lead Time as Date"; Date)
        {
            Caption = 'Lead Time as Date';
            AllowInCustomizations = Always;
        }
        field(11; "Quantity On UHD Location"; Decimal)
        {
            Caption = 'Quantity On UHD Location';
            ToolTip = 'Specifies the value of the Quantity On UHD Location field.';
        }
        field(12; "Quantity On WHSE Location"; Decimal)
        {
            Caption = 'Quantity On WHSE Location';
            ToolTip = 'Specifies the value of the Quantity On WHSE Location field.';
        }
        field(13; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            TableRelation = Vendor;
            AllowInCustomizations = Always;
        }
        field(14; "Vendor Name"; Text[100])
        {
            Caption = 'Vendor Name';
            AllowInCustomizations = Always;
        }
        field(15; Inventory; Decimal)
        {
            Caption = 'Inventory';
            ToolTip = 'Specifies the value of the Inventory field.';
        }
        field(16; "User Id"; Code[50])
        {
            Caption = 'User Id';
            ToolTip = 'Specifies the value of the User Id field.';
        }
        field(17; "Qty. on Purch. Order"; Decimal)
        {
            Caption = 'Qty. on Purch. Order';
            ToolTip = 'Specifies the value of the Qty. on Purch. Order field.';
        }
        field(18; "Qty. on Sales Order"; Decimal)
        {
            Caption = 'Qty. on Sales Order';
            ToolTip = 'Specifies the value of the Qty. on Sales Order field.';
        }
        field(19; "Product hyerachy dim. value"; Code[20])
        {
            Caption = 'Product hyerachy dim. value';
            AllowInCustomizations = Always;
        }
        field(20; "Hide Line"; Boolean)
        {
            Caption = 'Hide Line';
            AllowInCustomizations = Always;
        }
        field(21; "Item Description 2"; Text[100])
        {
            Caption = 'Item Description 2';
            ToolTip = 'Specifies the value of the Item Description 2 field.';
        }
        field(22; "Safety Stock Quantity"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'Safety Stock Quantity';
        }
        field(23; "Minimum Order Qty."; Decimal)
        {
            Caption = 'Minimum Order Qty.';
            ToolTip = 'Specifies the value of the Minimum Order Qty. field.';
        }
        field(24; "Item Category Code"; Code[20])
        {
            Caption = 'Item Category Code';
            ToolTip = 'Specifies the value of the Item Category Code field.';
        }
        field(25; "Qty. on Component Lines"; Decimal)
        {
            Caption = 'Qty. on Component Lines';
            ToolTip = 'Specifies the value of the Qty. on Component Lines field.';
        }
    }
    keys
    {
        key(PK; "Item No.", "User Id")
        {
            Clustered = true;
        }
        key(Key2; "Lead Time as Date")
        {
        }
    }
}

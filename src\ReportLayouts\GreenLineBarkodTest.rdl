﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>bc508610-c9c1-4ad1-a1b1-a57eaa153929</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
      <Fields>
        <Field Name="IDmmFLX_PalettePackageNoInformation">
          <DataField>IDmmFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PalettePackageNoInformationFormat">
          <DataField>IDmmFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ODmmFLX_PalettePackageNoInformation">
          <DataField>ODmmFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ODmmFLX_PalettePackageNoInformationFormat">
          <DataField>ODmmFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ProductionOrderNoFLX_PalettePackageNoInformation">
          <DataField>ProductionOrderNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="SalesOrderNoFLX_PalettePackageNoInformation">
          <DataField>SalesOrderNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PalettePackageNoInformation">
          <DataField>BPbarFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PalettePackageNoInformationFormat">
          <DataField>BPbarFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="WPbarFLX_PalettePackageNoInformation">
          <DataField>WPbarFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="WPbarFLX_PalettePackageNoInformationFormat">
          <DataField>WPbarFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SelltoCustomerNameFLX_PalettePackageNoInformation">
          <DataField>SelltoCustomerNameFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Description_PalettePackageNoInformation">
          <DataField>Description_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="HoseLenghtFLX_PalettePackageNoInformation">
          <DataField>HoseLenghtFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="HoseLenghtFLX_PalettePackageNoInformationFormat">
          <DataField>HoseLenghtFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="LabelLenghtFLX_PalettePackageNoInformation">
          <DataField>LabelLenghtFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="LabelLenghtFLX_PalettePackageNoInformationFormat">
          <DataField>LabelLenghtFLX_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="PackageNo_PalettePackageNoInformation">
          <DataField>PackageNo_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="YourReferenceFLX_PalettePackageNoInformation">
          <DataField>YourReferenceFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ItemNo_PalettePackageNoInformation">
          <DataField>ItemNo_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PalettePackageNoInformation">
          <DataField>Inventory_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PalettePackageNoInformationFormat">
          <DataField>Inventory_PalettePackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SalesOrderLineNoFLX_PalettePackageNoInformation">
          <DataField>SalesOrderLineNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="QrCode_PalettePackageNoInformation">
          <DataField>QrCode_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="QrCodePO_PalettePackageNoInformation">
          <DataField>QrCodePO_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="QrCodeQty_PalettePackageNoInformation">
          <DataField>QrCodeQty_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="QrCodeQtyLabel_PalettePackageNoInformation">
          <DataField>QrCodeQtyLabel_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ProductionOrderLineNoFLX_PalettePackageNoInformation">
          <DataField>ProductionOrderLineNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Ship_to_Code_FLX_PalettePackageNoInformation">
          <DataField>Ship_to_Code_FLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PackageNoInformation">
          <DataField>IDmmFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PackageNoInformationFormat">
          <DataField>IDmmFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ODmmFLX_PackageNoInformation">
          <DataField>ODmmFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ODmmFLX_PackageNoInformationFormat">
          <DataField>ODmmFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ProductionOrderNoFLX">
          <DataField>ProductionOrderNoFLX</DataField>
        </Field>
        <Field Name="BPbarFLX_PackageNoInformation">
          <DataField>BPbarFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PackageNoInformationFormat">
          <DataField>BPbarFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="WPbarFLX_PackageNoInformation">
          <DataField>WPbarFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="WPbarFLX_PackageNoInformationFormat">
          <DataField>WPbarFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SelltoCustomerNameFLX">
          <DataField>SelltoCustomerNameFLX</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="HoseLenghtFLX">
          <DataField>HoseLenghtFLX</DataField>
        </Field>
        <Field Name="HoseLenghtFLXFormat">
          <DataField>HoseLenghtFLXFormat</DataField>
        </Field>
        <Field Name="LabelLenghtFLX">
          <DataField>LabelLenghtFLX</DataField>
        </Field>
        <Field Name="LabelLenghtFLXFormat">
          <DataField>LabelLenghtFLXFormat</DataField>
        </Field>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="YourReferenceFLX_PackageNoInformation">
          <DataField>YourReferenceFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ItemNo_PackageNoInformation">
          <DataField>ItemNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformation">
          <DataField>Inventory_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformationFormat">
          <DataField>Inventory_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SalesOrderLineNoFLX_PackageNoInformation">
          <DataField>SalesOrderLineNoFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ProductionOrderLineNoFLX_PackageNoInformation">
          <DataField>ProductionOrderLineNoFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="Ship_to_Code_FLX">
          <DataField>Ship_to_Code_FLX</DataField>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="Rectangle4">
            <ReportItems>
              <Tablix Name="Tablix2">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>5.45276cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>5.54271cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>6.15068cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>1.20704cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ItemNo_PalettePackageNoInformation">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=mAX(Fields!ItemNo_PackageNoInformation.Value)</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ItemNo_PalettePackageNoInformation</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>None</Style>
                                  <Width>0.25pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ItemNo_PalettePackageNoInformation2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>="P.O. # "+Fields!BarcodePO_PalettePackageNoInformation.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ItemNo_PalettePackageNoInformation</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <rd:Selected>true</rd:Selected>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Tablix Name="Tablix8">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>6.15068cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>1.20704cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="QrCode6">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!BarcodePO_PalettePackageNoInformation.Value</Value>
                                                    <Style>
                                                      <FontFamily>IDAutomationHC39M</FontFamily>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>QrCode</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="Details7">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!PackageNo_PalettePackageNoInformation.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.9901cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox16">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox16</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Style>Solid</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Tablix Name="Tablix6">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>11.69339cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.9901cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="QrCode4">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!BarcodeItemCrossRef_PalettePackageNoInformation.Value</Value>
                                                    <Style>
                                                      <FontFamily>IDAutomationHC39M</FontFamily>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>QrCode</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>Solid</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <LeftBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </LeftBorder>
                                              <RightBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </RightBorder>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="Details5">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!PackageNo_PalettePackageNoInformation.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                            <ColSpan>2</ColSpan>
                          </CellContents>
                        </TablixCell>
                        <TablixCell />
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.99687cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Description3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Max(Fields!Description.Value)</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Description</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>None</Style>
                                  <Width>0.25pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox11">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!BarcodeQtyLabel_PalettePackageNoInformation.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox11</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </Border>
                                <TopBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </TopBorder>
                                <BottomBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </BottomBorder>
                                <LeftBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>Black</Color>
                                  <Style>Solid</Style>
                                  <Width>0.25pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Tablix Name="Tablix7">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>6.15068cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>0.99687cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="QrCode5">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!BarcodeQty_PalettePackageNoInformation.Value</Value>
                                                    <Style>
                                                      <FontFamily>IDAutomationHC39M</FontFamily>
                                                      <FontSize>9pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>QrCode</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <VerticalAlign>Middle</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <Group Name="Details6">
                                      <GroupExpressions>
                                        <GroupExpression>=Fields!PackageNo_PalettePackageNoInformation.Value</GroupExpression>
                                      </GroupExpressions>
                                    </Group>
                                    <TablixMembers>
                                      <TablixMember />
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <DataSetName>DataSet_Result</DataSetName>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details2">
                        <GroupExpressions>
                          <GroupExpression>=Fields!ProductionOrderNoFLX.Value</GroupExpression>
                          <GroupExpression>=Fields!SalesOrderLineNoFLX_PackageNoInformation.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                        <TablixMember />
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>DataSet_Result</DataSetName>
                <Height>3.19401cm</Height>
                <Width>17.14615cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>0.28259cm</Top>
            <Left>0.09525cm</Left>
            <Height>3.19401cm</Height>
            <Width>17.14615cm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>3.4766cm</Height>
        <Style />
      </Body>
      <Width>488.7326pt</Width>
      <Page>
        <PageHeader>
          <Height>1.84078cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Image Name="Image1">
              <Source>Embedded</Source>
              <Value>greenlinepalet</Value>
              <Sizing>FitProportional</Sizing>
              <Top>0.26458cm</Top>
              <Left>0.09525cm</Left>
              <Height>1.44708cm</Height>
              <Width>17.14615cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>7cm</PageHeight>
        <PageWidth>17.5cm</PageWidth>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>0.1cm</LeftMargin>
        <RightMargin>0.1cm</RightMargin>
        <TopMargin>0.1cm</TopMargin>
        <BottomMargin>0.1cm</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public RowColor As Boolean = 0

Public Function SetRowColor(ByVal val As Boolean) As Boolean
    RowColor = val
    Return val
End Function

Public Function GetRowColor() As Boolean
    Return RowColor
End Function
</Code>
  <EmbeddedImages>
    <EmbeddedImage Name="RDMLogo">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAkGBxQSEhQUERQVEBUWFxQYGBUUFxcUFhcZHBQXFhgUGBcYHCggGBolHBcUITEhJisrLi4uGB8zODMsQygtLiv/2wBDAQoKCg4NDhsQEBosJh8kLCwtMCwuLCw0LCw3LC0sLSw3MDQsNCwvOC03LDcsLTcvLC4sLCwsNy0sNCwsLCwsLCz/wAARCAB8ASIDASIAAhEBAxEB/8QAHAABAAICAwEAAAAAAAAAAAAAAAYHBQgCAwQB/8QARxAAAQMCAQcFCg0EAQUAAAAAAQACAwQRBQYHEiExQVETYXGBkSI1U2Jzg6Gxs9EUFyMyNEJScpKTssHCJDOCovAVJUN00v/EABoBAQEBAQEBAQAAAAAAAAAAAAABBAUCAwb/xAAoEQEAAQMDAwMEAwAAAAAAAAAAAQIDEQQhURIxQQUikRNhgaFxsdH/2gAMAwEAAhEDEQA/ALxREQEREBERAREQEREBERAReLFMUipmGSd7Y2je47TwA2k8wUIq8tZai/wZpgh3SO1yv52jYxvObnoXum3VVGfD43b9FqPdKdV1fHCLyPDebeegbSo1XZZboWf5PP8AEe9RiCF8rwG6Uj3cSSTzklTXBMmGRWdLaR+231W+886YiO7HTfvX5xb2jl4KJtbVazIYWHeBo3+6BrPapBRYSyPWdKR325CXu6r7F77L6vMy127EU7zOZ5kCIij7iIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiLw4xi0VLGZJ3iNo47SeDRtJ5lYiZnEJMxEZl7VBMsM48VNeOnAqJthN/k2dJHzjzDtUIyxzhzVd44bwQ6xYHu3jxiNg8UdqjuA4Xy79epjbaXP4oXRtaOKY67vw51/W+LfyzNIyatk+E1jzJ9lp2dAGxrfWpTh1C+Z4ZGLnedzRf5x5l00NK6WRsMIGkRfxWN2abrbBwG86lZODYWynj0Gaztc8
7XHieHMNyz3rvVv8QzafT1X6uqrt/Zg+Esp2WbrcfnOO0+4cyyCIsrs00xTGIEREehERAREQEREBERAREQEREBERAREQEREBERAREQEREBEXy6D6l1hMocqaaiHy8g0rXEbe6kPQ3d0lVHlVnDqKu7I/6aI3Gi03e4eM79h6Vos6Wu727cs93U0W+/fhP8r84kNJeOC1RNrFge4YfGI2nxR6FTuM4xNVScpUPMjt3Bo+y0bAF4Ai7FjTUWo278uVe1Fd2d+3Duo6Z0rwxu0+jiVNqdhjMdLTN5SZ+oDcOMj+A3rAU8wpI9mlPIBq+w3dfn32VtZusljSxGabuqiaxcTtY3aGdO89m5ZtVc2zPbxHM/4mnsTdqx4ZrJnAm0kWiDpvd3Ukh2vdx5gNgG4LMIi5MzMzmXdppimMQIiKKIiICIiAoFnBy/NC8QQMEkxaHOc75rAb2Fh85xtfmFlPVr7nSP8A3Oo837JqDuOc7EL35RnRybbKa5u8vZ62f4POyO/JufyjLtPclosW6wb6W3Vs3qmVOszXfE+Ql/XGqLzREUBERAREQEREBERAREQEREGMZlBTl5jMrI5BtZIeTcOp1r9SyLXX1jWOI1rEZR5NwVzNCdusX0XtsHs6D+2xUrlPkhUYc7SuXxE6po7tHQ8A9yeu3OtVmzbu7dWJ4Zrt6u3v05hsEvLWYnDD/dlji++4N9BK1nfVyEWMjyOBc4j1roDRwWqPTeav0zT6hxT+17YtnMoobhjnVDuEY1fidYKA47nOq57titStP2DpP/GQPQAoQi029Hao8Z/lmuau5X5x/Dk95cSXEuJNySbkniSdpXFEWtlFyil0SCLEjZfjxXAld2HUbppWRMF3SOa0dJNuxSZ2eohO81GTnwic1Uw0mRO7nS16cu2/Po6j0kK6F4MBwplLBHCzYxtr8Ttc7rNysdlDlnSURLZZLv8ABs7p/WB83rsvz+ou/Vrz48O7YtfTowkKKqKzPJr+
RpCRuMkuietrWEeleZueOXfSRnolcPToFfB9lwIq6wfO3TyENqIn0xP1gRKwdJADh2KfUlUyVgfE5sjHaw5puD1oO9EUayiy4pKMlsj9OQf+OMaTug7m9ZQSVFU1VnjN/kqQW4vl1n/FrLDtK4QZ5H37ukaRxbMQR1Fhv2hBbi18zo986jzfs2q2smsvKSsIY1xikOyOWzSTwab2d1FVLnR751Hm/ZtQRVTrM13xPkJf1xqCqdZmu+J8hL+uNUXmiLBZQZW0tFqnlAfb+23un83cjZ1qDOoqrrc8bQfkaVzhxkkDD+FrXeteNueOW+ukjI4CVw9OgUFwIq4wrO7TvIFRDJT33tIlaOkgB3+qnmGYlFUMEkEjZWne03tzHgeYoPWi655msaXPIa0C5c42AHEkqEYznTo4SWxadU4fYsGfjd+wKCdoqhlzxyX7mkYBzzFx9DAu2mzyG/ylILbyybX1NLNfagtlFFMn84FHVkNa8wyHYyazSeYG5aeoqVoCIiAuE0YcC1wDmkWIIuCOBB2rmiCqMtM2Vry0A4l0B9cZ/iergquc0gkEEEaiDqIO8EbitpyFDsucho60GSO0VQBqdbU/xX/s7culptdMe2525c/UaOJ91HwohF6K+ikhkdHK0se02LT/AM1heddWJzvDlzGNpF8JX1cChAArKzM4NpzSVLhqiGgz77hrPU39SrdbC5B4eKXD4gdRLTK887hpG/QLDqWPXXOi1jnZs0dHXczwwGdDLV1KBTUxtM9t3vG2Np2AeMfQOkKlibkkm5Osk6yTxJ3levGMRdUzyTu2yOLugHYOoWHUvIuK7Ai7qOlfK9scTTI9xsGtFyf+cVNaXNTWubdxhjPBziT/AKgoIIrGzK4o9tTJT3Jjex0ltwc0tFxzkG3UsJiubuvgGlyXLNG+I6Z/Dt7F7sFppMNoZqx4MU045CBrhZ7QdbpbEajYGw5kGdzkZwHNc+lo3aNtUkzTrvvYzhbe7qCq
lEQEXrwrC5ql/JwRulfwbuHEnYB0qVDNdX2voxX4coL+qyCErvrKt8rtOVxe6zQXONyQAALnfqAXoxfB5qV+hURuidtF9jhxaRqK8KAp1ma74nyEv641BVOMzjrYgTwp5f1RoJrnNy2NIBT05+Xe27neCadhHjHXbht4Kk5HlxLnEucSSSTcknaSd5Xtx7ETU1E0zjfTe4jo2NH4QF4UBF2UtO+R7Y42l73GzWtFyTzBS2PNliBbpcmxvimQaXZxQQ5ZHAcbmo5RLA4tOq7fqvH2XDeF04phktNIY543RPGuzt44g7COcLyIJLllllNiDgHfJQi1ogbi/wBpx+sb7OCjSLnBC57mtY0vc42a1ouSeAA2oOCKYU+bPEHt0uTYzxXyAO7NyjmL4RNSycnURuida4vsI4tI1EIPCQrNzYZcuY9lJUu0mO7mKRx1sO5jidrTsHDV1VmgPDVz/ug2tRQLCs4UXIRcoe75OPS1/W0Rpem6KCeoiICIiCHZw8kRWxacYtURg6B+2NvJn9uB6VQ5HUtqFROdbCBT1pc0WbM3lNWzSvZ47df+S6mgvzn6c/hzddZjHXH5Qxy+AK5mZtopMPijNoqkN0+Utr0nayx3Fuwc1lUmJUElPK+KZug9hsR+4O8Hittm/RdmYjwx3bFVuIz5MLpeVmii8JJGz8Tw391sdjrdGknA1AQSgW3WjNlRWbyLSxKlB+2T+FjnD0gK98oPotR5Gb2blz/UavdTH2bvT6fbM/drCF9XwL6ue6C38yOGNEM1QR3bpOTaeDWta426S7/VWcoLma73+el/ip0oCprPfWk1MEO5kRf1veW+gR+lXKqKzx98fMxetyCDrsp4S97WN1ue5rWjiXEADtK61mcirf8AUKTS2cvF26Q0f9rKi/Mlcn46GnbFGBfUXv3vfbW4/sNwWYRFBjcfwWKshfDMLhwNjvY7c9p3ELW3FKF0E0kL/nRuLTz2O3r2raRa9Zzbf9SqLcWX
6eTbf0oIupjmrP8AVy/+rUetihymWapn9VMeFLP6dD3KiFs2DoC5LizYOgLkgtnMhhjdGeoIu7SETTwFg51um7exWoq7zI/Q5vLu9nGrEUEPzpYO2eglfYacDTK128But46C2/YFQS2YyqH9FVeQm9m5azBB9VtZk8GboS1TgC/SMbCfqgAFxHOdIDqVSq8szXe/z0n8VRO1Ds6+Gtlw6VxHdQlsjTw7oB3+pPYFMVgMvu91X5GT1KDXFERUEREG1qIigIiICiOWeCCpqcPJFwyZ+l93QEljzExtHWpcuqWEOc0/ZJI62kfuvdFc0TmPu8V0xVGJdgUUy/yRbXxXZZs8YOg77Q28m48DuO49algRSiuaKoqp7rXRFcdMtfs3bSzFKdrwWuD5GkHUQeTeCD1q8cofotR5Gb2blDMucDENXTYjGLBs0QnAH1S4N5XsJB6lMsoPotR5Gb2blp1dyLnTXHDPpbc2+qieWsQX1fAvqytS88zXe/z0v8VOlBczXe/z0v8AFTpQFTufCgIngn3PjMZ5ixxcOsh5/CriUey7wH4bSSRttyg7uM+O3YOa+sdaDXNc4JixzXtNnMc1zTwLSHA9oC4yMLSWuBaQSCDqIINiDz3XxUbI5I5Rx10DZGEBwsJGb2OtrFuB3FZxatUFdJA8SQvdE8bHMNj0HiOY6lJG5x8QDdHlgfG0G6XbZQXflBjcVHC6aZ1gBqH1nnc1o3krW7Fa91RNJM/50ji4jhfd1CwTE8TmqH6c8jpnbLuN7DgBsA5gvKqCsfNBRXbWzbhFoDpIc4+gBVw1pJAGskgADeTqAWwOSeA/AsNMbv7jmSSSffc3Z1ANb1INfGbB0BclxZsHQFyQXTmR+hy+Xd7ONWIq7zI/Q5fLu9nGrEUGLyp+hVXkJvZuWsoWzWVP0Kq8hN7Ny1lCD6ryzNd7/PSfxVGq8szXe/z0n8UE7WAy973VfkZPUs+sBl73uq/IyepBriiIqCIiDa1E
RQEREBERAREQdFZStlY+N40mvaWuHEEWKxuIX+Azhxu5sErSeJEbhfr1HrWZXRUUjXte1w1SNLXWNrgt0T0GyudsJjfLVgL6r5+K7DvByfmye9Piuw7wcn5snvUV05mu9/npf4qdLG4DgkNHFyVOC1mkXWLi43O3Wde5ZJAREQV5nCzffCiails2a3dMOpstt9/qv59hVNVdM+J7o5Wuje3a1wsR1futqFjsYwOnqm6NREyXgSO6HQ7aEGsaKystsg6amaXwulb4pc1zR2t0vSq1VBc4IXPc1jGl73GzWtBLieAA2qxcishKaqbpzOlOzuQ5rWnsbf0q0cFyepqQWp4mx8XWu49LjrKCFZu83pp3NqasAyjXHFtEfjOO9/q9VgYl/Zk+4/8ASV6VwljDmlp2EEHoIsVBqmzYOgLkr5Ga3DvByfmyf/SfFdh3g5PzZPegx+ZH6HL5d3s41YixWT2AQUUbo6dpa1zi4hznP12A2uPABZVBi8qfoVV5Cb2blrKFtRWUzZY3xv1te1zXAG2pwIOsbNRUR+K7DvByfmye9BQyvLM13v8APSfxXf8AFdh3g5PzZPepFgOCQ0cXJU4LWaRdYuLjc7dZ17kGSWAy973VfkZPUs+vNidCyeJ8MoJZI0tcASCQdusawg1bRXz8V2HeDk/Nk96fFdh3g5PzZPegoZFfPxXYd4OT82T3og//2Q==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="greenlinepalet">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAApwAAAA/CAYAAABNXBjMAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAADZJSURBVHhe7Z0HeFVF08dBUBBBKTZQQVEBFbCBXUTUj1d5RTqCAtLtSkdAQRREilSlhpqQ0AmETgKE0AJpEHovgSQkIYRUCMy3/7lnL3tPboq+IkLm/zzznDZnz569Kb87uztbgEQikUgkEolEomsoAU6RSCQSiUQi0TWVAKdIJBKJRCKR6JpKgFMkEolEIpFIdE0lwCkSiUQikUgkuqYS4BSJRCKRSCQSXVMJcIpEIpFIJBKJrqkEOEUikUgkEolE11QCnCKRSCQSiUSiayoBTpFIJBKJRCLRNZUAp+i6KCn1Ii0PPUnT1x8kj4ADtC8q0boiEolEIpHoZpMAp+gf155T52j40l00ZHEE/b5qj7K91H1msECnSCQSXWNdsbbZ6cqVrB444+4+d77Z6cKFCxQcvI28vLzot5G/0aDBg2jwL4NdbfBgPs/X3FzH+SG/DiFfX19KT0+nNGWXMjOtJ+Qu1DenOud03d353Pyzu5Zf9a8EzoxLmbTrRAKtDD9FS0NOKjthGfazM8f1JdtPUNC+GErNyPsPoSh3pV/MpFURUbRkh/1zOKHOnSS/0JMUnZhqeWev2PNpDJp+6r6B88Jo1oZD1M8nhCas2UdzNh+xvEQikUj0d+vy5cuUkZHBkGaHIRxnqvPp6jr8tHDu4sWLdOnSJeuMwxdlZKjzZjl6/8iRI7Rs2TKa4uFBH7ZoQc/XqEGPP/4Y3XPP3VS4cGEqUKDA/2R33HEHVXmiClWqUolq1KxBrdq0pskeU/h5QZs2UZKCW3dCffEu9nfXwjty+xjvCqE90C5oCy1uS1UWzJ1ye1Z+1L8OONftPkONRgRQxa/m0j0dvKlMe68/ZSXbzKIGw/wpNjHNKlH0dyj+QjpV776Y
Srf1pDLtXNu81CeeVGfgCjoVn2J5Z6+AyNMc0Vy9M4o6T9pM3WcFU9jReBq5LJLW7462vEQikUj0dwqwBAgyYZLBUcEVzH6eQdMEL3VOQxbOa3/47t+/n0aNGkUvv/IyVateje4vez8VvvUqWBYsWJBuKXQL3VbkNnr2uWepcZPG1L5De2rbri1bm0/asHXs1JG69+hOffv2pW7du7HPJ20/YR9sW7RsQf959z8Mm4UKF+IyUbYdRvGMb779ln7//XcKDAyks2fPugU/vKP5/qYPjvGeJnya7eLON9NqE5F7/auAE9GyR76cSwWaTKECjZU18bhqOG6UB6s/ier/upbHCIr+PiEyeX9H76yfC6ypB/Xx3pFrVw0E4By0KIJ8Nh2htTtP029+kTR3y1EavXw3JcpnJhKJRP+oAEt22MK+Pu9OiNxt37GDuvfsQS+9/BKVKl3KBfpgd955J9X9T13q/Gln8vDwoJCQENqzZw8lJiZmW25ehGcDIHfv3k2bN2+mX4b8Qs2aN6OGjRrSAw8+QIUKFXKpR9Hbi9Izzz5D/fv3p3379lmlOGS+p/n+EI4Bl2ZdcU7fY8qdr12ICNvBPr/pXwOcR2IuUI3eS6hAw8lUoNnULHZ3ey+q8Plcw+ZkOX7wUx8q2mI6fTx2g/qmkRf8EeVViELe2WYWw6XLZ6OAs3Q7L45M50VnFbj+sngnj+HcsCeaRirgHLtiD0Ul5B4dFYlEItGfE8OUtQ8w4kicgh/suxP8dRQTMv1w7eDBgzR6zGh6vdbrLmBXpEgReuihh6h8+fJU+83a9Ouvv1JsbGyW56AMbVoMbOrY7OrHdcCl6Ze9rtD584m0ePFiatO2DT3y6CNU7I5iWaKfderUIW9vb0rPSLfuu6rL6rl4tr2+OHZXlys4p9pIt1Nuwv15f5+bU/8K4MTnO2pZJBVEtMyEGQtoXu7nR4uDj1PwoThlZ7OxWNq4N5omrd1PC7Yes0oW/V2avv4A
3apgvkBT47PBfmMPqt5jMZ0+l/v4Ta2ktIsMsJv3x9DhmCT1C5t/fwFFIpHoWgmwBHgEzAGjcAyocwc9uAYQdU7GMUDq9JkztNh3MXXq3IkqPlqRbr31VgY4dJsjgvhG7Tdo+IjhtHPnTjp56hTtioykNWvW0MKFC2nO3Lk023s2rVq1iqObeAaUkpJC69atc/jMmcPm7eND8+bNo/DwcPbRsIc6Ywwl3iVZ3ZeSmv3/G9T/8OHDqixv7oKv8HAF7nqHoc6VKlfmyCzGmMbExDjfWUcoXXHTIV0Pvq723Z0zr2mhTLP987v+FcCZmJJBdX5cQQUaTrEBjYeCnGkcAfsrwrjDY7EX6PjZrJac7vhlSs24xGMP9Xn4xydd/faTmJpBJ+OSXe7NzeCfYpVvF971xFmUl/cyT8aluEyCOqvqd8yNH+p+PlV9i8q87PJOrpbM7fJn9aXHZseQBf3ZGJ9R63GBPKkoJ6Wodna8t6N9UL9T8dg6zl1Q9U5TZeCa/d1wHKX88Q00Idn9u8NQZnoO8Bqn3vvEn/oskykqIZXbUyQSiW50AYgAkoA3ROYASTrypscfmuCE/VMKIH/++Wd68sknnZHCgrcUpLLlynI3NiKGJ06ccIGt+Qvm06uvvUrFixen24vdzvegWztSgagGztOnT1Pz5s2pbNmyHB29o/gdHJUsU6YMj+HUuqz8UT9M5kEdOfqqLC/CPWFhYfTjwIH08CMP0623OUAZVrxEcerWoztNmz6dPL28nF39qJ+uoynddiaIa+Eat6O6hjoyYKpzMN2e2Jptmx/1rwDO0KPxdH8na3ygCZxNPOjeDrNp0/4Yy/PPCZNTKn89n57suvCqdVlAz/ZaTMtCT1Lg3mhqPz6Inu3pq847rlf+Zj6NXb5HAdRlmrHhEDUZ4U/Vuy9yLcNu1r28VfZ0j8XUdvxG2rw/1qqJI++kR8B+ajTMn6p2y6Y8XY7tHMpr98dG2nH4LJc1ZPFOt76V
v1lAw5bspAHzwtQ7LVY+C1x9rHveHbyaJq3dx4CXF11Iu0Rv8hcC23AH67Ma4RdpeWavHYfj6JV+fvSEmzpV/nYBLd1xgg5Fn6f/DFpFT6hj0wfH/x2yhrw3HaH3f12jynC9X2+f7+WrwHgLRRyLt57qEGB1xNJdXHbVbtY99jLMY8Ne+G4JdZsZzJFYkUgkutFkAo7eN+EnOxhKTUujiJ07qWfPnlShQgWGNEQ0MVkH8PZdn+841ZEpfT+ilc/XeN4RVSzoADxM9Nm1a5cLcDZs2JBKlizJ13kCkALZYsWLUa9evdgHQolXa+UQ6grAM+ubk+IT4mnc7+MYevEOOuJZ4s4SCnLvoIqPPkozZ82iM9GOiatmudjT3e1a5r6uC3wAnGg3na7J9AOosp/yz6/6VwCnR8BBuq3lDI6WuQBNoykMgzHn/9qM8zErdjsgCSCrTR2XbudJXygwefiLuVSggXFdPa/wh9M4EXlfnxAqjDrY70eUD+e0NbJd19ZgEtUesJzrjujkN9O3UqEmRnn6vizlWfebhnP1J9I7P62kxJSLnFaIYc+Nb8Uv5znaDu+VXXnq/C2qrUct252nMP/uU+eobGcfa4KQKlt/KVDHpdp60oqwU5Zn9kIXOsbYcp3Muqh6lmg9izbuieZoJ+Aebefio96h5Cee6suH+lLygbqG69m9myoPPzNnrC5+THYCpLKv/dn6nDZ35eGc+qw6TQxyRgBEIpHoRhCAR3fpwjgCZwMhCOcASsmpqc7/CQDCiRMn0p133ekEQsBa2QfKMrwBNgFVXL7aAqSwD/nM8aGaL9R0wh3uf+LJJxg4dYQwKiqKGjVq5JxwpGEWkceevXpyHTFeFBHK0NBQ7maPVPej2/7YsWN/CdwmT5lCr9eqxcMAnHVTQIx9zKzH2NRoBZ2AR9QTz0C76e58vCdMgyPqyD7qnL1NIe3LEU/rXH7WdQdOfEhdpm9z/GPXoKmt4RT65PeNPKEEUDNv
y1Gav9WN4bx1DbkcA3ad5i5eRAK5XDNqqkCr2Mcz6M42ng6gAERpU8eYJf/Logi6o9VMx3WzPqqcW5pPowcUOD2iwA6+iMAyKDufofaxbexBjyqfXccTOHdlkZbTHc9wKc+DCinANcvD5KiCzQ0fNnWfeo+HPpvDXc6YEV6Q7zd8sI96qGeU6zyHy4IBCF3LskyB2Ws/+HEXdW5asuO4oy34GUYZqk7P9FxMh6Jzj/5tORCj3k21FdpAl4GtAr1qCjLRzY+E8IjSOp+lnwNT5/CZON5rHj2o2gLDLVx8YKoNCqrPyCvoMD/3V1/1M6A/X5vfbeozKf+F1VZfzaO7FNRmeS5MPfuFPkvzHBEWiUSiG0kAozQFnHpsJLqXZ3l60ksvv+zoEldQBhhEd3fzZs1p69at7If/37hXw5fWnLlzOD+mCZzokgdwplrPOHHypFvgvKvkXdS1a1faGbGTXlbPR5f7gw8+yJORHn74YapSpQr16dOHy/grmjpV/c99oBx3r+N5eC4iq6jDDz/8QKcUCEP2qKZdgEhEMgGj2cmEUXftlN903YHzbFIa1R20yhU49T99dW786r3c/X1fR28GhKIKFrPYR8rUNUAdImFjVuzhbuC2f2x0RK50uWb5qmxAW0kFZAAhbDFpCRNgPhqzPut96h7AYS+v7bT75DnaG5VI+08n0pqIKHoEkVIFmC7+ClLQFQ7gbD8hiOHZ5boCHkzC+WFOKEWeTHCWt3ZXFD30+RwHdJn+6rha98V0NPYCNR4R4NpelqH+P84Poz3O+p1nSMcMfidwGW2L+gFgc1M/n1AGVPNZjjImU6Ph/pSRh0k/SPBexB7Fxr5q56a/BbAP0mLxTHg7HFptuWHPGW4jvFvkiXM8HMJRjuGr9vG5jlu5h9IyMumlfn5ZPxv1XES5J67Z5yzvwJnz5Lnx8NU6mmWq+rz3y5o8vadIJBLdCAIw2aN2Gp4wbrP3d70ZwgCMADJEBZECSQNZ
TuDkDjh1hBPPghBBzQ44u3TpQsHbgnnWO64BeFEHGPJsfvrpp1xGdkLd8G6IUtrrOXv2bAZYAOcthR1RW13HDh060IYNGxi47ffhmLvFrQitO2kfRIvtz8Yem63c/KTrDpzhx+IdXdt2IFLHxVvPIn8FYEMRpQLwwABa8IUxGFiGYwUvmHyESTWYNc1plkxwQ9kWTLz+wzIasTSSxw6u3XWaV74Z6ruLenpu5y7ZrMDnwemXQo7EWTV3COmcOMqp6+P0n0L1hqxmOEXC9KzlOSKW9rGB6L5GxDOLv3rvJgo08XyMz8xyvckUur+zd5blITH+tRzKQ/1Mf9VWaANzgpQ7Ib3UOz+vzArMqrxb1Lb/3FDLM3tlql+wLjO2OaKydlPvMXTJTvZDuiTHZ5TVB/fb9fmUzVfb3NgWViDvE3SE9qq25Eh1lvI86MU+SylZfSkxtTzsJH+pcfG36oOfi3z8d0IkEt3gAlACpNCVDaBMSnL936OBCMJ1RBEBYYBAgF6RokXoxZdepOPHj7MPoAoRO8zwBlyZchvhfOpJzsOJeyAAZ+PGjal06dJugTNkRwg9/vjjzuiqLgsR1w4dO3AZOQlgp80UxpdWrFiRihYt6ixXP7t0mdLUUEHwYl9f5/hUswzUXdcf5wCfMH3e/ixEczETHm2Omf4XknMP8NzMuu7AiW5wxz95GxApyMB4Pkwo+mzyZnpYwdmjX82j8p/PcUShTF/jnl8VtEBbFGiV0V24Nh+UA9B1J0TRbv/Y/XjS53v70vbDZ3mWtLZp6w5k9VWAArj6bnYIL8/p7Ep2Xlf7qh4A4p0n4h1lxTrKQwJ0RGzdASKGCGAJSeS9zHK9wRSe2HMhzXX2HiYHYVxqljoqeG89bkOu40oQAa3AEdyswFmizSxCuqrchITunIXAXoayW5pP5XRW+LbdfOS6rO+l2hKf92yri1wrToEyoJH9TUBEvVp7cmR5+oaDzjKc1/nYg+EdQwH053hc
vSeg1sXP8kUkGtFPkUgkupF07tw5mj9/PufEbNqsKU/ceaTiIwxWNWo+z2mOunXrTmPGjqWQ0KvBA3fAiQgnurjPKHCCTPCyCxNwqlWvzvdp4KxUuRJt3LjR2aWO5S/fr/8+lSxV0gmV2BYtVpS++uorCg8L5zIKFXaAJoOh2i9RogR99vlnXEZuwhhTDYRaPj4+9MgjjzBAuwDnrYU4aXytWrUYSjHEAFqxYgXXs1mzZtSxY0deCx46eOggJ50HoH799decigmpnpCM/vvvv6eu3brS/9X9P05Gj0lWj1d6nHOXYgWl0LCwHFM73ay6rsCJ6Flf7x38Tz3LP3oFWI1H+HOi8PW7zzAgovtz4bZjjhntGkw0TKgyEBFF9zuErnhH3khb2Qoc0dWeXWL4CWv3Z4UYmDq+q40nd+0iAoqZ7s/18uUopYsfTN1/+8czuTsbSzaiKz5LecrQ/e8oz2Eoj6Obpi/21TsUUe8CuAOQZhm/CWs4hb6ausV6C4eQ3xKztp3vo++x9kf67bY8sxfak6OEur21MbjPzxJRdaeIYwns61IG6qCOEd0+nZBCMYlpPFvd6aPrqo7v6TCbx3eawqx3DLPIUi91jNnlmKjVaVKQIyJuXrcMqybpzxGGLxNYsjOLryrvoU/n8BcNkUgkupGENERVq1ejW265hQ0TZpB/8tPPPqWHylvd1cpq1KhBM2fOtO4iOnnyJM8UxzXAXsFCCjiLFqWXXnqJUyBBiGrqcYz2CCdgcsSIEfToY486107HspbIiQl4+7DFh1TrjVoMvhpI4Qd/LG2Jbu1NQZucs+M1GMK32B2IcHbMNVgC6eikCcWA4XK2MZy6Dq3btOZ8oXFxjp5MdP9jeUxcg91z7z08Ox/atHkTvf3O23weMB0WFkqJiedo3LhxTn+06+jRo3nC06BBg+iJJ56gBx96kHx9fbNEmPODritwIgF4vV9Wu418ATjcddcuVwDkEhHVYNLYg55QoAEo
xQ9Xp4mbHD76ujb1LET93AmDhNuN36ggZXLW+2CAGwXCLsZd/DB1TZu6HzOykZCe64Hy7GXBMLbQbXlGWTB1/pEv5vHEG6Rb4ueZ5ai6oht5xvqD1ps4hPGx6DZ3jmHU76TaBVHcoH25p5sasijC8fnY20PVE59dXiYdeQcdoTvUlwF+F7MM9V4Nhq6li5lXKHBPtAJwNz7qGIn/kV/UFCLLHAm2f6FQ79pmXKDyz1D3LXW0qXkdhnfBO+k2h9mHa2hT12r1X85tKRKJRDeSAJzVn0aUsBDdd/991LZdOwVG4XT48BFeGjI4OJg2bdrEM8ExO1sLkbrly5fzmufIjwl4QhkYb1n/gw8oSN0D4X8tooforseYSRPsEhISeLb6z4N+pvfqvcewhigpytGgh2OsSoQucsxM95jqQWeiz6iCidavW+8WOHFPu/btGHb1OFRtPLtcQSLvG1FNCHVDdHXU6FHcFu4mDfXv35+7+rUA0uPHj3cCZLkHy/HEImjL1i28rjvOP1X1KQreHszd8EuWLKE6b9XhyGaZu8swZH700Uf0xZdf8LODNgXR+aTzLm2VX3RdgRPRMY5q2YETEb2WM3hcpSn8MPWfF3oVMkwIUrBQf+gaSr2YyalwagG0ABCmn9oW+3imArer+TFNYaY0d9O6gRREKUu396J7O87mMZtOw7E+Z+2XbuvJXbbIm+kAvqzvh/IQUXNbnm0LEMPEmr2nEukZHl9qvb82BUal2nrxZCFTSEXEEVOAEz/3qj/SJyUkZz+7DkrJyKQWo9c7YMx8HtpftW1vrx3qM7GcsxF+p/pi0pGug2kKxAdYXyqQy9PR9W/zUfchcotxoKa+VOeytqvDf9yKPTx2tizndrU9V9UdX1juaW+1r96a7W5YadWu307fxu8hEolEN5IAnE8/8zR3F993nwLOtm0ZLhFBnO3lxTA1YeJEWrRoEacgMnX+/Hle9QdRSBP60AX+9TdfU1h4GPsBypyp
f6w/lBpEtQCwADlETtFdz6b2ES3FuEY7fMEfKxW5A06M4cRQgOykn20vE/wAqC1foXwW0ET3OqKVc+fNpbNxcdwNz93x6r1M4AREauDcum2rEzirVa9GO6whCYDdSxcvUUxMNC1duoQ+++wzqlylsnN1pmefe5bfLTkfjue8rsCJWcmctkcDpDYFEuU/m0NHYl2TyqamX2KodIKkNoCGKqOvzw72A1DifoYcE2DUfU/3WMSrzbjT1gMxjnyTZkQQpuqD8aQLg49xsnhE47I1dX3D7jN04PR5Xivc2e2ry7LqgVRAiNbmtTzAOSKS3O1rwhvKVMfojseqSaZmBh7iFFDO9jWe30wB7KVcaBGr7TzVdZFbaENGgFmq/NyEbAENhvtzG7qUoeqMKOvi7Y4xoBhPmiWyrJ6DMZ6T/fezjxainTyRyf5zoMpEaiPk9FwUfJxuMT9H7aPqgcT3GKvrtr1NQ9urLcbXikQi0Y2mXZG7OPoGUEM+zaZNm3JUs/Nnnem+svc5QQpQOnXaVL4H4MhbBVyHDh2in376iR577DH2A6RhnCVyZTb/sDlF7t5NKdZYR0AeZrlzwnN1DOADsEF79+5lyEIEUIMgthgniclHgDQTEDPSM2jD+g1UobwDOFF/QCK2qMNjjz9GH33Uklq2bEEtWnxIzZo15WOPKZNp//79XAf9HtDZs2dpyJAhVLVa1augqd6jbLmy3MWNnKFYZhOQjboAgrFKEWzSZPV/yVqTHf79B/TnMgHtb7/t6FLHhKhtql2RKH/YsGG8glKDBg04tRTK2r1nD3Xu3Jnuuusuuvfee3nNd+lS/4eFcZZuJ7QokEBkEEnOTe08nsA5E92BRpGPpjvhBV3Lt37oZvxmg8nUYvS6bNPbzNxwiAo3V/XRQKdBpeFk+uT3wDyNGTGFBPKFUQ9dnjZV3sdjN1heeRcidzwu1SwPdVRg9enkzVm+0XWdEax8bW0FazglT6sDYa1ztxOo1PPw
OWBCV05KSb/IY28rfWOM39RtqsrA+FcsR4nI8kt9l7r9XMupLwCohynM5HdkNlD+NpgEyJ+IS6G+3iGOJPE4r+uvfAsqG7QowipJJBKJbl7tUaD3/vvvU7ly5RjWEJ1EVK7yE1WoZOlS3EWOcZVNmjQh38W+fI/ZHY0tZlmPHDWSVw4CXGlDWegOB1QdP3HCeQ/+D2nTKZAOHT7Mk2kAWYh0Hj12jGF1wcKFnCoJ0T746zIAeoEbAhl0UW+M/0QUUhuDr1EXGCb8dOvWzbkOOwSAxEQljEfFmNXCtznuA3Defc/d1O/7frzm+rz583gGP4T/ouZkI8AylvYErKK9sFoRxsC+9fZbXAZWK6r/QX06cvQoz0YfPnw4T8zCczBRqM0nn1CXrl3p1ddeY3+0o7+/v0Q4/2lhuUEnMGCrTYFGqXaevLJOw+H+1GCYP30wdC3DBFLxXL3HAgkFLwCTA2eSOGzebeY2Puf00/coQBmyyDGL3Z0Gzg9zdB+bgIV9BUJ1flzOoBOdmJqjAY4wsQdrpiNnp+OdjPJgqrw3Byynw9FJbsswLWhvDOcVRQqf9np8KdfLKtd6rym2KCBmqyOSlwXi1D23t5xBqyMcudRyEt7DFW6tfbXFhKe6g1ZzHk58Pqb9Rz237o8r6JupWxn+72xtm3SEchQc1uq/jJ+DVE8YEuDiA1PviiEOGCJhCl8sXL6o6LZQ74olMJH0v776eckyNEL5IVdphwlBPFHJXXub5que47XxsOTfFIlEN6QATegiDgwM5NnVmNAydOhQtqlTp1LAugA6cOAARwDRjQ1/dwKMIeIIcEP3McZRMuQVLsSTeF588QUe/7h9+3aera3hDcL/ZAAk0icBKtHND/jEikGYXARfRELRNa8nH2nQxXhITPKZPmMGG/ZhM2bOdB57zZ7Na6HP9vZmOATQAjonTJzAsI3xmhpKYRhT2bdfX4Y+TA7Cs3QkVkMvtqZwDqsbIYcnIr6YnY6Z
/yNG/EYLFy2i+Ph4njyl85miPVeuWMkTjuAL++nnnxScz6K9+/ZyW+RHXVfgRJcspzjSoAFwYDhQxwAIwB+gAVu9r3yzrMSjQAPwci45gxIupNMr3yPZt7so6AyGCHfCD1iPWcFXo2KmAVTUtlirmbwMI1sbY2tY4RbT6Ps5IZx0nPNE2iHKKA+zv13Kg7mUP5PHeg5aGKEAKI1qfrfkKkA6YcuDk6UHRF4d6AyFH42n8p+7SWeE6OQXc2nf6Zxnl1/MvExNkabI3s2tDe+FzyOLqfZDG9afSD/OC6N+PiFUCFFjXV8YylP3f+GxmZ81fvU+1+va1Ls2VABrV5cZWx3PN+vF+x7Uw3O7+nZ6hWqrLwhZcodahjZ1trfdVJtjW1x9NuienxV4+E9HtkUikejfIsDSnxX+H+rJN+bfP5SFcZiAvQYNG3LET4+DxCxzTJLBTHNEPrt3705r166lo0ePOlMh5UV4LsAzr/UGUO9X0ByxaxdNnDSZ0xRhmUozCoq6IcKJWeh+y/y4y96dAMaoK6DRDp3ZCX4AddwL4NRtl9f785OuK3Ci6/Ptn1Y4oMYtvFgG2PxgMk/44Uk4dth5fwLV/XklR/WQr/G2Fgpi61vrbWtTEATQyCmy57nxEBUFAOu1uu3Ai/M5mYKsUu28aLM1KQlRx9sxkxoApuuhy+R9dR7XYLoMfQx7fyLn3MQEmKkBBxzRXfjo+2HKD8AZtPfqDMNLCha5SxnAZb4DtqqO6A63j4+1C+Ngee1yVQeXZ9rLM835jEl0d4fZnMKqeo9F/Mwsfqre3WcGU1LqRXoBIG33galykJvT1P6oREd3uv3zRZnq5wgrN0F9Zu9Q5433t/vqNtbtbrY99v87kZezzG68r0gkEt1IAsCZIAcgAiBx9FEZIMkUrsMXEUp36IR8nIhAInJX77/1eEKNBjwY0jBh3Gj1p5+mNm3a0ODBg7m7eeXKlRxVRQQTM+NjomMoNiaWo4SJ58/z
OE90N58/n8SRQvhpQ5c1JjetXbuGo7T9+vXj3JZ3lryTu6s1ZOLZxe4oxkD8/PPPMfyGhIQ4c2tmJ7yzNrvcnYNwFm2kpduNIVSBK0yDaHZl5xddV+CEAFPoem49LpBajl5PLUetc2xNG7OeWo8NpN/8InlN9VZjNyi/q9dbjAzgJR3jLqTR2BV76EN13HK0UY7ybaHKxTO2Hcw+n2Jy+iW+H+M18Uzn/TDz2Hi2aXguxgfqFD7oVh+70lYe7jXLwr792PJDeQMXhFvrwkeoY/VOpq8yjElF+RHHr46nRJQXbYp3dvpb2xajAnjWN2A/J/mFnKTm7j6LPBg+D9QXK/e0wudqLwefh3oXQHTIkXhqo+qP93DxUYZysAqR+QuKMjFz3uXzhan3a6HKnW6lhkJezx/mhvLPDV+3t7s7g4+1j7abuu5gvv7jIBKJbh4BNp05M9XfNZ1SiGeXWz4QYAnnEeVjcLK2OSk1JZW70wcNHkQDBgyg+vXr8+QYE0C1YSUhzBRH7kqMccQEICx7+XyNGlS7dm3uBm/YsCHVrVuXqlatyj7a4IsxpwBJd2XDMAEKuTIXLFhAoaEhFKegFX/H8Z4mGOZVuBdthsk/uusdQpsA1AHs7oTreCa3r1WGCfz5UdcdOLWQiB2Jyt1a5tUfFHxwGFNnXudj9iHet193+ijLLuG7KTwK5dnLyM3wDHfl49T/Uh7eOf2S+iZqu659YOYvEvYzcvE33N0K9XV3f14M9/HnmUMZfD4TXQ+OfXd+Dh/XX04+fzGrL5/HVpVpCm3xV9s+H/9dEIlEN5kYgDIzcwUehjPlB8tN/PfVDZAiGonE6D4+3tSpU0d65523OR1Q8eLFqaA14/t/MUROMXkHa7tjNZ8u3bqS3/JltNRvKc9St0sD31+FPXf3Y58hMg/tJHLoXwOcIpFIJBKJ/nkBnhDVRMTOXSwCwAXLi7KLJGJS0pbNW2jN6jW8XCTGUqJLHvtIEzRt2jQa
OXIkDRnyCw0bPozGTxhPnl6etHTpUlq9ejV3xWOVJCSkDw0NzXHiDeqK98lrnXMT2gdtY0I4ysZ5E0LtApDqqDL8/kw73owS4BSJRCKR6CaXBiQNPIAnDVH6mglUOMZ4SszOxsxzzCjHGErto++BkH4Ik4MQXYRhfKfuasYsdEw0QhnI64n0QeeMWexx8fF07PgxLh8+Bw4eoENHDjuTxJ84eYJOnjpJMbGxXF90keNZqBe2B1WZyHMZFhHBqZaiY2KcgBefEM/3o1yM/cTY0X379qnjIzw5KCf4c7aJMmwZHG3tk1OXOoQy9DMQCc7L8ISbWdcNONMyL1J8RpLNLhjm7pr9nD6f3TWYeT0nP2158cnOzGfZz9uP/5fnaHNXjnmcl2f8GR93vjndn5ey82ooK6/lab8/8/ys5ccpS85Md/uNXyQSif7tQqSRodICJ3u3sAYinE9RAJZk5cOEMGEH66vff//93I1dokQJev31151LYAI8dRqlMWPGcAJ1nS6pffv2HJXEOuKvvPoKj7nEpB4kSEeaooqPVqSPW31MK1et5CUfq1SpTKWVD/zQ7X777bfzikKY+IOlNR+rVIk+atWKps+aSe/We4+fgWfpiUFYNrPM3XfzuR49e/AEJLznwJ8G8lhR5PHELHWMAy1XriyPL61Tpw717t2b0xuNHTuWZ7qb8Ij78X4wbiP1vmbkFsdos2RlaFt3gj+u4X60F0yA8x9WyqV06hU+k+qs7Udv+f9g2ffGvnnOft7dOX3e3NrNfh3b3O7Ji5nl5HReH9vP5bRv+pv7djN9zK1p5jl3/tndY/c1z5vnzPPmsf26eazP2e/LzXIqx37enWlf08xrji1+PltvGU1RqXHWT65IJBLdWNIACZn7pngspgIrdENrqAK0eXp6Ope2RML1l196maOXdqErHCCJJO3w7dipI3nN9qKXX3mZihZ1QGiNmjVorb8//fLLEKpVqxbdXeZuXkmob5++nD4J
uUJHjRpFNZ6vwf5Y6/yRihVp+PARtGtXJIWFh9NSPz9epxzXMSP9IQW5gwb9zMt14n7/gAA6cPDqZE+sClTugXJctz/++J2X0pwwYQIfm2NJ71awihybWP/dnVBedtFJnMuu+x6wiXY174Nfdp/Dza7rApznLibTu+sGUgHP+lTA6wNlDWxbvW8387w7f/vWtNz8czqPfdPsvqbZfU2z+7i7z37Ofmz30/s5+Zo+et9udh/T3Plmd800089u+rq5Nc3ua56zXzfP2/30cV6vGfvq57OK35e0L+mU9ZMrEolEN6YQXQMYYZsd8DA8WVE+RDiRbxO5NQFliB6+/LJ74AQoli1blhPBw7fNJ20YIj08PDhtkY58YrIPjrt06cIwi0Tt6LaHMCYT97z55pvsC8Ct9nQ1WrN2DV8H8CFZuwZOAGmpUiWpTp03qVOnTtS2XVtq1boVrxykx3cO+HEAp2tCXs7x4/9Qz0oib28feujBh/j+Wwo7ABkA6u3tnS1w5kUaSmFazja3gBNbfZwfkfO6dakfSDpNK8+E0vLTIbQiKpRWYHsaW23mMfbt1/V5+7nszPDl57k573Jsns/uOe7O41xu95o+ufka5mwnd372c9n5uDtvmvbJzi+3++1mL8s8tpUVZb9mHpvn7NfNc3Y/87xt3+XnAGb6OX42wxKOUOaV/NsFIhKJblwBgtBdfkUBjo625QScpgBfiPqZwPnKK6/kCThbtGjBUUcI4ybRZd3sw2a8rCYSwwM8K1epTL8O/ZXXHweEJaek8MSg2m/UdgJn1epVeQ12CO9hAicinFhW8rnnn6P3679P7773Lr3+xuu8whB8IQAnIpxYA93DYwqD6Ny5c+mhhyzgtCKyAE68K6K6dmlotLeZjmzyNXWM6/A1x3m6i3DmZ8mkIZFIJBKJbkJp4ATwMBQ5TjMIcZTNgignLBlwFHv2LEOY7lIHoFV5ogovJblaQeAKBYeYLQ5hDKfZpd62bVue
Xd6nbx9q1LgRffPtNzwbHROIhgwZQpUrV2Y/wOwYBaPxCm4BbgDON2q9wdcw7vKpak9lBc46V4ETMNn7u97kH+DPKZFgfn5+PBseS1z27duXc34CTDt06MCw2bNnTypdurRLlzrqMWPGDDpjjE/Ni3S7ISqs2zI7oV3xDnpMaH6UAKdIJBKJRPlIiMLxJBgFQRqEAHyAIS1EOH18fDg6CJDUE3gQncQ+YO/DFh9yl7jHFA8qX748T/QBwKGLG+DXrXs3euaZZ/g8JvY0adqUx3JiglHNF2oyqCICCgHaAJT13qtHxYo5JgPBJyAggK+jjuvXr6f36r3Hy2miDjDUp2SpkmzYv+/++6l+/Q/Ib6kfJ4BH/k/Aa/ESxdkwXrNChQoMz+3at6NevXvxakmYXa+HEwDGGbwVGGqoxJbby4JF+KQpf0RNNUBqXz37H22MdsU+DDPdEcnFu+RH6BTgFIlEIpEoHwrQA/iBMWAZAnxhkg0ilfPmzaOFCxfSgoULaP78+RwpRO7MTZs3871IaQTAhA+uBQcHc9QSMIqURJjQM/iXwXTvfY7VhxA1bf5hcwoNc0RIkeJo1OhRvD57pcqV1fUyPO7y1ddepY0bN7IPnoWu+kqVKnEXOnJ4Llq0iOujDfXExKLg7ds5+TxSIK1bt07VW9V9wQKu26qVKykiPJxTLmnANMVtAli02gSGcZnO88q0n33MppY5mx1+MC1zP79JgPM6CGu+p6Rf/SYpEolEItHNLEQysTqQnkCENEkjfhtOUQr8ENn8oMEHvMRl6zatqV69ehzFfKrqUxzVRHQQk4xKlXIsadn5085WqaIbSfkGOKPPpVLgnjOUZK1zHpeUTpv3x1B0YiofQ7Hn02jL/lhaGX6K1u8+Qyfikq0r6luJsjOqjE3qnlXhUereWErJcEAjytq4N5rWq/LX7T5N2w6dpdSMrGNAohJSaOyK3TR8aST1nxvG635jCcXsdDgmiQIiT3O5Aarc
/afP87cj1Bl1P5ecQedTMyhoXwzXdwP7ObaJ6j33RiXy/TjGdezvPeVIuHtcvdsmdR/gFzoZl8I+h6KT+BjvtPVALMVfuLqaA9aIjziewH5rdkZRsHpPDc54D5S9dtdpbr+DZxx1NYVvfXtPnaMdh+PUt0THex9R7+iv7tF19Fd13KmekZx2ibe4hnNoU6yPLhKJRKIbT/sP7Od1zjEZCNBYoGAB7mp/o/YbVPvN2pwv87/v/5c2Bm2kP/74g30YODesp8vq/wXGk6JbHvd16tzJKlV0IynfAOcviyLo4S/m0e6T5yjtYiaNXbmHqnVfxBAKMFq64wQ1/S2Avpq6hX7zi6TOkzbRWz+toNUKrCDc999f19D7yn6cF0Z/rNpL51Ic4fgJa/ZRhS/m0ucem+n7OaF8nGSBnKmp6/bTbS2mU49Z26nOjyvo7Z9W0tHYCwr6MJ7DcrKUpoD1k98DuY4D54fTgHmh5L/zNF/71Xcn1fxuCe06kcBQ/NuySKo7aBXXoevMbTRCAS3g1nfHcfpq2hZ6/Ov59J9Bq2mQaoPlYSe5jK+nbVVlLKWT8ckM0z1mBVO5Tj70wdC1dCo+hWYGHqJne/rSHgtQcV/LMeupk2qXAer9YaOW7aazSWlc98n+++m5Xr70lccWGrggnEHRroTkdKqv2q/V2ED+DCD/XVHUy3M7PdV1Ib3+wzIud1HwcQo9Ekev9POjFqPX0+jlu+nzKVuo3pA1zvqLRCKR6MYRurcxbhIgWbNmTerStQtVf7o6FSlShO25556jfv360a5duzivJ/yQqD1gXYAzwonJPjiPFEiiG0/5AjgRLXtVwcv9Hb3pBwWEAxXUVO22iF7su5RiElPpUPR5BjtAG6Kc0PZDZ6lYq5n06eRNfIzIXH0FY7X6L6O+3iEcHQRopStwajjcn0q29aReXttp0MIICj/mmloBQLtDAVTbPzZS9R6L6MFPfajZyHUK6g5SSwVUk9bup4tWxE/r
SMwFVd/ZVEOB5VAFmFPXHeRoJuD0rYEr6f9UXROSMzhqiCjle7+s5rIBmog24jyiiPO3HKV7O8ym3xUg43zm5Ssc7a3SZQE1HqF+kdUx2uDZnoup4bC1VKP3EvpGwSigEACIeu2LSqQHOvvQu+oZxxQgr46IogFzQxnMUQfIY90BeqbHYmqioP2P1XtdIsdaiMSWU+UAmLVQR7T1fepdv/PewSCKOk0JOEB3t/OiZaEOwNx+KI5KtJlFTVW76eioSCQSiW4MYalKJIRHRHPAwAEUtCmIBg4cSA0aNKBu3brxuE/HRJtLnEsT6Y8++/wzBlAoKCiIWrZsyZOGxo4by+dEN5byBXBuOxBLRVrOYCDcovanrT9IxVvNoq+nb+XriOI9oQAMwLn/dCIdjk6inp7bGYIWbD3K8AboAhiNXBZJ97SfTfUUfGWoX44Dp89T6bZeHK303HiYZgUeotMK+kwBFAGEJVrP4shh2/EbGQIrfjWPGitYxfPsmqYArmCzqRyJ1d3XlzKvMLSV7eRD/RXwae1W9Ud5X3pssc44BHDrPiuYHlCAG3nynHWWuMu7gCobUUloYfBxjm76bj/O73CPKutOBXeAZwiRVJxD++w4fJbmKIgFOL7zswN6MUwh9Ggcd9k3GxlABZpOpbmbj/C9pn5eEEF3qjaYop6L9jxpDVmYsGYv3f7RDB46AKVfyuRoM9oHQxciFMB/O2Obem9vbl8MJ1is6qqHNIhEIpHoH5T6n4gZ10gjhHyb27Zto+3btztsx3YKCQ2h8IhwioiI4GuHjxx2maCTnp5BMTGxvCoQkr8fP36cJxiZw7AwaQfrn6MclBcSEkKRkZHsv3ffXjp46KDbCTuQnvAEf6Ruwj1JSUlZJkaJ/lnlC+BcpQALkUQ9fhEA8/HYDRS415Fz67ICM4DotwpAPxqzgSORADWAjSOCeImjeW3GBVI7BYvotg7aF80RzrCj8dwNje7vL6duod5eO7ib3BTA
D+M+4YPy0V3fVQFU85HrVLm7nOMoTQEGO04M4m5oUxgj+cnvGxlAtUKOxDHoYRypKYyvRHc8orrmJCXAGoBOj1H12XSE6w5QzriYSYMWhFPjEf78bhDaZ9vBWO52R9ugXqjDkh0n+Dr8cH8b9X7txwfRsCW76KwVKdZCVHLsij3UYNha6jAhSJWxiWYHOaD0dwXVX3ps5jGpELY9FPA3GObPz/pm2jYFv+EMtfiDNMR3J32h4FpHo0UikUj0zyo5OZnWrV9Hdd+tS09WfZJTGGEpS6ydjq5wpCxC9/ebdd4ib585dPZsHO1Q0PjbyN94YtCrr71GZcuV4zGZWOrS19fXCZBINYR0SEjJhPyfKA+R0WeffZbKlCnD40AxwQi5M2NjY/keU5j13q5dO84NijqgK3+t/1pKSXENBv3rpID4ysWsM+dvFuWbMZwikUgkEon+Xl22VmJLSU2h0PAwGjpsGL319lsKQGvQTz8NpH379vJ15PWc4jGFxowdQ37LlnHi+LffeYcKFS5ML770IqdZMoETM9cxkx05QKtWrUoNGjZgmEV+TkBk+Ycr0MRJk9yufATgRPJ5vUpSxUcr0urVq/8VwHkl8xJdTkmizLgTdOl4BKXvVCC8eiJdmNOHzk/pSInjPrI8bz4JcIpEIpFIJPqfhO50LFsJwKtb9/9o1aqVOXZhYyWgRo0a0a233srRUeTUNIET66ojXycipY0bNyYvLy+eOITZ6si5ieuIsoaHh/PqRVjRCPk5PaZ6cCqlZs2acSJ41Aez43EuNTXr3IK/XVeu0JX0NLp8IZ4yow9Rxr7NlLZ5HiX7DqKkWV/T+Ynt6NyIBhT//fMU1/MxZZUorndViv+hJiUMqkXnfn3HKujmkwCnSCQSiUSivyyA5bJly+ixSo9xF/nHrT7m8ZvupCE0KiqKoRCr/7zy6qu0UAFnappjmJTuUkfyd6xgNHToUD7vTpiM5OHhwd35iH4iKorlLLFFl3rL
j1qouvnRuXPnXMaI/mUBKJEUPvUCXU44Q5eO7aL0Hcspdc0EujC/P52f9gUljm1BCT+9QXF9qimgrExxvZ6i+D7PUHz/Fyhh8JuUOKY5Jc3uRSkrxygY9aaMyA106eQ+upx4lq5k3LxDxQQ4RSKRSCQS/WUh0qhXFFqzdg1P1sEkHXfSwIlJRzt37uRxoMHbgyk6OponCkHwQRd84MZAXmkIXeTZCRCJ2e2YdITyEMnE+utYXx0rGUWfiebrf0qAyox0uoIo5emDDISpQd6UsnwkXfD5js5P7kQJw+oxQMZ/96QCyico7ruqFNfvWYr/8RU6N/Q95dOZkhf+TGnrp1J6yDLKOLSDLsUeV6Cq2uVy/pzwKsApEolEIpEo/0mB5eXkRLp4Yi+lhayiVP9JlLz4Z0qa1Y3OjWlJCYPrUMKAmhT//XMKKKtRXG8Fl72rUHzfanRuVBP2S142gtI2eVPG7kC6pMq5nBBNVy6mK6j8G6KpN5kEOEUikUgkEolE11QCnCKRSCQSiUSiayoBTpFIJBKJRCLRNZUAp0gkEolEIpHomkqAUyQSiUQikUh0TSXAKRKJRCKRSCS6phLgFIlEIpFIJBJdUwlwikQikUgkEomuqQQ4RSKRSCQSiUTXUET/D6L/emjhgWngAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>127a8a5e-f8c6-4fa5-90a2-b672def52299</rd:ReportID>
</Report>
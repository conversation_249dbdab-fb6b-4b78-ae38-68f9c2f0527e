codeunit 60003 "Quality Control Management FLX"
{
    procedure ProcessLabelText(var QualityControlHeader: Record "Quality Control Header FLX")
    var
        Location: Record Location;
        PackageNoInformation: Record "Package No. Information";
        QualityControlLine: Record "Quality Control Line FLX";
        AlreadyReadErr: Label 'This package is already read in Quality Control Document No. %1.', Comment = '%1=QualityControlHeader."No."';
    begin
        QualityControlHeader.TestField(Posted, false);

        if QualityControlHeader."Label Text" = '' then
            exit;

        PackageNoInformation.SetRange("Package No.", QualityControlHeader."Label Text");
        PackageNoInformation.FindFirst();

        PackageNoInformation.CalcFields("Location Code FLX");
        Location.Get(PackageNoInformation."Location Code FLX");
        Location.TestField("Quality Control Location FLX", true);

        PackageNoInformation.TestField("Quality Control Status FLX", PackageNoInformation."Quality Control Status FLX"::" ");
        PackageNoInformation.TestField("Package Type FLX", PackageNoInformation."Package Type FLX"::Coil);

        QualityControlLine.SetRange("Package No.", QualityControlHeader."Label Text");
        if QualityControlLine.FindFirst() then
            Error(AlreadyReadErr, QualityControlLine."Document No.");

        PackageNoInformation.CalcFields(Inventory);
        if PackageNoInformation.Inventory = 0 then
            Error(InventoryZeroErr);

        QualityControlLine.Init();
        QualityControlLine."Document No." := QualityControlHeader."No.";
        QualityControlLine."Package No." := PackageNoInformation."Package No.";
        QualityControlLine."Item No." := PackageNoInformation."Item No.";
        QualityControlLine."Item Description" := PackageNoInformation.Description;
        QualityControlLine.Quantity := PackageNoInformation.Inventory;
        QualityControlLine."Quality Control Status" := QualityControlLine."Quality Control Status"::Accept;
        QualityControlLine.Insert(true);

        QualityControlHeader."Label Text" := '';
    end;

    procedure PostQualityControlDocument(var QualityControlHeader: Record "Quality Control Header FLX")
    var
        PackageNoInformation: Record "Package No. Information";
        QualityControlLine: Record "Quality Control Line FLX";
        SuccesMsg: Label 'Quality Control Document posted succesfully.';
    begin
        QualityControlHeader.TestField(Posted, false);
        QualityControlHeader.Date := WorkDate();

        QualityControlLine.SetRange("Document No.", QualityControlHeader."No.");
        QualityControlLine.FindSet(false);
        repeat
            if QualityControlLine."Quality Control Status" = QualityControlLine."Quality Control Status"::Reject then
                QualityControlLine.TestField("Reject Reason Code");

            PackageNoInformation.Get(QualityControlLine."Item No.", '', QualityControlLine."Package No.");
            PackageNoInformation.Validate("Quality Control Status FLX", QualityControlLine."Quality Control Status");
            PackageNoInformation."Reject Reason Code FLX" := QualityControlLine."Reject Reason Code";
            PackageNoInformation."Quality Control Date FLX" := QualityControlHeader.Date;
            PackageNoInformation."Quality Controller ID FLX" := CopyStr(UserId(), 1, MaxStrLen(PackageNoInformation."Quality Controller ID FLX"));
            PackageNoInformation.Modify(true);
        until QualityControlLine.Next() = 0;

        QualityControlHeader.Posted := true;
        Message(SuccesMsg);
    end;

    procedure IsPackageQualityControlAccepted(CoilPackageNo: Code[50]): Boolean
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        PackageNoInformation.SetRange("Package No.", CoilPackageNo);
        PackageNoInformation.FindFirst();
        if PackageNoInformation."Quality Control Status FLX" = PackageNoInformation."Quality Control Status FLX"::Accept then
            exit(true);
        exit(false);
    end;

    procedure SetQualityControlStatusAcceptForSelectedLines(var PackageNoInformation: Record "Package No. Information")
    var
        QualityControlHeader: Record "Quality Control Header FLX";
    //SuccesfullMsg: Label '%1 Package''s Quality Control Status has been set to Accept.', Comment = '%1="Package No. Information".Count';
    begin
        QualityControlHeader.Init();
        QualityControlHeader.Insert(true);

        PackageNoInformation.FindSet(true);
        repeat
            // PackageNoInformation.TestField("Quality Control Status FLX", PackageNoInformation."Quality Control Status FLX"::" ");
            // PackageNoInformation.TestField("Package Type FLX", PackageNoInformation."Package Type FLX"::Coil);
            // PackageNoInformation.CalcFields(Inventory);
            // if PackageNoInformation.Inventory = 0 then
            //     Error(InventoryZeroErr);

            // PackageNoInformation."Quality Control Status FLX" := PackageNoInformation."Quality Control Status FLX"::Accept;
            // PackageNoInformation."Quality Control Date FLX" := WorkDate();
            // PackageNoInformation.Modify(true);
            QualityControlHeader.Validate("Label Text", PackageNoInformation."Package No.");
        until PackageNoInformation.Next() = 0;

        PageManagement.PageRun(QualityControlHeader);
        //Message(SuccesfullMsg, PackageNoInformation.Count);
    end;

    var
        PageManagement: Codeunit "Page Management";
        InventoryZeroErr: Label 'You can not read Package with 0 inventory.';
}
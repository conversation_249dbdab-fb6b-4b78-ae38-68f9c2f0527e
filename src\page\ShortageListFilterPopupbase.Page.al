page 60028 "Shortage List Filter Popupbase"
{
    ApplicationArea = All;
    Caption = 'Shortage List Filter Popupbase';
    PageType = StandardDialog;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            field(StartDate; StartDate)
            {
                ToolTip = 'Specifies the value of the Start Date field.';
                Caption = 'Start Date';
            }
            field(EndDate; EndDate)
            {
                ToolTip = 'Specifies the value of the End Date field.';
                Caption = 'End Date';
            }
            field(SourceItemNo; SourceItemNo)
            {
                ToolTip = 'Specifies the value of the Source Item No field.';
                Caption = 'Source Item No';
                trigger OnLookup(var Text: Text): Boolean
                var
                //ProdBomHD: Record "Production BOM Header";
                begin
                    //BRST.006.07
                    /*if Page.RunModal(Page::"Production BOM List", ProdBomHD) = Action::LookupOK then begin
                        if STRPOS(SourceItemNo, ProdBomHD."No.") = 0 then begin
                            if SourceItemNo <> '' then
                                SourceItemNo += '|';
                            SourceItemNo += ProdBomHD."No.";
                        end;
                    end;
                    */
                end;
            }
            field(ItemNo; ItemNo)
            {
                ToolTip = 'Specifies the value of the Item No field.';
                Caption = 'Item No';
                trigger OnLookup(var Text: Text): Boolean
                var
                //Item: Record Item;
                begin
                    //BRST.006.07
                    /*if Page.RunModal(Page::"Item List", Item) = Action::LookupOK then begin
                        if STRPOS(ItemNo, Item."No.") = 0 then begin
                            if ItemNo <> '' then
                                ItemNo += '|';
                            ItemNo += Item."No.";
                        end;
                    end;
                    */
                end;
            }
            field(LoopSubComp; LoopSubComp)
            {
                ToolTip = 'Specifies the value of the Calculate Sub Companent field.';
                Caption = 'Calculate Sub Companent';
            }
            field(ExcludeSupplyQuantities; ExcludeSupplyQuantities)
            {
                ToolTip = 'Specifies the value of the Exclude Supply Quantities field.';
                Caption = 'Exclude Supply Quantities';
            }
            field(IncludePlannedProdOrders; IncludePlannedProdOrders)
            {
                ToolTip = 'Specifies the value of the Include Planned Prod Orders field.';
                Caption = 'Include Planned Prod Orders';
            }
            field(IncludeFirmPlannedProdOrders; IncludeFirmPlannedProdOrders)
            {
                ToolTip = 'Specifies the value of the Include Firm Planned Prod Orders field.';
                Caption = 'Include Firm Planned Prod Orders';
            }
            field(IncludeReleasedProdOrders; IncludeReleasedProdOrders)
            {
                ToolTip = 'Specifies the value of the Include Released Prod Orders field.';
                Caption = 'Include Released Prod Orders';
            }
            field(ShowOnlyNegativeQty; ShowOnlyNegativeQty)
            {
                ToolTip = 'Specifies the value of the Show Only Negative Qty field.';
                Caption = 'Show Only Negative Qty';
            }
        }
    }
    trigger OnOpenPage()
    begin
        //StartDate := WORKDATE;
        StartDate := 0D; //BRST.006.05 - Modified Line
        //EndDate := 12312199D;//CALCDATE('<CY>',TODAY);
        EndDate := Today() + 1000;//CALCDATE('<CY>',TODAY);
                                  //SourceItemNo := '511-00000401'
        IncludeReleasedProdOrders := true;
        LoopSubComp := false;
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction = Action::OK then begin
            if EndDate = 0D then
                Error(EndDateErr);
            ShortageListFunctions.ProcessFilters(StartDate, EndDate, SourceItemNo, ItemNo, ExcludeSupplyQuantities, IncludePlannedProdOrders, IncludeFirmPlannedProdOrders, IncludeReleasedProdOrders, ShowOnlyNegativeQty, LoopSubComp); //BRST.006.09 - Removed Line
        end;
    end;

    var
        ShortageListFunctions: Codeunit "Shortage List Functions FLX";
        ExcludeSupplyQuantities: Boolean;
        IncludeFirmPlannedProdOrders: Boolean;
        //==BRST.006.09==	Integer
        IncludePlannedProdOrders: Boolean;
        IncludeReleasedProdOrders: Boolean;
        LoopSubComp: Boolean;
        ShowOnlyNegativeQty: Boolean;
        EndDate: Date;
        StartDate: Date;
        EndDateErr: Label 'End Date Must Be Filled';
        //==BRST.006.04==	Integer
        ItemNo: Text[250];
        SourceItemNo: Text[250];

    procedure GetStartDate(): Date
    begin
        exit(StartDate);
    end;

    procedure GetEndDate(): Date
    begin
        exit(EndDate);
    end;

    procedure GetItemNo(): Text
    begin
        exit(ItemNo);
    end;

    procedure GetSourceItemNo(): Text
    begin
        exit(SourceItemNo);
    end;
}

page 60034 "Items by Item Attribute Matrix"
{
    ApplicationArea = All;
    Caption = 'Items by Item Attribute Matrix';
    PageType = ListPart;
    SourceTable = Item;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies a description of the item.';
                }
                field("Item Category Code"; Rec."Item Category Code")
                {
                    ToolTip = 'Specifies the category that the item belongs to. Item categories also contain any assigned item attributes.';
                }
                // field(Field1; MATRIX_CellData[1])
                // {
                //     trigger OnDrillDown()
                //     begin
                //         MatrixOnDrillDown(1);
                //     end;
                // }
                /*field(Field2; MATRIX_CellData[2])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(2);
                    end;
                }
                field(Field3; MATRIX_CellData[3])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(3);
                    end;
                }
                field(Field4; MATRIX_CellData[4])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(4);
                    end;
                }
                field(Field5; MATRIX_CellData[5])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(5);
                    end;
                }
                field(Field6; MATRIX_CellData[6])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(6);
                    end;
                }
                field(Field7; MATRIX_CellData[7])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(7);
                    end;
                }
                field(Field8; MATRIX_CellData[8])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(8);
                    end;
                }
                field(Field9; MATRIX_CellData[9])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(9);
                    end;
                }
                field(Field10; MATRIX_CellData[10])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(10);
                    end;
                }
                field(Field11; MATRIX_CellData[11])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(11);
                    end;
                }
                field(Field12; MATRIX_CellData[12])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(12);
                    end;
                }
                field(Field13; MATRIX_CellData[13])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(13);
                    end;
                }
                field(Field14; MATRIX_CellData[14])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(14);
                    end;
                }
                field(Field15; MATRIX_CellData[15])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(15);
                    end;
                }
                field(Field16; MATRIX_CellData[16])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(16);
                    end;
                }
                field(Field17; MATRIX_CellData[17])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(17);
                    end;
                }
                field(Field18; MATRIX_CellData[18])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(18);
                    end;
                }
                field(Field19; MATRIX_CellData[19])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(19);
                    end;
                }
                field(Field20; MATRIX_CellData[20])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(20);
                    end;
                }
                field(Field21; MATRIX_CellData[21])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(1);
                    end;
                }
                field(Field22; MATRIX_CellData[22])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(22);
                    end;
                }
                field(Field23; MATRIX_CellData[23])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(23);
                    end;
                }
                field(Field24; MATRIX_CellData[24])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(24);
                    end;
                }
                field(Field25; MATRIX_CellData[25])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(25);
                    end;
                }
                field(Field26; MATRIX_CellData[26])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(26);
                    end;
                }
                field(Field27; MATRIX_CellData[27])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(27);
                    end;
                }
                field(Field28; MATRIX_CellData[28])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(28);
                    end;
                }
                field(Field29; MATRIX_CellData[29])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(29);
                    end;
                }
                field(Field30; MATRIX_CellData[30])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(30);
                    end;
                }
                field(Field31; MATRIX_CellData[31])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(31);
                    end;
                }
                field(Field32; MATRIX_CellData[32])
                {
                    trigger OnDrillDown()
                    begin
                        MatrixOnDrillDown(32);
                    end;
                }
                */
            }
        }
    }
    // local procedure MATRIX_OnAfterGetRecord(ColumnID: Integer)
    // var
    //     ItemAttributeValue: Record "Item Attribute Value";
    //     ItemAttributeValueMapping: Record "Item Attribute Value Mapping";
    // begin
    //     // TempItem.COPY(Rec);
    //     // // TempItem.SETRANGE("Location Filter",MatrixRecords[ColumnID].Id);
    //     // TempItem.CALCFIELDS(Inventory);
    //     // MATRIX_CellData[ColumnID] := TempItem.Inventory;
    //     ItemAttributeValueMapping.Reset();
    //     ItemAttributeValueMapping.SetRange("No.", Rec."No.");
    //     ItemAttributeValueMapping.SetRange("Item Attribute ID", MatrixRecords[ColumnID].ID);
    //     if ItemAttributeValueMapping.FindFirst() then begin
    //         ItemAttributeValue.Get(ItemAttributeValueMapping."Item Attribute ID", ItemAttributeValueMapping."Item Attribute Value ID");
    //         MATRIX_CellData[ColumnID] := ItemAttributeValue.Value;
    //     end else
    //         MATRIX_CellData[ColumnID] := '';
    //     SetVisible();
    // end;

    procedure Load(MatrixColumns1: array[32] of Text[1024]; var MatrixRecords1: array[32] of Record "Item Attribute"; var MatrixRecord1: Record "Item Attribute"; CurrSetLength: Integer)
    begin
        /*MATRIX_CurrSetLength := CurrSetLength;
        COPYARRAY(MATRIX_ColumnCaption, MatrixColumns1, 1);
        COPYARRAY(MatrixRecords, MatrixRecords1, 1);
        TempMatrixItemAttribute.COPY(MatrixRecord1, true);
        */
    end;

    // local procedure MatrixOnDrillDown()
    // begin
    //     // ItemLedgerEntry.SETCURRENTKEY(
    //     //  "Item No.","Entry Type","Variant Code","Drop Shipment","Location Code","Posting Date");
    //     // ItemLedgerEntry.SETRANGE("Item No.","No.");
    //     // ItemLedgerEntry.SETRANGE("Location Code",MatrixRecords[ColumnID].Code);
    //     // PAGE.RUN(0,ItemLedgerEntry);
    // end;

    procedure SetVisible()
    begin
        /*Field1Visible := MATRIX_CurrSetLength > 0;
        Field2Visible := MATRIX_CurrSetLength > 1;
        Field3Visible := MATRIX_CurrSetLength > 2;
        Field4Visible := MATRIX_CurrSetLength > 3;
        Field5Visible := MATRIX_CurrSetLength > 4;
        Field6Visible := MATRIX_CurrSetLength > 5;
        Field7Visible := MATRIX_CurrSetLength > 6;
        Field8Visible := MATRIX_CurrSetLength > 7;
        Field9Visible := MATRIX_CurrSetLength > 8;
        Field10Visible := MATRIX_CurrSetLength > 9;
        Field11Visible := MATRIX_CurrSetLength > 10;
        Field12Visible := MATRIX_CurrSetLength > 11;
        Field13Visible := MATRIX_CurrSetLength > 12;
        Field14Visible := MATRIX_CurrSetLength > 13;
        Field15Visible := MATRIX_CurrSetLength > 14;
        Field16Visible := MATRIX_CurrSetLength > 15;
        Field17Visible := MATRIX_CurrSetLength > 16;
        Field18Visible := MATRIX_CurrSetLength > 17;
        Field19Visible := MATRIX_CurrSetLength > 18;
        Field20Visible := MATRIX_CurrSetLength > 19;
        Field21Visible := MATRIX_CurrSetLength > 20;
        Field22Visible := MATRIX_CurrSetLength > 21;
        Field23Visible := MATRIX_CurrSetLength > 22;
        Field24Visible := MATRIX_CurrSetLength > 23;
        Field25Visible := MATRIX_CurrSetLength > 24;
        Field26Visible := MATRIX_CurrSetLength > 25;
        Field27Visible := MATRIX_CurrSetLength > 26;
        Field28Visible := MATRIX_CurrSetLength > 27;
        Field29Visible := MATRIX_CurrSetLength > 28;
        Field30Visible := MATRIX_CurrSetLength > 29;
        Field31Visible := MATRIX_CurrSetLength > 30;
        Field32Visible := MATRIX_CurrSetLength > 31;
        */
    end;

    trigger OnInit()
    begin
        /*Field32Visible := TRUE;
        Field31Visible := TRUE;
        Field30Visible := TRUE;
        Field29Visible := TRUE;
        Field28Visible := TRUE;
        Field27Visible := TRUE;
        Field26Visible := TRUE;
        Field25Visible := TRUE;
        Field24Visible := TRUE;
        Field23Visible := TRUE;
        Field22Visible := TRUE;
        Field21Visible := TRUE;
        Field20Visible := TRUE;
        Field19Visible := TRUE;
        Field18Visible := TRUE;
        Field17Visible := TRUE;
        Field16Visible := TRUE;
        Field15Visible := TRUE;
        Field14Visible := TRUE;
        Field13Visible := TRUE;
        Field12Visible := TRUE;
        Field11Visible := TRUE;
        Field10Visible := TRUE;
        Field9Visible := TRUE;
        Field8Visible := TRUE;
        Field7Visible := TRUE;
        Field6Visible := TRUE;
        Field5Visible := TRUE;
        Field4Visible := TRUE;
        Field3Visible := TRUE;
        Field2Visible := TRUE;
        Field1Visible := TRUE;
        */
    end;

    trigger OnOpenPage()
    begin
        //MATRIX_NoOfMatrixColumns := ARRAYLEN(MATRIX_CellData);
    end;

    trigger OnAfterGetRecord()
    begin
        /*MATRIX_CurrentColumnOrdinal := 0;
        IF TempMatrixItemAttribute.FINDSET THEN
            REPEAT
                MATRIX_CurrentColumnOrdinal := MATRIX_CurrentColumnOrdinal + 1;
                MATRIX_OnAfterGetRecord(MATRIX_CurrentColumnOrdinal);
            UNTIL (TempMatrixItemAttribute.NEXT = 0) OR (MATRIX_CurrentColumnOrdinal = MATRIX_NoOfMatrixColumns);

        Item.GET(Rec."No.");
        ItemCategoryCode := Item."Item Category Code";
        */
    end;

    var
    // MatrixRecords: array[32] of Record "Item Attribute";
    // MATRIX_CellData: array[32] of Text;
    /*[InDataSet]
    Field1Visible: Boolean;
    Field2Visible: Boolean;
    Field3Visible: Boolean;
    Field4Visible: Boolean;
    Field5Visible: Boolean;
    Field6Visible: Boolean;
    Field7Visible: Boolean;
    Field8Visible: Boolean;
    Field9Visible: Boolean;
    Field10Visible: Boolean;
    Field11Visible: Boolean;
    Field12Visible: Boolean;
    Field13Visible: Boolean;
    Field14Visible: Boolean;
    Field15Visible: Boolean;
    Field16Visible: Boolean;
    Field17Visible: Boolean;
    Field18Visible: Boolean;
    Field19Visible: Boolean;
    Field20Visible: Boolean;
    Field21Visible: Boolean;
    Field22Visible: Boolean;
    Field23Visible: Boolean;
    Field24Visible: Boolean;
    Field25Visible: Boolean;
    Field26Visible: Boolean;
    Field27Visible: Boolean;
    Field28Visible: Boolean;
    Field29Visible: Boolean;
    Field30Visible: Boolean;
    Field31Visible: Boolean;
    Field32Visible: Boolean;
    */
}
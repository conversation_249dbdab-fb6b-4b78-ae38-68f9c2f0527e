page 60006 "Package Split List FLX"
{
    ApplicationArea = All;
    Caption = 'Package Split List';
    PageType = List;
    SourceTable = "Package Split Header FLX";
    UsageCategory = Lists;
    CardPageId = "Package Split FLX";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
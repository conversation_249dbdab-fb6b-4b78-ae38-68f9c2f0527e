﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-10-31T07:44:50.6555593Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>bc508610-c9c1-4ad1-a1b1-a57eaa153929</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
      <Fields>
        <Field Name="IDmmFLX_PackageNoInformation">
          <DataField>IDmmFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="IDmmFLX_PackageNoInformationFormat">
          <DataField>IDmmFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ODmmFLX_PackageNoInformation">
          <DataField>ODmmFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="ODmmFLX_PackageNoInformationFormat">
          <DataField>ODmmFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ProductionOrderNoFLX">
          <DataField>ProductionOrderNoFLX</DataField>
        </Field>
        <Field Name="SalesOrderNoFLX_SubPalet">
          <DataField>SalesOrderNoFLX_SubPalet</DataField>
        </Field>
        <Field Name="BPbarFLX_PackageNoInformation">
          <DataField>BPbarFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="BPbarFLX_PackageNoInformationFormat">
          <DataField>BPbarFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="WPbarFLX_PackageNoInformation">
          <DataField>WPbarFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="WPbarFLX_PackageNoInformationFormat">
          <DataField>WPbarFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SelltoCustomerNameFLX">
          <DataField>SelltoCustomerNameFLX</DataField>
        </Field>
        <Field Name="SelltoCustomerRegisterName">
          <DataField>SelltoCustomerRegisterName</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="Description2">
          <DataField>Description2</DataField>
        </Field>
        <Field Name="HoseLenghtFLX">
          <DataField>HoseLenghtFLX</DataField>
        </Field>
        <Field Name="HoseLenghtFLXFormat">
          <DataField>HoseLenghtFLXFormat</DataField>
        </Field>
        <Field Name="HoseLenghtFLX_PackageNoInformation">
          <DataField>HoseLenghtFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="HoseLenghtFLX_PackageNoInformationFormat">
          <DataField>HoseLenghtFLX_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="Weight_PackageNoInformation">
          <DataField>Weight_PackageNoInformation</DataField>
        </Field>
        <Field Name="Weight_PackageNoInformationFormat">
          <DataField>Weight_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="LabelLenghtFLX">
          <DataField>LabelLenghtFLX</DataField>
        </Field>
        <Field Name="LabelLenghtFLXFormat">
          <DataField>LabelLenghtFLXFormat</DataField>
        </Field>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="YourReferenceFLX_PackageNoInformation">
          <DataField>YourReferenceFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="YourReferenceFLX_PalettePackageNoInformation">
          <DataField>YourReferenceFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ItemNo_PackageNoInformation">
          <DataField>ItemNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="ItemSearchDesc_PackageNoInformation">
          <DataField>ItemSearchDesc_PackageNoInformation</DataField>
        </Field>
        <Field Name="BarcodeItemSearchDesc_PackageNoInformation">
          <DataField>BarcodeItemSearchDesc_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformation">
          <DataField>Inventory_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformationFormat">
          <DataField>Inventory_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="SalesOrderLineNoFLX_PackageNoInformation">
          <DataField>SalesOrderLineNoFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="QrCode">
          <DataField>QrCode</DataField>
        </Field>
        <Field Name="ProductionOrderLineNoFLX_PackageNoInformation">
          <DataField>ProductionOrderLineNoFLX_PackageNoInformation</DataField>
        </Field>
        <Field Name="Ship_to_Code_FLX">
          <DataField>Ship_to_Code_FLX</DataField>
        </Field>
        <Field Name="BarcodePackageNo_PackageNoInformation">
          <DataField>BarcodePackageNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="BarcodePO_PackageNoInformation">
          <DataField>BarcodePO_PackageNoInformation</DataField>
        </Field>
        <Field Name="BarcodeDeliveryNo_PackageNoInformation">
          <DataField>BarcodeDeliveryNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="DeliveryNo_PackageNoInformation">
          <DataField>DeliveryNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="BarcodeItemCrossRef_PackageNoInformation">
          <DataField>BarcodeItemCrossRef_PackageNoInformation</DataField>
        </Field>
        <Field Name="BarcodeQty_PackageNoInformation">
          <DataField>BarcodeQty_PackageNoInformation</DataField>
        </Field>
        <Field Name="BarcodeQtyLabel_PackageNoInformation">
          <DataField>BarcodeQtyLabel_PackageNoInformation</DataField>
        </Field>
        <Field Name="LotNo_PackageNoInformation">
          <DataField>LotNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="PackageNo_PalettePackageNoInformation">
          <DataField>PackageNo_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="ProductionOrderNoFLX_PalettePackageNoInformation">
          <DataField>ProductionOrderNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="SalesOrderNoFLX_PalettePackageNoInformation">
          <DataField>SalesOrderNoFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="SelltoCustomerNameFLX_PalettePackageNoInformation">
          <DataField>SelltoCustomerNameFLX_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Today_PalettePackageNoInformation">
          <DataField>Today_PalettePackageNoInformation</DataField>
        </Field>
        <Field Name="Registration_Name_INF">
          <DataField>Registration_Name_INF</DataField>
        </Field>
        <Field Name="NotesFLX_Item">
          <DataField>NotesFLX_Item</DataField>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="Rectangle4">
            <ReportItems>
              <Tablix Name="Tablix2">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>6.44286cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.70962cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ProductionOrderNoFLX2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ProductionOrderNoFLX.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ProductionOrderNoFLX</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.47653cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ProductionOrderLineNoFLX_PackageNoInformation.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.55087cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="SelltoCustomerNameFLX2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=iif(Fields!Registration_Name_INF.Value="",Fields!SelltoCustomerNameFLX.Value,Fields!Registration_Name_INF.Value)</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>SelltoCustomerNameFLX</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.56851cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Description2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Description.Value</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Description</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details2">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PackageNo.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                        <TablixMember />
                        <TablixMember />
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>DataSet_Result</DataSetName>
                <Height>2.30553cm</Height>
                <Width>6.44286cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Tablix Name="Tablix3">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>2.81454cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.15216cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>0.30627cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.3597cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>0.81019cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.55087cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox25">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>İç Çap X Dış Çap</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox17</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox24">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!IDmmFLX_PackageNoInformation.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Right</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox18</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox117">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>X</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox117</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox118">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ODmmFLX_PackageNoInformation.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox118</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox119">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>mm</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details1">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PackageNo.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <Top>2.41813cm</Top>
                <Height>0.55087cm</Height>
                <Width>6.44286cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Tablix Name="Tablix4">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>1.94559cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.60306cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.66728cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox23">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>İstenen Boy</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox21</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox43">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!HoseLenghtFLX.Value</Value>
                                      <Style>
                                        <FontSize>11pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox41</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.58741cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox66">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Üretim mt</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox60</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox67">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!LabelLenghtFLX.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="NotesFLX_Item">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=LAST(Fields!NotesFLX_Item.Value)</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Left</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>NotesFLX_Item</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Bottom</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <ColSpan>2</ColSpan>
                          </CellContents>
                        </TablixCell>
                        <TablixCell />
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details3">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PackageNo.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                        <TablixMember />
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>DataSet_Result</DataSetName>
                <Top>3.0078cm</Top>
                <Left>0.01588cm</Left>
                <Height>1.85469cm</Height>
                <Width>3.54865cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Tablix Name="Tablix5">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>2.76545cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>1.77475cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="QrCode3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!QrCode.Value</Value>
                                      <Style>
                                        <FontFamily>IDAutomation2D</FontFamily>
                                        <FontSize>9pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>QrCode</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details4">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PackageNo.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>DataSet_Result</DataSetName>
                <Top>3.03956cm</Top>
                <Left>3.67741cm</Left>
                <Height>1.77475cm</Height>
                <Width>2.76545cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Tablix Name="Tablix6">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>6.39137cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.60378cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Inventory_PackageNoInformation2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!PackageNo.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Inventory_PackageNoInformation</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details5">
                        <GroupExpressions>
                          <GroupExpression>=Fields!PackageNo.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>DataSet_Result</DataSetName>
                <Top>4.96099cm</Top>
                <Left>0.05149cm</Left>
                <Height>0.60378cm</Height>
                <Width>6.39137cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>0.0635cm</Top>
            <Left>0.09525cm</Left>
            <Height>5.56477cm</Height>
            <Width>6.44639cm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>5.62827cm</Height>
        <Style />
      </Body>
      <Width>185.93231pt</Width>
      <Page>
        <PageHeight>6.75cm</PageHeight>
        <PageWidth>6.8cm</PageWidth>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>0.1cm</LeftMargin>
        <RightMargin>0.1cm</RightMargin>
        <TopMargin>0.1cm</TopMargin>
        <BottomMargin>0.1cm</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public RowColor As Boolean = 0

Public Function SetRowColor(ByVal val As Boolean) As Boolean
    RowColor = val
    Return val
End Function

Public Function GetRowColor() As Boolean
    Return RowColor
End Function
</Code>
  <EmbeddedImages>
    <EmbeddedImage Name="RDMLogo">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAkGBxQSEhQUERQVEBUWFxQYGBUUFxcUFhcZHBQXFhgUGBcYHCggGBolHBcUITEhJisrLi4uGB8zODMsQygtLiv/2wBDAQoKCg4NDhsQEBosJh8kLCwtMCwuLCw0LCw3LC0sLSw3MDQsNCwvOC03LDcsLTcvLC4sLCwsNy0sNCwsLCwsLCz/wAARCAB8ASIDASIAAhEBAxEB/8QAHAABAAICAwEAAAAAAAAAAAAAAAYHBQgCAwQB/8QARxAAAQMCAQcFCg0EAQUAAAAAAQACAwQRBQYHEiExQVETYXGBkSI1U2Jzg6Gxs9EUFyMyNEJScpKTssHCJDOCovAVJUN00v/EABoBAQEBAQEBAQAAAAAAAAAAAAABBAUCAwb/xAAoEQEAAQMDAwMEAwAAAAAAAAAAAQIDEQQhURIxQQUikRNhgaFxsdH/2gAMAwEAAhEDEQA/ALxREQEREBERAREQEREBERAReLFMUipmGSd7Y2je47TwA2k8wUIq8tZai/wZpgh3SO1yv52jYxvObnoXum3VVGfD43b9FqPdKdV1fHCLyPDebeegbSo1XZZboWf5PP8AEe9RiCF8rwG6Uj3cSSTzklTXBMmGRWdLaR+231W+886YiO7HTfvX5xb2jl4KJtbVazIYWHeBo3+6BrPapBRYSyPWdKR325CXu6r7F77L6vMy127EU7zOZ5kCIij7iIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiLw4xi0VLGZJ3iNo47SeDRtJ5lYiZnEJMxEZl7VBMsM48VNeOnAqJthN/k2dJHzjzDtUIyxzhzVd44bwQ6xYHu3jxiNg8UdqjuA4Xy79epjbaXP4oXRtaOKY67vw51/W+LfyzNIyatk+E1jzJ9lp2dAGxrfWpTh1C+Z4ZGLnedzRf5x5l00NK6WRsMIGkRfxWN2abrbBwG86lZODYWynj0Gaztc8
7XHieHMNyz3rvVv8QzafT1X6uqrt/Zg+Esp2WbrcfnOO0+4cyyCIsrs00xTGIEREehERAREQEREBERAREQEREBERAREQEREBERAREQEREBEXy6D6l1hMocqaaiHy8g0rXEbe6kPQ3d0lVHlVnDqKu7I/6aI3Gi03e4eM79h6Vos6Wu727cs93U0W+/fhP8r84kNJeOC1RNrFge4YfGI2nxR6FTuM4xNVScpUPMjt3Bo+y0bAF4Ai7FjTUWo278uVe1Fd2d+3Duo6Z0rwxu0+jiVNqdhjMdLTN5SZ+oDcOMj+A3rAU8wpI9mlPIBq+w3dfn32VtZusljSxGabuqiaxcTtY3aGdO89m5ZtVc2zPbxHM/4mnsTdqx4ZrJnAm0kWiDpvd3Ukh2vdx5gNgG4LMIi5MzMzmXdppimMQIiKKIiICIiAoFnBy/NC8QQMEkxaHOc75rAb2Fh85xtfmFlPVr7nSP8A3Oo837JqDuOc7EL35RnRybbKa5u8vZ62f4POyO/JufyjLtPclosW6wb6W3Vs3qmVOszXfE+Ql/XGqLzREUBERAREQEREBERAREQEREGMZlBTl5jMrI5BtZIeTcOp1r9SyLXX1jWOI1rEZR5NwVzNCdusX0XtsHs6D+2xUrlPkhUYc7SuXxE6po7tHQ8A9yeu3OtVmzbu7dWJ4Zrt6u3v05hsEvLWYnDD/dlji++4N9BK1nfVyEWMjyOBc4j1roDRwWqPTeav0zT6hxT+17YtnMoobhjnVDuEY1fidYKA47nOq57titStP2DpP/GQPQAoQi029Hao8Z/lmuau5X5x/Dk95cSXEuJNySbkniSdpXFEWtlFyil0SCLEjZfjxXAld2HUbppWRMF3SOa0dJNuxSZ2eohO81GTnwic1Uw0mRO7nS16cu2/Po6j0kK6F4MBwplLBHCzYxtr8Ttc7rNysdlDlnSURLZZLv8ABs7p/WB83rsvz+ou/Vrz48O7YtfTowkKKqKzPJr+
RpCRuMkuietrWEeleZueOXfSRnolcPToFfB9lwIq6wfO3TyENqIn0xP1gRKwdJADh2KfUlUyVgfE5sjHaw5puD1oO9EUayiy4pKMlsj9OQf+OMaTug7m9ZQSVFU1VnjN/kqQW4vl1n/FrLDtK4QZ5H37ukaRxbMQR1Fhv2hBbi18zo986jzfs2q2smsvKSsIY1xikOyOWzSTwab2d1FVLnR751Hm/ZtQRVTrM13xPkJf1xqCqdZmu+J8hL+uNUXmiLBZQZW0tFqnlAfb+23un83cjZ1qDOoqrrc8bQfkaVzhxkkDD+FrXeteNueOW+ukjI4CVw9OgUFwIq4wrO7TvIFRDJT33tIlaOkgB3+qnmGYlFUMEkEjZWne03tzHgeYoPWi655msaXPIa0C5c42AHEkqEYznTo4SWxadU4fYsGfjd+wKCdoqhlzxyX7mkYBzzFx9DAu2mzyG/ylILbyybX1NLNfagtlFFMn84FHVkNa8wyHYyazSeYG5aeoqVoCIiAuE0YcC1wDmkWIIuCOBB2rmiCqMtM2Vry0A4l0B9cZ/iergquc0gkEEEaiDqIO8EbitpyFDsucho60GSO0VQBqdbU/xX/s7culptdMe2525c/UaOJ91HwohF6K+ikhkdHK0se02LT/AM1heddWJzvDlzGNpF8JX1cChAArKzM4NpzSVLhqiGgz77hrPU39SrdbC5B4eKXD4gdRLTK887hpG/QLDqWPXXOi1jnZs0dHXczwwGdDLV1KBTUxtM9t3vG2Np2AeMfQOkKlibkkm5Osk6yTxJ3levGMRdUzyTu2yOLugHYOoWHUvIuK7Ai7qOlfK9scTTI9xsGtFyf+cVNaXNTWubdxhjPBziT/AKgoIIrGzK4o9tTJT3Jjex0ltwc0tFxzkG3UsJiubuvgGlyXLNG+I6Z/Dt7F7sFppMNoZqx4MU045CBrhZ7QdbpbEajYGw5kGdzkZwHNc+lo3aNtUkzTrvvYzhbe7qCq
lEQEXrwrC5ql/JwRulfwbuHEnYB0qVDNdX2voxX4coL+qyCErvrKt8rtOVxe6zQXONyQAALnfqAXoxfB5qV+hURuidtF9jhxaRqK8KAp1ma74nyEv641BVOMzjrYgTwp5f1RoJrnNy2NIBT05+Xe27neCadhHjHXbht4Kk5HlxLnEucSSSTcknaSd5Xtx7ETU1E0zjfTe4jo2NH4QF4UBF2UtO+R7Y42l73GzWtFyTzBS2PNliBbpcmxvimQaXZxQQ5ZHAcbmo5RLA4tOq7fqvH2XDeF04phktNIY543RPGuzt44g7COcLyIJLllllNiDgHfJQi1ogbi/wBpx+sb7OCjSLnBC57mtY0vc42a1ouSeAA2oOCKYU+bPEHt0uTYzxXyAO7NyjmL4RNSycnURuida4vsI4tI1EIPCQrNzYZcuY9lJUu0mO7mKRx1sO5jidrTsHDV1VmgPDVz/ug2tRQLCs4UXIRcoe75OPS1/W0Rpem6KCeoiICIiCHZw8kRWxacYtURg6B+2NvJn9uB6VQ5HUtqFROdbCBT1pc0WbM3lNWzSvZ47df+S6mgvzn6c/hzddZjHXH5Qxy+AK5mZtopMPijNoqkN0+Utr0nayx3Fuwc1lUmJUElPK+KZug9hsR+4O8Hittm/RdmYjwx3bFVuIz5MLpeVmii8JJGz8Tw391sdjrdGknA1AQSgW3WjNlRWbyLSxKlB+2T+FjnD0gK98oPotR5Gb2blz/UavdTH2bvT6fbM/drCF9XwL6ue6C38yOGNEM1QR3bpOTaeDWta426S7/VWcoLma73+el/ip0oCprPfWk1MEO5kRf1veW+gR+lXKqKzx98fMxetyCDrsp4S97WN1ue5rWjiXEADtK61mcirf8AUKTS2cvF26Q0f9rKi/Mlcn46GnbFGBfUXv3vfbW4/sNwWYRFBjcfwWKshfDMLhwNjvY7c9p3ELW3FKF0E0kL/nRuLTz2O3r2raRa9Zzbf9SqLcWX
6eTbf0oIupjmrP8AVy/+rUetihymWapn9VMeFLP6dD3KiFs2DoC5LizYOgLkgtnMhhjdGeoIu7SETTwFg51um7exWoq7zI/Q5vLu9nGrEUEPzpYO2eglfYacDTK128But46C2/YFQS2YyqH9FVeQm9m5azBB9VtZk8GboS1TgC/SMbCfqgAFxHOdIDqVSq8szXe/z0n8VRO1Ds6+Gtlw6VxHdQlsjTw7oB3+pPYFMVgMvu91X5GT1KDXFERUEREG1qIigIiICiOWeCCpqcPJFwyZ+l93QEljzExtHWpcuqWEOc0/ZJI62kfuvdFc0TmPu8V0xVGJdgUUy/yRbXxXZZs8YOg77Q28m48DuO49algRSiuaKoqp7rXRFcdMtfs3bSzFKdrwWuD5GkHUQeTeCD1q8cofotR5Gb2blDMucDENXTYjGLBs0QnAH1S4N5XsJB6lMsoPotR5Gb2blp1dyLnTXHDPpbc2+qieWsQX1fAvqytS88zXe/z0v8VOlBczXe/z0v8AFTpQFTufCgIngn3PjMZ5ixxcOsh5/CriUey7wH4bSSRttyg7uM+O3YOa+sdaDXNc4JixzXtNnMc1zTwLSHA9oC4yMLSWuBaQSCDqIINiDz3XxUbI5I5Rx10DZGEBwsJGb2OtrFuB3FZxatUFdJA8SQvdE8bHMNj0HiOY6lJG5x8QDdHlgfG0G6XbZQXflBjcVHC6aZ1gBqH1nnc1o3krW7Fa91RNJM/50ji4jhfd1CwTE8TmqH6c8jpnbLuN7DgBsA5gvKqCsfNBRXbWzbhFoDpIc4+gBVw1pJAGskgADeTqAWwOSeA/AsNMbv7jmSSSffc3Z1ANb1INfGbB0BclxZsHQFyQXTmR+hy+Xd7ONWIq7zI/Q5fLu9nGrEUGLyp+hVXkJvZuWsoWzWVP0Kq8hN7Ny1lCD6ryzNd7/PSfxVGq8szXe/z0n8UE7WAy973VfkZPUs+sBl73uq/IyepBriiIqCIiDa1E
RQEREBERAREQdFZStlY+N40mvaWuHEEWKxuIX+Azhxu5sErSeJEbhfr1HrWZXRUUjXte1w1SNLXWNrgt0T0GyudsJjfLVgL6r5+K7DvByfmye9Piuw7wcn5snvUV05mu9/npf4qdLG4DgkNHFyVOC1mkXWLi43O3Wde5ZJAREQV5nCzffCiails2a3dMOpstt9/qv59hVNVdM+J7o5Wuje3a1wsR1futqFjsYwOnqm6NREyXgSO6HQ7aEGsaKystsg6amaXwulb4pc1zR2t0vSq1VBc4IXPc1jGl73GzWtBLieAA2qxcishKaqbpzOlOzuQ5rWnsbf0q0cFyepqQWp4mx8XWu49LjrKCFZu83pp3NqasAyjXHFtEfjOO9/q9VgYl/Zk+4/8ASV6VwljDmlp2EEHoIsVBqmzYOgLkr5Ga3DvByfmyf/SfFdh3g5PzZPegx+ZH6HL5d3s41YixWT2AQUUbo6dpa1zi4hznP12A2uPABZVBi8qfoVV5Cb2blrKFtRWUzZY3xv1te1zXAG2pwIOsbNRUR+K7DvByfmye9BQyvLM13v8APSfxXf8AFdh3g5PzZPepFgOCQ0cXJU4LWaRdYuLjc7dZ17kGSWAy973VfkZPUs+vNidCyeJ8MoJZI0tcASCQdusawg1bRXz8V2HeDk/Nk96fFdh3g5PzZPegoZFfPxXYd4OT82T3og//2Q==</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>127a8a5e-f8c6-4fa5-90a2-b672def52299</rd:ReportID>
</Report>
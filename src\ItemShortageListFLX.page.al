// page 60021 "Item Shortage List FLX"
// {
//     ApplicationArea = All;
//     Caption = 'Item Shortage List';
//     Editable = false;
//     PageType = ListPart;
//     SourceTable = "Shortage Item List FLX";

//     layout
//     {
//         area(Content)
//         {
//             repeater(General)
//             {
//                 field("Item No."; Rec."Item No.")
//                 {
//                 }
//                 field("Item Description"; Rec."Item Description")
//                 {
//                 }
//                 field("Item Description 2"; Rec."Item Description 2")
//                 {
//                 }
//                 field("User Id"; Rec."User Id")
//                 {
//                 }
//                 field("Item Category Code"; Rec."Item Category Code")
//                 {
//                 }
//                 field("Lead Time Calculation"; Rec."Lead Time Calculation")
//                 {
//                 }
//                 field("Minimum Order Qty."; Rec."Minimum Order Qty.")
//                 {
//                 }
//                 field(Inventory; Rec.Inventory)
//                 {
//                 }
//                 field("Quantity On UHD Location"; Rec."Quantity On UHD Location")
//                 {
//                 }
//                 field("Quantity On WHSE Location"; Rec."Quantity On WHSE Location")
//                 {
//                 }
//                 field("Qty. on Purch. Order"; Rec."Qty. on Purch. Order")
//                 {
//                     trigger OnAssistEdit()
//                     var
//                         PurchLine: Record "Purchase Line";
//                     begin
//                         PurchLine.Reset();
//                         PurchLine.SetRange(PurchLine."Document Type", PurchLine."Document Type"::Order);
//                         PurchLine.SetRange(PurchLine."No.", Rec."Item No.");
//                         PurchLine.SetFilter(PurchLine."Outstanding Quantity", '<>0');
//                         if PurchLine.FindFirst() then
//                             Page.RunModal(Page::"Purchase Lines", PurchLine);
//                     end;
//                 }
//                 field("Qty. on Sales Order"; Rec."Qty. on Sales Order")
//                 {
//                 }
//                 field("Qty. on Component Lines"; Rec."Qty. on Component Lines")
//                 {
//                 }
//                 field(RealConsump1; RealConsump_CellData[1])
//                 {
//                     ToolTip = 'Specifies the value of the RealConsump1 field.';
//                     CaptionClass = '3,' + RealConsump_CaptionSet[1];
//                     trigger OnDrillDown()
//                     begin
//                         RealConsumpOnDrillDown(1);
//                     end;
//                 }
//                 field(RealConsump2; RealConsump_CellData[2])
//                 {
//                     ToolTip = 'Specifies the value of the RealConsump2 field.';
//                     CaptionClass = '3,' + RealConsump_CaptionSet[2];
//                     trigger OnDrillDown()
//                     begin
//                         RealConsumpOnDrillDown(2);
//                     end;
//                 }
//                 field(RealConsump3; RealConsump_CellData[3])
//                 {
//                     ToolTip = 'Specifies the value of the RealConsump3 field.';
//                     CaptionClass = '3,' + RealConsump_CaptionSet[3];
//                     trigger OnDrillDown()
//                     begin
//                         RealConsumpOnDrillDown(3);
//                     end;
//                 }
//                 field(Demand_Before1; Demand_Before_CellData[1])
//                 {
//                     ToolTip = 'Specifies the value of the Demand_Before1 field.';
//                     Visible = false;
//                     CaptionClass = '3,' + Demand_Before_CaptionSet[1];
//                     trigger OnDrillDown()
//                     begin
//                         Demand_Before_OnDrillDown(1);
//                     end;
//                 }
//                 field(Demand_Before2; Demand_Before_CellData[2])
//                 {
//                     ToolTip = 'Specifies the value of the Demand_Before2 field.';
//                     Visible = false;
//                     CaptionClass = '3,' + Demand_Before_CaptionSet[2];
//                     trigger OnDrillDown()
//                     begin
//                         Demand_Before_OnDrillDown(2);
//                     end;
//                 }
//                 field(Demand_Before3; Demand_Before_CellData[3])
//                 {
//                     ToolTip = 'Specifies the value of the Demand_Before3 field.';
//                     Visible = true;
//                     CaptionClass = '3,' + Demand_Before_CaptionSet[3];
//                     trigger OnDrillDown()
//                     begin
//                         Demand_Before_OnDrillDown(3);
//                     end;
//                 }
//                 field(Demand1; Demand_CellData[1])
//                 {
//                     ToolTip = 'Specifies the value of the Demand1 field.';
//                     CaptionClass = '3,' + Demand_CaptionSet[1];
//                     Visible = true;
//                     trigger OnDrillDown()
//                     begin
//                         DemandOnDrillDown(1);
//                     end;
//                 }
//                 field(Demand2; Demand_CellData[2])
//                 {
//                     ToolTip = 'Specifies the value of the Demand2 field.';
//                     CaptionClass = '3,' + Demand_CaptionSet[2];
//                     Visible = true;
//                     trigger OnDrillDown()
//                     begin
//                         DemandOnDrillDown(2);
//                     end;
//                 }
//                 field(Demand3; Demand_CellData[3])
//                 {
//                     ToolTip = 'Specifies the value of the Demand3 field.';
//                     CaptionClass = '3,' + Demand_CaptionSet[3];
//                     Visible = true;
//                     trigger OnDrillDown()
//                     begin
//                         DemandOnDrillDown(3);
//                     end;
//                 }
//                 field(Demand4; Demand_CellData[4])
//                 {
//                     ToolTip = 'Specifies the value of the Demand4 field.';
//                     Visible = true;
//                     CaptionClass = '3,' + Demand_CaptionSet[4];
//                     trigger OnDrillDown()
//                     begin
//                         DemandOnDrillDown(4);
//                     end;
//                 }
//                 field(Purch_Before1; Purch_Before_CellData[1])
//                 {
//                     ToolTip = 'Specifies the value of the Purch_Before1 field.';
//                     Visible = false;
//                     CaptionClass = '3,' + Purch_Before_CaptionSet[1];
//                     trigger OnDrillDown()
//                     begin
//                         Purch_Before_OnDrillDown(1);
//                     end;
//                 }
//                 field(Purch_Before2; Purch_Before_CellData[2])
//                 {
//                     ToolTip = 'Specifies the value of the Purch_Before2 field.';
//                     Visible = false;
//                     CaptionClass = '3,' + Purch_Before_CaptionSet[2];
//                     trigger OnDrillDown()
//                     begin
//                         Purch_Before_OnDrillDown(2);
//                     end;
//                 }
//                 field(Purch_Before3; Purch_Before_CellData[3])
//                 {
//                     ToolTip = 'Specifies the value of the Purch_Before3 field.';
//                     Visible = true;
//                     CaptionClass = '3,' + Purch_Before_CaptionSet[3];
//                     trigger OnDrillDown()
//                     begin
//                         Purch_Before_OnDrillDown(3);
//                     end;
//                 }
//                 field(Purch1; Purch_CellData[1])
//                 {
//                     ToolTip = 'Specifies the value of the Purch1 field.';
//                     Visible = true;
//                     CaptionClass = '3,' + Purch_CaptionSet[1];
//                     trigger OnDrillDown()
//                     begin
//                         PurchOnDrillDown(1);
//                     end;
//                 }
//                 field(Purch2; Purch_CellData[2])
//                 {
//                     ToolTip = 'Specifies the value of the Purch2 field.';
//                     Visible = true;
//                     CaptionClass = '3,' + Purch_CaptionSet[2];
//                     trigger OnDrillDown()
//                     begin
//                         PurchOnDrillDown(2);
//                     end;
//                 }
//                 field(Purch3; Purch_CellData[3])
//                 {
//                     ToolTip = 'Specifies the value of the Purch3 field.';
//                     Visible = true;
//                     CaptionClass = '3,' + Purch_CaptionSet[3];
//                     trigger OnDrillDown()
//                     begin
//                         PurchOnDrillDown(3);
//                     end;
//                 }
//                 field(Purch4; Purch_CellData[4])
//                 {
//                     ToolTip = 'Specifies the value of the Purch4 field.';
//                     CaptionClass = '3,' + Purch_CaptionSet[4];
//                     Visible = true;
//                     trigger OnDrillDown()
//                     begin
//                         PurchOnDrillDown(4);
//                     end;
//                 }
//                 /*field(Field1; MATRIX_CellData[1])
//                 {
//                     ToolTip = 'Specifies the value of the field1.';
//                     Visible = false;
//                     trigger OnValidate()
//                     begin
//                         QtyValidate(1);
//                     end;

//                     trigger OnDrillDown()
//                     begin
//                         MatrixOnDrillDown(1);
//                     end;
//                 }
//                 field(Field2; MATRIX_CellData[2])
//                 {
//                     ToolTip = 'Specifies the value of the field2.';
//                     Visible = false;
//                     trigger OnValidate()
//                     begin
//                         QtyValidate(2);
//                     end;

//                     trigger OnDrillDown()
//                     begin
//                         MatrixOnDrillDown(2);
//                     end;
//                 }
//                 field(Field3; MATRIX_CellData[3])
//                 {
//                     ToolTip = 'Specifies the value of the field3.';
//                     Visible = false;
//                     trigger OnValidate()
//                     begin
//                         QtyValidate(3);
//                     end;

//                     trigger OnDrillDown()
//                     begin
//                         MatrixOnDrillDown(3);
//                     end;
//                 }
//                 field(Field4; MATRIX_CellData[4])
//                 {
//                     ToolTip = 'Specifies the value of the field4.';
//                     Visible = false;
//                     trigger OnValidate()
//                     begin
//                         QtyValidate(4);
//                     end;

//                     trigger OnDrillDown()
//                     begin
//                         MatrixOnDrillDown(4);
//                     end;
//                 }
//                 */
//             }
//         }
//     }
//     trigger OnInit()
//     begin
//         //Field4Visible := TRUE;
//         //Field3Visible := TRUE;
//         //Field2Visible := TRUE;
//         //Field1Visible := TRUE;
//     end;

//     trigger OnOpenPage()
//     begin
//         Rec.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     end;

//     // trigger OnAfterGetRecord()
//     // var
//     //     MATRIX_CurrentColumnOrdinal: Integer;
//     // begin
//     //     StyleText_1 := 'Standard';
//     //     StyleText_2 := 'Standard';
//     //     StyleText_3 := 'Standard';
//     //     StyleText_4 := 'Standard';

//     //     MATRIX_CurrentColumnOrdinal := 0;
//     //     while MATRIX_CurrentColumnOrdinal < MATRIX_NoOfMatrixColumns do begin
//     //         MATRIX_CurrentColumnOrdinal := MATRIX_CurrentColumnOrdinal + 1;
//     //         MATRIX_OnAfterGetRecord(MATRIX_CurrentColumnOrdinal);
//     //     end;
//     //     if (MATRIX_CurrentColumnOrdinal > 0) and (QtyType = QtyType::"Net Change") then
//     //         Rec.SetRange("Date Filter", MatrixRecords[1]."Period Start", MatrixRecords[MATRIX_CurrentColumnOrdinal]."Period End");

//     //     //BRST.006.13 BEGIN
//     //     if (ItemQtyFilter > 0) and (ApplyItemQtyFilter) then begin
//     //         Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//     //         ApplyItemQtyFilter := false;
//     //     end;
//     //     //BRST.006.13 END
//     // end;

//     var
//         Demand_Before_DateRecords: array[3] of Record Date;
//         MatrixRecords: array[4] of Record Date;
//         Purch_Before_DateRecords: array[3] of Record Date;
//         RealConsumpDateRecords: array[3] of Record Date;
//         //ExcelBuf: Record "Excel Buffer";
//         //ApplyItemQtyFilter: Boolean;
//         //Field1Visible: Boolean;
//         //Field2Visible: Boolean;
//         //Field3Visible: Boolean;
//         //Field4Visible: Boolean;
//         //ProductionForecastName: Code[10];
//         Demand_Before_CellData: array[3] of Decimal;
//         Demand_CellData: array[4] of Decimal;
//         ItemQtyFilter: Decimal;
//         MATRIX_CellData: array[4] of Decimal;
//         Purch_Before_CellData: array[3] of Decimal;
//         Purch_CellData: array[4] of Decimal;
//         RealConsump_CellData: array[3] of Decimal;
//         ForecastType: Enum "ForecastType FLX";
//         QtyType: Enum "QtyType FLX";
//         //Demand_Before_NoOfMatrixColumns: Integer;
//         //ItemQtyFilterFieldNo: Integer;
//         MATRIX_NoOfMatrixColumns: Integer;
//         //Purch_Before_NoOfMatrixColumns: Integer;
//         //RealConsump_NoOfMatrixColumns: Integer;
//         //Text000Lbl: Label 'The Forecast On field must be Sales Items or Component.';
//         //Text001Lbl: Label 'A forecast was previously made on the %1. Do you want all forecasts of the period %2-%3 moved to the start of the period?';
//         //Text003Lbl: Label 'You must set a location filter.';
//         //Text004Lbl: Label 'You must change view to Sales Items or Component.';
//         Demand_Before_CaptionSet: array[3] of Text[50];
//         Demand_CaptionSet: array[4] of Text[50];
//         MATRIX_CaptionSet: array[4] of Text[50];
//         Purch_Before_CaptionSet: array[3] of Text[50];
//         Purch_CaptionSet: array[4] of Text[50];
//         RealConsump_CaptionSet: array[3] of Text[50];
//         // StyleText_1: Text[50];
//         // StyleText_2: Text[50];
//         // StyleText_3: Text[50];
//         // StyleText_4: Text[50];
//         DateFilter: Text[250];
//         LocationFilter: Text[250];

//     local procedure SetDateFilter(ColumnID: Integer)
//     begin
//         if DateFilter <> '' then
//             MatrixRecords[ColumnID].SetFilter("Period Start", DateFilter)
//         else
//             MatrixRecords[ColumnID].SetRange("Period Start");

//         Rec.SetRange("Date Filter", MatrixRecords[ColumnID]."Period Start", MatrixRecords[ColumnID]."Period End");
//     end;

//     procedure Load(MatrixColumns1: array[4] of Text[50]; var MatrixRecords1: array[4] of Record Date; ProductionForecastName1: Text[30]; DateFilter1: Text; LocationFilter1: Text; var ForecastType1: Enum "ForecastType FLX"; var QtyType1: Enum "QtyType FLX"; NoOfMatrixColumns1: Integer; ItemQtyFilter1: Decimal)
//     begin

//         CopyArray(MATRIX_CaptionSet, MatrixColumns1, 1);
//         CopyArray(MatrixRecords, MatrixRecords1, 1);
//         //MATRIX_CaptionSet[4]+=' Ve Sonrası';

//         //ProductionForecastName := ProductionForecastName1;
//         DateFilter := CopyStr(DateFilter1, 1, 250);
//         LocationFilter := CopyStr(LocationFilter1, 1, 250);
//         ForecastType := ForecastType1;
//         QtyType := QtyType1;
//         //MATRIX_NoOfMatrixColumns := NoOfMatrixColumns1;
//         //BRST.006.13 BEGIN
//         ItemQtyFilter := ItemQtyFilter1;
//         if ItemQtyFilter > 0 then begin
//             Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//             //ApplyItemQtyFilter := true;
//         end;
//         //BRST.006.13 END
//         SetVisible();
//     end;

//     procedure RealConsumpLoad(MatrixColumns1: array[3] of Text[50]; var MatrixRecords1: array[3] of Record Date; ProductionForecastName1: Text[30]; DateFilter1: Text; LocationFilter1: Text; var ForecastType1: Enum "ForecastType FLX"; var QtyType1: Enum "QtyType FLX"; NoOfMatrixColumns1: Integer; ItemQtyFilter1: Decimal)
//     begin

//         CopyArray(RealConsump_CaptionSet, MatrixColumns1, 1);
//         CopyArray(RealConsumpDateRecords, MatrixRecords1, 1);
//         //MESSAGE(RealConsump_CaptionSet[1]);
//         //ProductionForecastName := ProductionForecastName1;
//         DateFilter := CopyStr(DateFilter1, 1, 250);
//         LocationFilter := CopyStr(LocationFilter1, 1, 250);
//         ForecastType := ForecastType1;
//         QtyType := QtyType1;
//         //RealConsump_NoOfMatrixColumns := NoOfMatrixColumns1;
//         //BRST.006.13 BEGIN
//         ItemQtyFilter := ItemQtyFilter1;
//         if ItemQtyFilter > 0 then begin
//             Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//             //ApplyItemQtyFilter := true;
//         end;
//         //BRST.006.13 END
//     end;

//     procedure DemandBeforeLoad(MatrixColumns1: array[3] of Text[50]; var MatrixRecords1: array[3] of Record Date; ProductionForecastName1: Text[30]; DateFilter1: Text; LocationFilter1: Text; var ForecastType1: Enum "ForecastType FLX"; var QtyType1: Enum "QtyType FLX"; NoOfMatrixColumns1: Integer; ItemQtyFilter1: Decimal)
//     begin
//         //FLEX-240
//         CopyArray(Demand_Before_CaptionSet, MatrixColumns1, 1);
//         CopyArray(Demand_Before_DateRecords, MatrixRecords1, 1);
//         //MESSAGE(RealConsump_CaptionSet[1]);
//         //ProductionForecastName := ProductionForecastName1;
//         DateFilter := CopyStr(DateFilter1, 1, 250);
//         LocationFilter := CopyStr(LocationFilter1, 1, 250);
//         ForecastType := ForecastType1;
//         QtyType := QtyType1;
//         //Demand_Before_NoOfMatrixColumns := NoOfMatrixColumns1;
//         //BRST.006.13 BEGIN
//         ItemQtyFilter := ItemQtyFilter1;
//         if ItemQtyFilter > 0 then begin
//             Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//             //ApplyItemQtyFilter := true;
//         end;
//         //BRST.006.13 END
//     end;

//     procedure PurchaseBeforeLoad(MatrixColumns1: array[3] of Text[50]; var MatrixRecords1: array[3] of Record Date; ProductionForecastName1: Text[30]; DateFilter1: Text; LocationFilter1: Text; var ForecastType1: Enum "ForecastType FLX"; var QtyType1: Enum "QtyType FLX"; NoOfMatrixColumns1: Integer; ItemQtyFilter1: Decimal)
//     begin
//         //FLEX-240
//         CopyArray(Purch_Before_CaptionSet, MatrixColumns1, 1);
//         CopyArray(Purch_Before_DateRecords, MatrixRecords1, 1);
//         //MESSAGE(RealConsump_CaptionSet[1]);
//         //ProductionForecastName := ProductionForecastName1;
//         DateFilter := CopyStr(DateFilter1, 1, 250);
//         LocationFilter := CopyStr(LocationFilter1, 1, 250);
//         ForecastType := ForecastType1;
//         QtyType := QtyType1;
//         //Purch_Before_NoOfMatrixColumns := NoOfMatrixColumns1;
//         //BRST.006.13 BEGIN
//         ItemQtyFilter := ItemQtyFilter1;
//         if ItemQtyFilter > 0 then begin
//             Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//             //ApplyItemQtyFilter := true;
//         end;
//         //BRST.006.13 END
//     end;

//     procedure DemandLoad(MatrixColumns1: array[4] of Text[50]; var MatrixRecords1: array[4] of Record Date; ProductionForecastName1: Text[30]; DateFilter1: Text; LocationFilter1: Text; var ForecastType1: Enum "ForecastType FLX"; var QtyType1: Enum "QtyType FLX"; NoOfMatrixColumns1: Integer; ItemQtyFilter1: Decimal)
//     begin
//         CopyArray(Demand_CaptionSet, MatrixColumns1, 1);
//         //CopyArray(MatrixRecords,MatrixRecords1,1);
//         //Demand_CaptionSet[4]+=' Ve Sonrası';

//         //ProductionForecastName := ProductionForecastName1;
//         DateFilter := CopyStr(DateFilter1, 1, 250);
//         LocationFilter := CopyStr(LocationFilter1, 1, 250);
//         ForecastType := ForecastType1;
//         QtyType := QtyType1;
//         MATRIX_NoOfMatrixColumns := NoOfMatrixColumns1;
//         //BRST.006.13 BEGIN
//         ItemQtyFilter := ItemQtyFilter1;
//         if ItemQtyFilter > 0 then begin
//             Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//             //ApplyItemQtyFilter := true;
//         end;
//         //BRST.006.13 END
//         SetVisible();
//     end;

//     procedure PurchaseLoad(MatrixColumns1: array[4] of Text[50]; var MatrixRecords1: array[4] of Record Date; ProductionForecastName1: Text[30]; DateFilter1: Text; LocationFilter1: Text; var ForecastType1: Enum "ForecastType FLX"; var QtyType1: Enum "QtyType FLX"; NoOfMatrixColumns1: Integer; ItemQtyFilter1: Decimal)
//     begin
//         CopyArray(Purch_CaptionSet, MatrixColumns1, 1);
//         //CopyArray(MatrixRecords,MatrixRecords1,1);
//         //Purch_CaptionSet[4]+=' Ve Sonrası';

//         //ProductionForecastName := ProductionForecastName1;
//         DateFilter := CopyStr(DateFilter1, 1, 250);
//         LocationFilter := CopyStr(LocationFilter1, 1, 250);
//         ForecastType := ForecastType1;
//         QtyType := QtyType1;
//         MATRIX_NoOfMatrixColumns := NoOfMatrixColumns1;
//         //BRST.006.13 BEGIN
//         ItemQtyFilter := ItemQtyFilter1;
//         if ItemQtyFilter > 0 then begin
//             Rec.SetFilter(Inventory, '<%1', ItemQtyFilter);
//             //ApplyItemQtyFilter := true;
//         end;
//         //BRST.006.13 END
//         SetVisible();
//     end;

//     /*local procedure MatrixOnDrillDown(ColumnID: Integer)
//     var
//         ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//     begin
//         SetDateFilter(ColumnID);
//         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//         ShortageListCumulativeQty.SetRange("Document Date", MatrixRecords[ColumnID]."Period Start", MatrixRecords[ColumnID]."Period End");
//         ShortageListCumulativeQty.SetRange("Real Consump Qty", 0);
//         Page.Run(Page::"Shortage List - Cumulative Qty", ShortageListCumulativeQty);
//     end;
//     */

//     // local procedure MATRIX_OnAfterGetRecord(ColumnOrdinal: Integer)
//     // var
//     //     ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//     //     // ShortageListCumulativeQtyColo: Record "Shortage List - Cumulative Qty";
//     //     // CumNegativeFound: Boolean;
//     //     sd: Date;
//     // //toplamnegatif: Decimal;
//     // begin
//     //     //FLEX-240
//     //     sd := Today() - 1000;
//     //     SetDateFilter(ColumnOrdinal);

//     //     MATRIX_CellData[ColumnOrdinal] := 0;
//     //     ShortageListCumulativeQty.Reset();
//     //     ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//     //     ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     //     ShortageListCumulativeQty.SetRange("Document Date", 0D, MatrixRecords[ColumnOrdinal]."Period End");
//     //     ShortageListCumulativeQty.SetRange("Real Consump Qty", 0);
//     //     if ShortageListCumulativeQty.FindLast() then
//     //         MATRIX_CellData[ColumnOrdinal] := ShortageListCumulativeQty."Cumulative Net Qty.";

//     //     Demand_CellData[ColumnOrdinal] := 0;
//     //     ShortageListCumulativeQty.Reset();
//     //     ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//     //     ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     //     ShortageListCumulativeQty.SetRange("Document Date", MatrixRecords[ColumnOrdinal]."Period Start", MatrixRecords[ColumnOrdinal]."Period End");
//     //     //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//     //     ShortageListCumulativeQty.SetFilter("Negative Qty.", '<>0');
//     //     if ShortageListCumulativeQty.FindSet() then
//     //         repeat
//     //             Demand_CellData[ColumnOrdinal] += -ShortageListCumulativeQty."Negative Qty.";
//     //         until ShortageListCumulativeQty.Next() = 0;

//     //     Purch_CellData[ColumnOrdinal] := 0;
//     //     ShortageListCumulativeQty.Reset();
//     //     ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//     //     ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     //     ShortageListCumulativeQty.SetRange("Document Date", MatrixRecords[ColumnOrdinal]."Period Start", MatrixRecords[ColumnOrdinal]."Period End");
//     //     //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//     //     ShortageListCumulativeQty.SetFilter("Positive Qty.", '<>0');
//     //     if ShortageListCumulativeQty.FindSet() then
//     //         repeat
//     //             Purch_CellData[ColumnOrdinal] += ShortageListCumulativeQty."Positive Qty.";
//     //         until ShortageListCumulativeQty.Next() = 0;

//     //     if ColumnOrdinal < 4 then begin
//     //         RealConsump_CellData[ColumnOrdinal] := 0;
//     //         ShortageListCumulativeQty.Reset();
//     //         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//     //         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     //         ShortageListCumulativeQty.SetRange("Document Date", RealConsumpDateRecords[ColumnOrdinal]."Period Start", RealConsumpDateRecords[ColumnOrdinal]."Period End");
//     //         ShortageListCumulativeQty.SetFilter("Real Consump Qty", '<>0');
//     //         if ShortageListCumulativeQty.FindSet() then
//     //             repeat
//     //                 RealConsump_CellData[ColumnOrdinal] += ShortageListCumulativeQty."Real Consump Qty";
//     //             until ShortageListCumulativeQty.Next() = 0;

//     //         //FLEX-240 BEGIN
//     //         Demand_Before_CellData[ColumnOrdinal] := 0;
//     //         //toplamnegatif := 0;
//     //         ShortageListCumulativeQty.Reset();
//     //         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//     //         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     //                                                                  //ShortageListCumulativeQty.SETRANGE("Document Date",Demand_Before_DateRecords[ColumnOrdinal]."Period Start",Demand_Before_DateRecords[ColumnOrdinal]."Period End");
//     //                                                                  //ShortageListCumulativeQty.SETRANGE("Document Date", 01011900D, Demand_Before_DateRecords[ColumnOrdinal]."Period End");
//     //         ShortageListCumulativeQty.SetRange("Document Date", sd, Demand_Before_DateRecords[ColumnOrdinal]."Period End");
//     //         //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//     //         ShortageListCumulativeQty.SetFilter("Negative Qty.", '<>0');
//     //         if ShortageListCumulativeQty.FindSet() then
//     //             repeat
//     //                 Demand_Before_CellData[ColumnOrdinal] += -ShortageListCumulativeQty."Negative Qty.";
//     //             //toplamnegatif += ShortageListCumulativeQty."Negative Qty.";
//     //             until ShortageListCumulativeQty.Next() = 0;
//     //         //IF "Item No." = '100-40' THEN
//     //         //   MESSAGE('toplam negatif:'+FORMAT(toplamnegatif));
//     //         Purch_Before_CellData[ColumnOrdinal] := 0;
//     //         ShortageListCumulativeQty.Reset();
//     //         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//     //         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//     //                                                                  //ShortageListCumulativeQty.SETRANGE("Document Date",Purch_Before_DateRecords[ColumnOrdinal]."Period Start",Purch_Before_DateRecords[ColumnOrdinal]."Period End");
//     //         ShortageListCumulativeQty.SetRange("Document Date", sd, Purch_Before_DateRecords[ColumnOrdinal]."Period End");
//     //         //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//     //         ShortageListCumulativeQty.SetFilter("Positive Qty.", '<>0');
//     //         if ShortageListCumulativeQty.FindSet() then
//     //             repeat
//     //                 Purch_Before_CellData[ColumnOrdinal] += ShortageListCumulativeQty."Positive Qty.";
//     //             until ShortageListCumulativeQty.Next() = 0;
//     //         //FLEX-240 END
//     //     end;

//         //CumNegativeFound := false;
//         // ShortageListCumulativeQtyColo.Reset();
//         // ShortageListCumulativeQtyColo.SetRange("Item No.", Rec."Item No.");
//         // ShortageListCumulativeQtyColo.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//         // ShortageListCumulativeQtyColo.SetRange("Document Date", MatrixRecords[ColumnOrdinal]."Period Start", MatrixRecords[ColumnOrdinal]."Period End");
//         // ShortageListCumulativeQtyColo.SetRange("Real Consump Qty", 0);
//         // if ShortageListCumulativeQtyColo.FindSet() then
//         //     repeat
//         //         if ShortageListCumulativeQtyColo."Cumulative Net Qty." < 0 then
//         //             //CumNegativeFound := true;
//         //     until ShortageListCumulativeQtyColo.Next() = 0;
//         //         if MATRIX_CellData[ColumnOrdinal] < 0 then
//         //             CumNegativeFound := true;

//         // if CumNegativeFound then //begin
//         //     case ColumnOrdinal of
//         //         1:
//         //             StyleText_1 := 'Attention';
//         //         2:
//         //             StyleText_2 := 'Attention';
//         //         3:
//         //             StyleText_3 := 'Attention';
//         //         4:
//         //             StyleText_4 := 'Attention';
//         //     end;
//         //end;
//     end;

//     procedure SetVisible()
//     begin
//         //Field1Visible := MATRIX_CaptionSet[1] <> '';
//         //Field2Visible := MATRIX_CaptionSet[2] <> '';
//         //Field3Visible := MATRIX_CaptionSet[3] <> '';
//         //Field4Visible := MATRIX_CaptionSet[4] <> '';
//     end;

//     //local procedure QtyValidate(ColumnID: Integer)
//     //begin

//     //end;

//     //local procedure Enter_BaseQty(ColumnID: Integer)
//     //begin

//     //end;

//     //local procedure ProdForecastQtyBase_OnValidate(ColumnID: Integer)
//     //begin

//     //end;

//     //local procedure ProdForecastByLocationQtyBase(var SourceProdForecastEntry: Record "Production Forecast Entry"): Decimal
//     //begin

//     //end;

//     local procedure RealConsumpOnDrillDown(ColumnID: Integer)
//     var
//         ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//     begin
//         SetDateFilter(ColumnID);
//         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//         ShortageListCumulativeQty.SetRange("Document Date", RealConsumpDateRecords[ColumnID]."Period Start", RealConsumpDateRecords[ColumnID]."Period End");
//         ShortageListCumulativeQty.SetFilter("Real Consump Qty", '<>0');
//         Page.Run(Page::"Shortage List - Real Consump", ShortageListCumulativeQty);
//     end;

//     local procedure DemandOnDrillDown(ColumnID: Integer)
//     var
//         ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//     begin
//         SetDateFilter(ColumnID);
//         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//         ShortageListCumulativeQty.SetRange("Document Date", MatrixRecords[ColumnID]."Period Start", MatrixRecords[ColumnID]."Period End");
//         //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//         ShortageListCumulativeQty.SetFilter("Negative Qty.", '<>0');
//         Page.Run(Page::"Shortage List - Demands FLX", ShortageListCumulativeQty);
//     end;

//     local procedure PurchOnDrillDown(ColumnID: Integer)
//     var
//         ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//     begin
//         SetDateFilter(ColumnID);
//         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//         ShortageListCumulativeQty.SetRange("Document Date", MatrixRecords[ColumnID]."Period Start", MatrixRecords[ColumnID]."Period End");
//         //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//         ShortageListCumulativeQty.SetFilter("Positive Qty.", '<>0');
//         Page.Run(Page::"Shortage List - Purchases FLX", ShortageListCumulativeQty);
//     end;

//     local procedure Demand_Before_OnDrillDown(ColumnID: Integer)
//     var
//         ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//         sd: Date;
//     begin
//         //FLEX-240
//         sd := Today() - 1000;
//         SetDateFilter(ColumnID);
//         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//                                                                  //ShortageListCumulativeQty.SETRANGE("Document Date",Demand_Before_DateRecords[ColumnID]."Period Start",Demand_Before_DateRecords[ColumnID]."Period End");
//         ShortageListCumulativeQty.SetRange("Document Date", sd, Demand_Before_DateRecords[ColumnID]."Period End");
//         //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//         ShortageListCumulativeQty.SetFilter("Negative Qty.", '<>0');
//         Page.Run(Page::"Shortage List - Demands FLX", ShortageListCumulativeQty);
//     end;

//     local procedure Purch_Before_OnDrillDown(ColumnID: Integer)
//     var
//         ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
//         sd: Date;
//     begin
//         //FLEX-240
//         sd := Today() - 1000;
//         SetDateFilter(ColumnID);
//         ShortageListCumulativeQty.SetRange("Item No.", Rec."Item No.");
//         ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
//                                                                  //ShortageListCumulativeQty.SETRANGE("Document Date",Purch_Before_DateRecords[ColumnID]."Period Start",Purch_Before_DateRecords[ColumnID]."Period End");
//         ShortageListCumulativeQty.SetRange("Document Date", sd, Purch_Before_DateRecords[ColumnID]."Period End");
//         //ShortageListCumulativeQty.SETRANGE("Real Consump Qty",0);
//         ShortageListCumulativeQty.SetFilter("Positive Qty.", '<>0');
//         Page.Run(Page::"Shortage List - Purchases FLX", ShortageListCumulativeQty);
//     end;
// }

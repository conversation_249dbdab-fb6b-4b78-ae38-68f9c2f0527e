/// <summary>
/// TableExtension Prod. Order Component Ext. FLX (ID 60016) extends Record Prod. Order Component.
/// </summary>
tableextension 60016 "Prod. Order Component Ext. FLX" extends "Prod. Order Component"
{
    fields
    {
        field(60000; "Source Item No. FLX"; Code[20])
        {
            Caption = 'Source Item No.';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Item No." where(Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Line No." = field("Prod. Order Line No.")));
        }
        field(60001; "Prod. Order Start Date FLX"; Date)
        {
            Caption = 'Prod. Order Start Date';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Starting Date" where(Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Line No." = field("Prod. Order Line No.")));
        }
        field(60002; "Stop FLX"; Boolean)
        {
            Caption = 'Stop';
            ToolTip = 'Specifies the value of the Stop field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Stop FLX" where(Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Line No." = field("Prod. Order Line No.")));
        }
    }
}

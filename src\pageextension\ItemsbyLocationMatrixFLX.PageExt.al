pageextension 60033 "Items by Location Matrix FLX" extends "Items by Location Matrix"
{
    layout
    {
        addafter(Description)
        {
            field("Item Category Code FLX"; Rec."Item Category Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Item Category Code field.';
            }
            field("Qty. on Purch. Order FLX"; Rec."Qty. on Purch. Order")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Qty. on Purch. Order field.';
            }
            field("Qty. on Prod. Order FLX"; Rec."Qty. on Prod. Order")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Qty. on Prod. Order field.';
            }
            field("Qty. on Sales Order FLX"; Rec."Qty. on Sales Order")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Qty. on Sales Order field.';
            }
        }
    }
}
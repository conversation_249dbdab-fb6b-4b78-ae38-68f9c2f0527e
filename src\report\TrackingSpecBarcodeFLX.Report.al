report 60010 "Tracking Spec Barcode FLX"
{
    ApplicationArea = All;
    Caption = 'LotNo Label';
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/LotLabel.rdlc';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(TrackingSpecification; Integer)
        {
            column(ItemNo; glItemNo)
            {
            }
            column(Lot_No_; glLotNo)
            {
            }
            column(Description; glDesc)
            {
            }
            trigger OnPreDataItem()
            begin
                SetRange(Number, 1);
            end;
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
    procedure SetVar(ItemNo: Code[20]; LotNo: Code[50])
    var
        Item: Record Item;
    begin
        glItemNo := ItemNo;
        glLotNo := LotNo;
        if Item.Get(ItemNo) then
            glDesc := Item.Description;
    end;

    var
        glItemNo: Code[20];
        glLotNo: Code[50];
        glDesc: Text[100];
}
tableextension 60017 "Sales Header FLX" extends "Sales Header"
{
    fields
    {
        field(60000; "Note FLX"; Text[500])
        {
            Caption = 'Note';
            ToolTip = 'Specifies the value of the Note field.';
        }
        modify("Ship-to Code")
        {
            trigger OnAfterValidate()
            var
                LotNoInformation: Record "Lot No. Information";
                PackageNoInformation: Record "Package No. Information";
                ProdOrderLine: Record "Prod. Order Line";
            begin
                ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
                ProdOrderLine.ModifyAll("Ship-to Code FLX", Rec."Ship-to Code", true);

                LotNoInformation.SetRange("Production Order No. FLX", Rec."No.");
                LotNoInformation.ModifyAll("Ship-to Code FLX", Rec."Ship-to Code", true);

                PackageNoInformation.SetRange("Production Order No. FLX", Rec."No.");
                PackageNoInformation.ModifyAll("Ship-to Code FLX", Rec."Ship-to Code", true);
            end;
        }
    }
}
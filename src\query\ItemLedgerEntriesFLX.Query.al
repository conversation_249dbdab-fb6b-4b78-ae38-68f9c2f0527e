query 60003 "Item Ledger Entries FLX"
{
    Caption = 'Item Ledger Entries';
    QueryType = Normal;
    DataAccessIntent = ReadOnly;

    elements
    {
        dataitem(Item_Ledger_Entry; "Item Ledger Entry")
        {
            column(Lot_No_; "Lot No.")
            {

            }

            column(Location_Code; "Location Code")
            {

            }

            column(Remaining_Quantity; "Remaining Quantity")
            {

            }

            column(Entry_Type; "Entry Type")
            {
                ColumnFilter = Entry_Type = const("Output");
            }
        }
    }

}
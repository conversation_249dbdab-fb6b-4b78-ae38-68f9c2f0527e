tableextension 60005 "Sales Invoice Line FLX" extends "Sales Invoice Line"
{
    fields
    {
        field(60000; "Piece FLX"; Integer)
        {
            Caption = 'Piece';
            ToolTip = 'Specifies the value of the Piece field.';
        }
        field(60001; "Hose Length FLX"; Decimal)
        {
            Caption = 'Hose Length';
            ToolTip = 'Specifies the value of the Hose Lenght field.';
        }
        field(60002; "Unit Weight (Base) FLX"; Decimal)
        {
            Caption = 'Coil Weight';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Coil Weight field.';
        }
        // field(60003; "Production Order Status FLX"; Enum "Production Order Status")
        // {
        //     Caption = 'Production Order Status';
        // }
        field(60004; "Production Order No. FLX"; Code[20])
        {
            Caption = 'Production Order No.';
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(60005; "Sell-to Customer Name FLX"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Sell-to Customer Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        // field(60006; "Your Reference FLX"; Text[35])
        // {
        //     Caption = 'Your Reference';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Sales Header"."Your Reference" where("No." = field("Document No.")));
        // }
        field(60007; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Ship-to Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(60008; "Flexati Shipment Date FLX"; Date)
        {
            Caption = 'Flexati Shipment Date';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Flexati Shipment Date FLX field.';
        }
        field(60010; "Your Reference FLX"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(60011; "Currency Code FLX"; Code[10])
        {
            Caption = 'Currency Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Currency Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(60012; "Currency Factor FLX"; Decimal)
        {
            Caption = 'Currency Factor';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Currency Factor" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Currency Factor field.';
        }
        field(60013; "Sales Order Quantity FLX"; Decimal)
        {
            Caption = 'Sales Order Quantity';
            Editable = false;
            AllowInCustomizations = Always;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line".Quantity where("Document No." = field("Order No."), "Line No." = field("Order Line No.")));
            ToolTip = 'Specifies the value of the Sales Order Quantity field.';
        }
    }
}
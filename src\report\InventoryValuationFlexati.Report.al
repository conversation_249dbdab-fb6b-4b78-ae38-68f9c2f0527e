report 60014 "Inventory Valuation Flexati"
{
    ApplicationArea = All;
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/InventoryValuationFLX.rdlc';
    Caption = 'Inventory Valuation Flexati';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(Item; Item)
        {
            RequestFilterFields = "No.";
            column(PageNoCaptionLbl; PageNoCaptionLbl)
            {

            }
            column(Text005Lbl; Text005Lbl)
            {

            }
            column(STRSUBSTNO_Text005_StartDateText; StrSubstNo(Text005Lbl, StartDateText))
            {

            }
            column(STRSUBSTNO_Text005_EndDate; StrSubstNo(Text005Lbl, Format(EndDate)))
            {

            }
            column(No; "No.")
            {
            }
            column(Description; Description)
            {
            }
            column(ItemCategoryCode; "Item Category Code")
            {
            }
            column(BaseUnitofMeasure; "Base Unit of Measure")
            {
            }
            column(InventoryPostingGroup; "Inventory Posting Group")
            {
            }
            column(ItemCategoryCodeCaptionLbl; ItemCategoryCodeCaptionLbl)
            {

            }
            column(BoM_TextLbl; BoM_TextLbl)
            {

            }
            column(GeneralTotalCaptionLbl; GeneralTotalCaptionLbl)
            {

            }
            column(COMPANYNAME; CompanyName())
            {

            }
            column(ItemFilter; ItemFilter)
            {

            }
            column(Inventory_ValuationCaptionLbl; Inventory_ValuationCaptionLbl)
            {

            }
            column(This_report_includes_entries_that_have_been_posted_with_expected_costs_CaptionLbl; This_report_includes_entries_that_have_been_posted_with_expected_costs_CaptionLbl)
            {

            }
            column(IncreaseInvoicedQtyCaptionLbl; IncreaseInvoicedQtyCaptionLbl)
            {

            }
            column(DecreaseInvoicedQtyCaptionLbl; DecreaseInvoicedQtyCaptionLbl)
            {

            }
            column(QuantityCaptionLbl; QuantityCaptionLbl)
            {

            }
            column(ValueCaptionLbl; ValueCaptionLbl)
            {

            }
            column(TotalCaptionLbl; TotalCaptionLbl)
            {

            }
            column(Expected_Cost_IncludedCaptionLbl; Expected_Cost_IncludedCaptionLbl)
            {

            }
            column(Expected_Cost_Included_TotalCaptionLbl; Expected_Cost_Included_TotalCaptionLbl)
            {

            }
            column(Expected_Cost_TotalCaptionLbl; Expected_Cost_TotalCaptionLbl)
            {

            }
            column(StartingInvoicedValue; StartingInvoicedValue)
            {

            }
            column(StartingInvoicedQty; StartingInvoicedQty)
            {

            }
            column(StartingExpectedValue; StartingExpectedValue)
            {

            }
            column(StartingExpectedQty; StartingExpectedQty)
            {

            }
            column(IncreaseInvoicedValue; IncreaseInvoicedValue)
            {

            }
            column(IncreaseInvoicedQty; IncreaseInvoicedQty)
            {

            }
            column(IncreaseExpectedValue; IncreaseExpectedValue)
            {

            }
            column(IncreaseExpectedQty; IncreaseExpectedQty)
            {

            }
            column(DecreaseInvoicedValue; DecreaseInvoicedValue)
            {

            }
            column(DecreaseInvoicedQty; DecreaseInvoicedQty)
            {

            }
            column(DecreaseExpectedValue; DecreaseExpectedValue)
            {

            }
            column(DecreaseExpectedQty; DecreaseExpectedQty)
            {

            }
            column(EndingInvoicedValue; StartingInvoicedValue + IncreaseInvoicedValue - DecreaseInvoicedValue)
            {

            }
            column(EndingInvoicedQty; StartingInvoicedQty + IncreaseInvoicedQty - DecreaseInvoicedQty)
            {

            }
            column(EndingExpectedValue; StartingExpectedValue + IncreaseExpectedValue - DecreaseExpectedValue)
            {

            }
            column(EndingExpectedQty; StartingExpectedQty + IncreaseExpectedQty - DecreaseExpectedQty)
            {

            }
            column(CostPostedToGL; CostPostedToGL)
            {

            }
            column(InvCostPostedToGL; InvCostPostedToGL)
            {

            }
            column(ExpCostPostedToGL; ExpCostPostedToGL)
            {

            }
            column(InvCostPostedToGLLbl; InvCostPostedToGLLbl)
            {

            }
            trigger OnAfterGetRecord()
            begin
                CalcFields("Assembly BOM");

                if EndDate = 0D then
                    EndDate := DMY2Date(31, 12, 9999);

                StartingInvoicedValue := 0;
                StartingExpectedValue := 0;
                StartingInvoicedQty := 0;
                StartingExpectedQty := 0;
                IncreaseInvoicedValue := 0;
                IncreaseExpectedValue := 0;
                IncreaseInvoicedQty := 0;
                IncreaseExpectedQty := 0;
                DecreaseInvoicedValue := 0;
                DecreaseExpectedValue := 0;
                DecreaseInvoicedQty := 0;
                DecreaseExpectedQty := 0;
                InvCostPostedToGL := 0;
                CostPostedToGL := 0;
                ExpCostPostedToGL := 0;

                IsEmptyLine := true;
                ValueEntry.Reset();
                ValueEntry.SetCurrentKey("Item No.", "Posting Date", "Item Ledger Entry Type", "Entry Type", "Variance Type", "Item Charge No.", "Location Code", "Variant Code", "Global Dimension 1 Code", "Global Dimension 2 Code", "Source Type", "Source No.");
                ValueEntry.SetRange("Item No.", "No.");
                ValueEntry.SetFilter("Variant Code", GetFilter("Variant Filter"));
                ValueEntry.SetFilter("Location Code", GetFilter("Location Filter"));
                ValueEntry.SetFilter("Global Dimension 1 Code", GetFilter("Global Dimension 1 Filter"));
                ValueEntry.SetFilter("Global Dimension 2 Code", GetFilter("Global Dimension 2 Filter"));

                if StartDate > 0D then begin
                    ValueEntry.SetRange("Posting Date", 0D, CalcDate('<-1D>', StartDate));
                    ValueEntry.CalcSums("Item Ledger Entry Quantity", "Cost Amount (Actual)", "Cost Amount (Expected)", "Invoiced Quantity");
                    AssignAmounts(StartingInvoicedValue, StartingInvoicedQty, StartingExpectedValue, StartingExpectedQty, 1);
                    IsEmptyLine := IsEmptyLine and ((StartingInvoicedValue = 0) and (StartingInvoicedQty = 0));
                    if ShowExpected then
                        IsEmptyLine := IsEmptyLine and ((StartingExpectedValue = 0) and (StartingExpectedQty = 0));
                end;

                ValueEntry.SetRange("Posting Date", StartDate, EndDate);
                ValueEntry.SetFilter(
                  "Item Ledger Entry Type", '%1|%2|%3|%4',
                  ValueEntry."Item Ledger Entry Type"::Purchase,
                  ValueEntry."Item Ledger Entry Type"::"Positive Adjmt.",
                  ValueEntry."Item Ledger Entry Type"::Output,
                  ValueEntry."Item Ledger Entry Type"::"Assembly Output");
                ValueEntry.CalcSums("Item Ledger Entry Quantity", "Cost Amount (Actual)", "Cost Amount (Expected)", "Invoiced Quantity");
                AssignAmounts(IncreaseInvoicedValue, IncreaseInvoicedQty, IncreaseExpectedValue, IncreaseExpectedQty, 1);

                ValueEntry.SetRange("Posting Date", StartDate, EndDate);
                ValueEntry.SetFilter(
                  "Item Ledger Entry Type", '%1|%2|%3|%4',
                  ValueEntry."Item Ledger Entry Type"::Sale,
                  ValueEntry."Item Ledger Entry Type"::"Negative Adjmt.",
                  ValueEntry."Item Ledger Entry Type"::Consumption,
                  ValueEntry."Item Ledger Entry Type"::"Assembly Consumption");
                ValueEntry.CalcSums("Item Ledger Entry Quantity", "Cost Amount (Actual)", "Cost Amount (Expected)", "Invoiced Quantity");
                AssignAmounts(DecreaseInvoicedValue, DecreaseInvoicedQty, DecreaseExpectedValue, DecreaseExpectedQty, -1);

                ValueEntry.SetRange("Posting Date", StartDate, EndDate);
                ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Transfer);
                if ValueEntry.FindSet() then
                    repeat
                        if true in [ValueEntry."Valued Quantity" < 0, not GetOutboundItemEntry(ValueEntry."Item Ledger Entry No.")] then
                            AssignAmounts(DecreaseInvoicedValue, DecreaseInvoicedQty, DecreaseExpectedValue, DecreaseExpectedQty, -1)
                        else
                            AssignAmounts(IncreaseInvoicedValue, IncreaseInvoicedQty, IncreaseExpectedValue, IncreaseExpectedQty, 1);
                    until ValueEntry.Next() = 0;

                IsEmptyLine := IsEmptyLine and ((IncreaseInvoicedValue = 0) and (IncreaseInvoicedQty = 0));
                IsEmptyLine := IsEmptyLine and ((DecreaseInvoicedValue = 0) and (DecreaseInvoicedQty = 0));
                if ShowExpected then begin
                    IsEmptyLine := IsEmptyLine and ((IncreaseExpectedValue = 0) and (IncreaseExpectedQty = 0));
                    IsEmptyLine := IsEmptyLine and ((DecreaseExpectedValue = 0) and (DecreaseExpectedQty = 0));
                end;

                ValueEntry.SetRange("Posting Date", 0D, EndDate);
                ValueEntry.SetRange("Item Ledger Entry Type");
                ValueEntry.CalcSums("Cost Posted to G/L", "Expected Cost Posted to G/L");
                ExpCostPostedToGL += ValueEntry."Expected Cost Posted to G/L";
                InvCostPostedToGL += ValueEntry."Cost Posted to G/L";

                StartingExpectedValue += StartingInvoicedValue;
                IncreaseExpectedValue += IncreaseInvoicedValue;
                DecreaseExpectedValue += DecreaseInvoicedValue;
                CostPostedToGL := ExpCostPostedToGL + InvCostPostedToGL;

                if IsEmptyLine then
                    CurrReport.Skip();

            end;
        }

    }
    requestpage
    {
        layout
        {
            area(Content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(StartDatex; StartDate)
                    {
                        ApplicationArea = Suite;
                        Caption = 'Start Date';
                        ToolTip = 'Specifies Start Date.';
                    }
                    field(EndDatex; EndDate)
                    {
                        ApplicationArea = Suite;
                        Caption = 'End Date';
                        ToolTip = 'Specifies End Date.';
                    }
                    field(IncludeExpectedCost; ShowExpected)
                    {
                        ApplicationArea = Suite;
                        Caption = 'Include Expected Cost';
                        ToolTip = 'Specifies Include Expected Cost.';

                    }

                }
            }

        }
        actions
        {
            area(Processing)
            {
            }
        }
        trigger OnOpenPage()
        begin
            if (StartDate = 0D) and (EndDate = 0D) then
                EndDate := WorkDate();
            ShowExpected := true;

        end;
    }
    trigger OnPreReport()
    begin
        if (StartDate = 0D) and (EndDate = 0D) then
            EndDate := WorkDate();

        //if StartDate in [0D, 01010000D] then
        if StartDate = 0D then
            StartDateText := ''
        else
            StartDateText := Format(StartDate - 1);

        ItemFilter := Item.GetFilters();

    end;

    var
        ValueEntry: Record "Value Entry";
        StartDate: Date;
        EndDate: Date;
        ShowExpected: Boolean;
        ItemFilter: Text;
        StartDateText: Text[10];
        StartingInvoicedValue: Decimal;
        StartingExpectedValue: Decimal;
        StartingInvoicedQty: Decimal;
        StartingExpectedQty: Decimal;
        IncreaseInvoicedValue: Decimal;
        IncreaseExpectedValue: Decimal;
        IncreaseInvoicedQty: Decimal;
        IncreaseExpectedQty: Decimal;
        DecreaseInvoicedValue: Decimal;
        DecreaseExpectedValue: Decimal;
        DecreaseInvoicedQty: Decimal;
        DecreaseExpectedQty: Decimal;
        InvCostPostedToGL: Decimal;
        CostPostedToGL: Decimal;
        ExpCostPostedToGL: Decimal;
        IsEmptyLine: Boolean;
        Text005Lbl: Label 'As of %1', Comment = 'As of %1';
        BoM_TextLbl: Label 'Base UoM';
        Inventory_ValuationCaptionLbl: Label 'Inventory Valuation';
        PageNoCaptionLbl: Label 'Page';
        This_report_includes_entries_that_have_been_posted_with_expected_costs_CaptionLbl: Label 'This report includes entries that have been posted with expected costs.';
        IncreaseInvoicedQtyCaptionLbl: Label 'Increases (LCY)';
        DecreaseInvoicedQtyCaptionLbl: Label 'Decreases (LCY)';
        QuantityCaptionLbl: Label 'Quantity';
        ValueCaptionLbl: Label 'Value';
        InvCostPostedToGLLbl: Label 'Cost Posted to G/L';
        TotalCaptionLbl: Label 'Total';
        GeneralTotalCaptionLbl: Label 'General Total';
        Expected_Cost_Included_TotalCaptionLbl: Label 'Expected Cost Included Total';
        Expected_Cost_TotalCaptionLbl: Label 'Expected Cost Total';
        Expected_Cost_IncludedCaptionLbl: Label 'Expected Cost Included';
        ItemCategoryCodeCaptionLbl: Label 'Item Category Code';

    local procedure AssignAmounts(var InvoicedValue: Decimal; var InvoicedQty: Decimal; var ExpectedValue: Decimal; var ExpectedQty: Decimal; Sign: Decimal)
    begin
        InvoicedQty += ValueEntry."Invoiced Quantity" * Sign;
        ExpectedQty += ValueEntry."Item Ledger Entry Quantity" * Sign;
        if Item."Rolled-up Material Cost" > 0 then begin
            if ValueEntry."Cost Amount (Actual)" <> 0 then
                InvoicedValue += ValueEntry."Invoiced Quantity" * Item."Rolled-up Material Cost" * Sign;
            if ValueEntry."Cost Amount (Expected)" <> 0 then
                ExpectedValue += ValueEntry."Item Ledger Entry Quantity" * Item."Rolled-up Material Cost" * Sign;
        end else begin
            InvoicedValue += ValueEntry."Cost Amount (Actual)" * Sign;
            ExpectedValue += ValueEntry."Cost Amount (Expected)" * Sign;
        end;
    end;

    local procedure GetOutboundItemEntry(ItemLedgerEntryNo: Integer): Boolean
    var
        ItemApplnEntry: Record "Item Application Entry";
        ItemLedgEntry: Record "Item Ledger Entry";
    begin
        ItemApplnEntry.SetCurrentKey("Item Ledger Entry No.");
        ItemApplnEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntryNo);
        if not ItemApplnEntry.FindFirst() then
            exit(true);

        ItemLedgEntry.SetRange("Item No.", Item."No.");
        ItemLedgEntry.SetFilter("Variant Code", Item.GetFilter("Variant Filter"));
        ItemLedgEntry.SetFilter("Location Code", Item.GetFilter("Location Filter"));
        ItemLedgEntry.SetFilter("Global Dimension 1 Code", Item.GetFilter("Global Dimension 1 Filter"));
        ItemLedgEntry.SetFilter("Global Dimension 2 Code", Item.GetFilter("Global Dimension 2 Filter"));
        ItemLedgEntry."Entry No." := ItemApplnEntry."Outbound Item Entry No.";
        //exit(not ItemLedgEntry.FindFirst());
        exit(ItemLedgEntry.IsEmpty());
    end;

    procedure SetStartDate(DateValue: Date)
    begin
        StartDate := DateValue;
    end;

    procedure SetEndDate(DateValue: Date)
    begin
        EndDate := DateValue;
    end;

    procedure InitializeRequest(NewStartDate: Date; NewEndDate: Date; NewShowExpected: Boolean)
    begin
        StartDate := NewStartDate;
        EndDate := NewEndDate;
        ShowExpected := NewShowExpected;
    end;

    /*local procedure GetUrlForReportDrilldown(ItemNumber: Code[20]): Text
    begin
        // Generates a URL to the report which sets tab "Item" and field "Field1" on the request page, such as
        // dynamicsnav://hostname:port/instance/company/runreport?report=5801<&Tenant=tenantId>&filter=Item.Field1:1100.
        // TODO
        // Eventually leverage parameters 5 and 6 of GETURL by adding ",Item,TRUE)" and
        // use filter Item.SETFILTER("No.",'=%1',ItemNumber);.
        exit(GETURL(ClientTypeManagement.GetCurrentClientType, COMPANYNAME, OBJECTTYPE::Report, Report::"Invt. Valuation - Cost Spec.") +
          StrSubstNo('&filter=Item.Field1:%1', ItemNumber));

    end;
*/
}
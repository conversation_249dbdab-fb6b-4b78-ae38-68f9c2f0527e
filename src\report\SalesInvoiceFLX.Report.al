/// <summary>
/// Report Sales Invoice FLX (ID 60003).
/// </summary>
report 60003 "Sales Invoice FLX"
{
    Caption = 'Sales Invoice';
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/StandardSalesInvoice.rdlc';
    ApplicationArea = Basic, Suite;
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(Header; "Sales Header")
        {
            DataItemTableView = sorting("Document Type", "No.") where("Document Type" = const(Invoice));
            RequestFilterFields = "No.", "Sell-to Customer No.", "No. Printed";
            RequestFilterHeading = 'Draft Invoice';
            column(Description_Line_Lbl; Description_Line_Lbl)
            {
            }
            column(SupplierNumber; SupplierNumber)
            {
            }
            column(DateText; DateText)
            {
            }
            column(Quantity_Line_Lbl; Quantity_Line_Lbl)
            {
            }
            column(ItemNo_Line_Lbl; ItemNo_Line_Lbl)
            {
            }
            column(LineAmount_Line_Lbl; LineAmount_Line_Lbl)
            {
            }
            column(LineNoLbl; LineNoLbl)
            {
            }
            column(CustPoLbl; CustPoLbl)
            {
            }
            column(OurPOLbl; OurPOLbl)
            {
            }
            column(UnitPrice_Lbl; UnitPrice_Lbl)
            {
            }
            column(UnitOfMeasure_Lbl; UnitOfMeasure_Lbl)
            {
            }
            column(TotalPackage; TotalPackage)
            {
            }
            column(PackageDesc; PackageDesc)
            {
            }
            column(GrandPallettotaltxt; GrandPallettotaltxt)
            {
            }
            column(TotalGrossWeight; TotalGrossWeight)
            {
            }
            column(TotalNetWeight; TotalNetWeight)
            {
            }
            column(TotalTareWeight; TotalTareWeight)
            {
            }
            column(GrossWeightLbl; GrossWeightLbl)
            {
            }
            column(TareWeightLbl; TareWeightLbl)
            {
            }
            column(NetWeightLbl; NetWeightLbl)
            {
            }
            column(ContainerNumberLbl; ContainerNumberLbl)
            {
            }
            column(CountryOfOriginLbl; CountryOfOriginLbl)
            {
            }
            column(ContainerNumber; ContainerNumber)
            {
            }
            column(CountryOfOrigin; CountryOfOrigin)
            {
            }
            column(plasticwooddesc; plasticwooddesc)
            {
            }
            column(CurrencyCode; CurrencyCode)
            {
            }
            column(PackagingLbl; PackagingLbl)
            {
            }
            column(TotalChargesLbl; TotalChargesLbl)
            {
            }
            column(NetGoodsLbl; NetGoodsLbl)
            {
            }
            column(TotalAmountLbl; TotalAmountLbl)
            {
            }
            column(CompInfoName; CompanyInfo.Name)
            {
            }
            column(CompanyAddress1; CompanyAddr[1])
            {
            }
            column(CompanyAddress2; CompanyAddr[2])
            {
            }
            column(CompanyAddress3; CompanyAddr[3])
            {
            }
            column(CompanyAddress4; CompanyAddr[4])
            {
            }
            column(CompanyAddress5; CompanyAddr[5])
            {
            }
            column(CompanyAddress6; CompanyAddr[6])
            {
            }
            column(CompanyAddress7; CompanyAddr[7])
            {
            }
            column(CompanyAddress8; CompanyAddr[8])
            {
            }
#pragma warning disable AL0432
            column(CompanyHomePage; CompanyInfo."Home Page")
#pragma warning restore AL0432
            {
            }
            column(CompanyEMail; CompanyInfo."E-Mail")
            {
            }
            column(CompanyPicture; CompanyInfo.Picture)
            {
            }
            column(CompanyPhoneNo; CompanyInfo."Phone No.")
            {
            }
            column(CompanyPhoneNo_Lbl; CompanyInfoPhoneNoLbl)
            {
            }
            column(CompanyGiroNo; CompanyInfo."Giro No.")
            {
            }
            column(CompanyGiroNo_Lbl; CompanyInfoGiroNoLbl)
            {
            }
            column(CompanyBankName; CompanyBankAccount.Name)
            {
            }
            column(CompanyBankName_Lbl; CompanyInfoBankNameLbl)
            {
            }
            column(CompanyBankBranchNo; CompanyBankAccount."Bank Branch No.")
            {
            }
            column(CompanyBankBranchNo_Lbl; CompanyBankAccount.FieldCaption("Bank Branch No."))
            {
            }
            column(CompanyBankAccountNo; CompanyBankAccount."Bank Account No.")
            {
            }
            column(CompanyBankAccountNo_Lbl; CompanyInfoBankAccNoLbl)
            {
            }
            column(CompanyIBAN; CompanyBankAccount.IBAN)
            {
            }
            column(CompanyIBAN_Lbl; CompanyBankAccount.FieldCaption(IBAN))
            {
            }
            column(CompanySWIFT; CompanyBankAccount."SWIFT Code")
            {
            }
            column(CompanySWIFT_Lbl; CompanyBankAccount.FieldCaption("SWIFT Code"))
            {
            }
            column(CompanyLogoPosition; CompanyLogoPosition)
            {
            }
            column(CompanyRegistrationNumber; CompanyInfo.GetRegistrationNumber())
            {
            }
            column(CompanyRegistrationNumber_Lbl; CompanyInfo.GetRegistrationNumberLbl())
            {
            }
            column(CompanyVATRegNo; CompanyInfo.GetVATRegistrationNumber())
            {
            }
            column(CompanyVATRegNo_Lbl; CompanyInfo.GetVATRegistrationNumberLbl())
            {
            }
            column(CompanyVATRegistrationNo; CompanyInfo.GetVATRegistrationNumber())
            {
            }
            column(CompanyVATRegistrationNo_Lbl; CompanyInfo.GetVATRegistrationNumberLbl())
            {
            }
            column(CompanyLegalOffice; LegalOfficeTxt)
            {
            }
            column(CompanyLegalOffice_Lbl; LegalOfficeLbl)
            {
            }
            column(CompanyCustomGiro; CustomGiroTxt)
            {
            }
            column(CompanyCustomGiro_Lbl; CustomGiroLbl)
            {
            }
            column(CompanyLegalStatement; LegalStatementLbl)
            {
            }
            column(CustomerAddress1; CustAddr[1])
            {
            }
            column(CustomerAddress2; CustAddr[2])
            {
            }
            column(CustomerAddress3; CustAddr[3])
            {
            }
            column(CustomerAddress4; CustAddr[4])
            {
            }
            column(CustomerAddress5; CustAddr[5])
            {
            }
            column(CustomerAddress6; CustAddr[6])
            {
            }
            column(CustomerAddress7; CustAddr[7])
            {
            }
            column(CustomerAddress8; CustAddr[8])
            {
            }
            column(SellToPhoneNo; SellToPhoneNo)
            {
            }
            column(SellToEmail; SellToEmail)
            {

            }
            column(ShipToPhoneNo; ShipToPhoneNo)
            {
            }
            column(ShipToEmail; ShipToEmail)
            {

            }
            column(SellToContactPhoneNoLbl; SellToContactPhoneNoLbl)
            {
            }
            column(SellToContactMobilePhoneNoLbl; SellToContactMobilePhoneNoLbl)
            {
            }
            column(SellToContactEmailLbl; SellToContactEmailLbl)
            {
            }
            column(BillToContactPhoneNoLbl; BillToContactPhoneNoLbl)
            {
            }
            column(BillToContactMobilePhoneNoLbl; BillToContactMobilePhoneNoLbl)
            {
            }
            column(BillToContactEmailLbl; BillToContactEmailLbl)
            {
            }
            column(SellToContactPhoneNo; SellToContact."Phone No.")
            {
            }
            column(SellToContactMobilePhoneNo; SellToContact."Mobile Phone No.")
            {
            }
            column(SellToContactEmail; SellToContact."E-Mail")
            {
            }
            column(BillToContactPhoneNo; BillToContact."Phone No.")
            {
            }
            column(BillToContactMobilePhoneNo; BillToContact."Mobile Phone No.")
            {
            }
            column(BillToContactEmail; BillToContact."E-Mail")
            {
            }
            column(CustomerPostalBarCode; FormatAddr.PostalBarCode(1))
            {
            }
            column(YourReference; "Your Reference")
            {
            }
            column(YourReference__Lbl; FieldCaption("Your Reference"))
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(ExternalDocumentNo__Lbl; FieldCaption("External Document No."))
            {
            }
            column(ShipmentMethodDescription; ShipmentMethod.Description)
            {
            }
            column(ShipmentMethodDescription_Lbl; ShptMethodDescLbl)
            {
            }
            column(Shipment_Lbl; ShipmentLbl)
            {
            }
            column(ShowShippingAddress; ShowShippingAddr)
            {
            }
            column(ShipToAddress_Lbl; ShiptoAddrLbl)
            {
            }
            column(ShipToAddress1; ShipToAddr[1])
            {
            }
            column(ShipToAddress2; ShipToAddr[2])
            {
            }
            column(ShipToAddress3; ShipToAddr[3])
            {
            }
            column(ShipToAddress4; ShipToAddr[4])
            {
            }
            column(ShipToAddress5; ShipToAddr[5])
            {
            }
            column(ShipToAddress6; ShipToAddr[6])
            {
            }
            column(ShipToAddress7; ShipToAddr[7])
            {
            }
            column(ShipToAddress8; ShipToAddr[8])
            {
            }
            column(PaymentTermsDescription; PaymentTerms.Description)
            {
            }
            column(PaymentTermsDescription_Lbl; PaymentTermsDescLbl)
            {
            }
            column(PaymentMethodDescription; PaymentMethod.Description)
            {
            }
            column(PaymentMethodDescription_Lbl; PaymentMethodDescLbl)
            {
            }
            column(BilltoCustumerNo; "Bill-to Customer No.")
            {
            }
            column(BilltoCustomerNo_Lbl; FieldCaption("Bill-to Customer No."))
            {
            }
            column(PostingDate; Format("Posting Date"))
            {
            }
            column(DocumentDate; Format("Document Date", 0, 4))
            {
            }
            column(DocumentDate_Lbl; FieldCaption("Document Date"))
            {
            }
            column(DueDate; Format("Due Date", 0, 4))
            {
            }
            column(DueDate_Lbl; FieldCaption("Due Date"))
            {
            }
            column(DocumentNo; "No.")
            {
            }
            column(DocumentNo_Lbl; InvoiceNoText)
            {
            }
            column(InvoiceNoPosition_Lbl; NextInvoiceNo)
            {
            }
            column(PricesIncludingVAT; "Prices Including VAT")
            {
            }
            column(PricesIncludingVAT_Lbl; FieldCaption("Prices Including VAT"))
            {
            }
            column(PricesIncludingVATYesNo; Format("Prices Including VAT"))
            {
            }
            column(SalesPerson_Lbl; SalespersonLbl)
            {
            }
            column(SalesPersonBlank_Lbl; SalesPersonText)
            {
            }
            column(SalesPersonName; SalespersonPurchaser.Name)
            {
            }
            column(SelltoCustomerNo; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomerNo_Lbl; FieldCaption("Sell-to Customer No."))
            {
            }
            column(VATRegistrationNo; GetCustomerVATRegistrationNumber())
            {
            }
            column(VATRegistrationNo_Lbl; GetCustomerVATRegistrationNumberLbl())
            {
            }
            //column(GlobalLocationNumber; GetCustomerGlobalLocationNumber()) //marked removal
            //{
            //}
            //column(GlobalLocationNumber_Lbl; GetCustomerGlobalLocationNumberLbl()) //marked removal
            //{
            //}
            column(LegalEntityType; Cust.GetLegalEntityType())
            {
            }
            column(LegalEntityType_Lbl; Cust.GetLegalEntityTypeLbl())
            {
            }
            column(Copy_Lbl; CopyLbl)
            {
            }
            column(EMail_Lbl; EMailLbl)
            {
            }
            column(From_Lbl; FromLbl)
            {
            }
            column(BilledTo_Lbl; BilledToLbl)
            {
            }
            column(ChecksPayable_Lbl; ChecksPayableText)
            {
            }
            column(HomePage_Lbl; HomePageLbl)
            {
            }
            column(InvoiceDiscountBaseAmount_Lbl; InvDiscBaseAmtLbl)
            {
            }
            column(InvoiceDiscountAmount_Lbl; InvDiscountAmtLbl)
            {
            }
            column(LineAmountAfterInvoiceDiscount_Lbl; LineAmtAfterInvDiscLbl)
            {
            }
            column(LocalCurrency_Lbl; LocalCurrencyLbl)
            {
            }
            column(ExchangeRateAsText; ExchangeRateText)
            {
            }
            column(Page_Lbl; PageLbl)
            {
            }
            column(SalesInvoiceLineDiscount_Lbl; SalesInvLineDiscLbl)
            {
            }
            column(Questions_Lbl; QuestionsLbl)
            {
            }
            column(Contact_Lbl; CompanyInfo.GetContactUsText())
            {
            }
            column(DocumentTitle_Lbl; DocumentTitleText)
            {
            }
            column(YourDocumentTitle_Lbl; YourDocumentTitleText)
            {
            }
            column(Thanks_Lbl; ThanksLbl)
            {
            }
            column(ShowWorkDescription; ShowWorkDescription)
            {
            }
            column(Subtotal_Lbl; SubtotalLbl)
            {
            }
            column(Total_Lbl; TotalLbl)
            {
            }
            column(VATAmount_Lbl; VATAmtLbl)
            {
            }
            column(VATBase_Lbl; VATBaseLbl)
            {
            }
            column(VATAmountSpecification_Lbl; VATAmtSpecificationLbl)
            {
            }
            column(VATClauses_Lbl; VATClausesLbl)
            {
            }
            column(VATIdentifier_Lbl; VATIdentifierLbl)
            {
            }
            column(VATPercentage_Lbl; VATPercentageLbl)
            {
            }
            column(VATClause_Lbl; VATClause.TableCaption())
            {
            }
            column(PaymentInstructions_Txt; PaymentInstructionsTxt)
            {
            }
            dataitem("Sales Comment Line"; "Sales Comment Line")
            {
                DataItemLink = "No." = field("No.");
                DataItemLinkReference = Header;
                DataItemTableView = sorting("No.", "Line No.");
                column(Comment_SalesCommentLine; "Sales Comment Line".Comment)
                {
                }
            }
            dataitem(Line; "Sales Line")
            {
                DataItemLink = "Document No." = field("No.");
                DataItemLinkReference = Header;
                DataItemTableView = sorting("Document No.", "Line No.");
                UseTemporary = true;
                column(LineNo_Line; "Line No.")
                {
                }
                column(AmountExcludingVAT_Line; Amount)
                {
                    AutoFormatExpression = "Currency Code";
                    AutoFormatType = 1;
                }
                column(AmountExcludingVAT_Line_Lbl; FieldCaption(Amount))
                {
                }
                column(AmountIncludingVAT_Line; "Amount Including VAT")
                {
                    AutoFormatExpression = "Currency Code";
                    AutoFormatType = 1;
                }
                column(AmountIncludingVAT_Line_Lbl; FieldCaption("Amount Including VAT"))
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(Description_Line; Description)
                {
                }
                column(LineDiscountPercent_Line; "Line Discount %")
                {
                }
                column(LineDiscountPercentText_Line; LineDiscountPctText)
                {
                }
                column(LineAmount_Line; FormattedLineAmount)
                {
                    AutoFormatExpression = "Currency Code";
                    AutoFormatType = 1;
                }
                column(ItemNo_Line; "No.")
                {
                }
                column(CustOrderNo; CustOrderNo)
                {
                }
                column(OurOrderNo; OrderNo)
                {
                }
                column(LineNumberNo; LineNumberNo)
                {
                }
                column(ItemReferenceNo_Line; "Item Reference No.")
                {
                }
                column(ItemReferenceNo_Line_Lbl; FieldCaption("Item Reference No."))
                {
                }
                column(ShipmentDate_Line; Format("Shipment Date"))
                {
                }
                column(ShipmentDate_Lbl; PostedShipmentDateLbl)
                {
                }
                column(Quantity_Line; FormattedQuantity)
                {
                }
                column(Type_Line; Format(Type))
                {
                }
                column(UnitPrice; FormattedUnitPrice)
                {
                    AutoFormatExpression = "Currency Code";
                    AutoFormatType = 2;
                }
                column(UnitOfMeasure; "Unit of Measure")
                {
                }
                column(VATIdentifier_Line; "VAT Identifier")
                {
                }
                column(VATIdentifier_Line_Lbl; FieldCaption("VAT Identifier"))
                {
                }
                column(VATPct_Line; FormattedVATPct)
                {
                }
                column(VATPct_Line_Lbl; FieldCaption("VAT %"))
                {
                }
                column(TransHeaderAmount; TransHeaderAmount)
                {
                    AutoFormatExpression = "Currency Code";
                    AutoFormatType = 1;
                }
                column(Unit_Lbl; UnitLbl)
                {
                }
                column(Qty_Lbl; QtyLbl)
                {
                }
                column(Price_Lbl; PriceLbl)
                {
                }
                column(PricePer_Lbl; PricePerLbl)
                {
                }

                trigger OnAfterGetRecord()
                begin
                    if Type = Type::"G/L Account" then
                        "No." := '';

                    if "Line Discount %" = 0 then
                        LineDiscountPctText := ''
                    else
                        LineDiscountPctText := StrSubstNo('%1%', -Round("Line Discount %", 0.1));

                    OrderNo := '';
                    CustOrderNo := '';
                    SalesShipmentHD.Reset();
                    if (Line."Shipment No." <> '') and (SalesShipmentHD.Get(Line."Shipment No.")) then begin
                        OrderNo := SalesShipmentHD."Order No.";
                        CustOrderNo := SalesShipmentHD."Your Reference";
                    end;
                    if Line.Quantity > 0 then
                        LineNumberNo += 1;

                    TransHeaderAmount += PrevLineAmount;
                    PrevLineAmount := "Line Amount";
                    TotalSubTotal += "Line Amount";
                    TotalInvDiscAmount -= "Inv. Discount Amount";
                    TotalAmount += Amount;
                    TotalAmountVAT += "Amount Including VAT" - Amount;
                    TotalAmountInclVAT += "Amount Including VAT";
                    //message('Amount inc. vat :' + format("Amount Including VAT"));
                    TotalPaymentDiscOnVAT += -("Line Amount" - "Inv. Discount Amount" - "Amount Including VAT");
                    OnLineOnAfterGetRecordOnAfterCalcTotals(Header, Line, TotalAmount, TotalAmountVAT, TotalAmountInclVAT);

                    //if "VAT Clause Code" <> '' then //marked removal
                    //    if VATAmountLine.Get("VAT Identifier", "VAT Calculation Type", "Tax Group Code", false, "Line Amount" >= 0) then begin
                    //        VATAmountLine."VAT Clause Code" := "VAT Clause Code";
                    //        VATAmountLine.Modify(true);
                    //    end;

                    FormatLineValues(Line);

                    if FirstLineHasBeenOutput then
                        Clear(DummyCompanyInfo.Picture);
                    FirstLineHasBeenOutput := true;
                end;

                trigger OnPreDataItem()
                begin
                    MoreLines := Find('+');
                    while MoreLines and (Description = '') and ("No." = '') and (Quantity = 0) and (Amount = 0) do
                        MoreLines := Next(-1) <> 0;
                    if not MoreLines then
                        CurrReport.Break();
                    SetRange("Line No.", 0, "Line No.");
                    TransHeaderAmount := 0;
                    PrevLineAmount := 0;
                    FirstLineHasBeenOutput := false;
                    DummyCompanyInfo.Picture := CompanyInfo.Picture;
                end;
            }
            dataitem(GTIPLoop; Integer)
            {
                DataItemTableView = sorting(Number);
                column(TmpSalesLineGTIP_Tariff_No; TempSalesLineGTIP."Description 2")
                {
                }
                column(TempSalesLineGTIP_Quantity; TempSalesLineGTIP.Quantity)
                {
                }
                column(TempSalesLineGTIP_QtytoShip; TempSalesLineGTIP."Qty. to Ship")
                {
                }
                column(TempSalesLineGTIP_NetWeight; TempSalesLineGTIP."Net Weight")
                {
                }
                column(TempSalesLineGTIP_GrossWeight; TempSalesLineGTIP."Gross Weight")
                {
                }
                column(TempSalesLineGTIP_LineAmount; TempSalesLineGTIP."Line Amount")
                {
                }
                trigger OnPreDataItem()
                begin
                    TempSalesLineGTIP.Reset();
                    if TempSalesLineGTIP.FindSet() then
                        repeat
                            if (farkmiktar_th <> 0) and (toplamKG_th > 0) then begin
                                //MESSAGE('fark : '+FORMAT(farkmiktar_th)+' toplam hesaplanan :'+FORMAT(toplamKG_th)+' tartılan : '+FORMAT(TotalNetWeight));
                                toplammiktar := TempSalesLineGTIP."Net Weight";
                                farkoran_th := toplammiktar / toplamKG_th;
                                hesaplananfark_th := Round(farkmiktar_th * farkoran_th, 0.01);
                                farkdahiltoplam := toplammiktar + hesaplananfark_th;
                                //MESSAGE(TempSalesLineGTIP."Tariff No"+' net weight:'+FORMAT(TempSalesLineGTIP."Net Weight")+' fark dahil:'+FORMAT(farkdahiltoplam));
                                TempSalesLineGTIP."Net Weight" := farkdahiltoplam;
                                TempSalesLineGTIP.Modify(false);
                            end;
                        until TempSalesLineGTIP.Next() = 0;

                    GTIPLoop.SetRange(Number, 1, TempSalesLineGTIP.Count());
                end;

                trigger OnAfterGetRecord()
                begin
                    if GTIPLoop.Number = 1 then
                        TempSalesLineGTIP.FindFirst()
                    else
                        TempSalesLineGTIP.Next();
                end;
            }
            dataitem(WorkDescriptionLines; Integer)
            {
                DataItemTableView = sorting(Number) where(Number = filter(1 .. 99999));
                column(WorkDescriptionLineNumber; Number)
                {
                }
                column(WorkDescriptionLine; WorkDescriptionLine)
                {
                }

                trigger OnAfterGetRecord()
                begin
                    if WorkDescriptionInstream.EOS() then
                        CurrReport.Break();
                    WorkDescriptionInstream.ReadText(WorkDescriptionLine);
                end;

                trigger OnPostDataItem()
                begin
                    Clear(WorkDescriptionInstream)
                end;

                trigger OnPreDataItem()
                begin
                    if not ShowWorkDescription then
                        CurrReport.Break();

                    Header."Work Description".CreateInStream(WorkDescriptionInstream, TextEncoding::UTF8);
                end;
            }
            /*dataitem(VATAmountLine; "VAT Amount Line") //marked removal
            {
                DataItemTableView = sorting("VAT Identifier", "VAT Calculation Type", "Tax Group Code", "Use Tax", Positive);
                UseTemporary = true;
                column(InvoiceDiscountAmount_VATAmountLine; "Invoice Discount Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(InvoiceDiscountAmount_VATAmountLine_Lbl; FieldCaption("Invoice Discount Amount"))
                {
                }
                column(InvoiceDiscountBaseAmount_VATAmountLine; "Inv. Disc. Base Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(InvoiceDiscountBaseAmount_VATAmountLine_Lbl; FieldCaption("Inv. Disc. Base Amount"))
                {
                }
                column(LineAmount_VatAmountLine; "Line Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(LineAmount_VatAmountLine_Lbl; FieldCaption("Line Amount"))
                {
                }
                column(VATAmount_VatAmountLine; "VAT Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(VATAmount_VatAmountLine_Lbl; FieldCaption("VAT Amount"))
                {
                }
                column(VATAmountLCY_VATAmountLine; VATAmountLCY)
                {
                }
                column(VATAmountLCY_VATAmountLine_Lbl; VATAmountLCYLbl)
                {
                }
                column(VATBase_VatAmountLine; "VAT Base")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(VATBase_VatAmountLine_Lbl; FieldCaption("VAT Base"))
                {
                }
                column(VATBaseLCY_VATAmountLine; VATBaseLCY)
                {
                }
                column(VATBaseLCY_VATAmountLine_Lbl; VATBaseLCYLbl)
                {
                }
                column(VATIdentifier_VatAmountLine; "VAT Identifier")
                {
                }
                column(VATIdentifier_VatAmountLine_Lbl; FieldCaption("VAT Identifier"))
                {
                }
                column(VATPct_VatAmountLine; "VAT %")
                {
                    DecimalPlaces = 0 : 5;
                }
                column(VATPct_VatAmountLine_Lbl; FieldCaption("VAT %"))
                {
                }
                column(NoOfVATIdentifiers; Count())
                {
                }

                trigger OnAfterGetRecord()
                begin
                    VATBaseLCY :=
                      GetBaseLCY(
                        Header."Posting Date", Header."Currency Code",
                        Header."Currency Factor");
                    VATAmountLCY :=
                      GetAmountLCY(
                        Header."Posting Date", Header."Currency Code",
                        Header."Currency Factor");

                    TotalVATBaseLCY += VATBaseLCY;
                    TotalVATAmountLCY += VATAmountLCY;

                    if ShowVATClause("VAT Clause Code") then begin
                        VATClauseLine := VATAmountLine;
                        //if VATClauseLine.Insert(true) then;
                        VATClauseLine.Insert(true);
                    end;
                end;

                trigger OnPreDataItem()
                begin
                    Clear(VATBaseLCY);
                    Clear(VATAmountLCY);

                    TotalVATBaseLCY := 0;
                    TotalVATAmountLCY := 0;
                end;
            }
            dataitem(VATClauseLine; "VAT Amount Line") //marked removal
            {
                DataItemTableView = sorting("VAT Identifier", "VAT Calculation Type", "Tax Group Code", "Use Tax", Positive);
                UseTemporary = true;
                column(VATClausesHeader; VATClausesText)
                {
                }
                column(VATIdentifier_VATClauseLine; "VAT Identifier")
                {
                }
                column(Code_VATClauseLine; VATClause.Code)
                {
                }
                column(Code_VATClauseLine_Lbl; VATClause.FieldCaption(Code))
                {
                }
                column(Description_VATClauseLine; VATClauseText)
                {
                }
                column(Description2_VATClauseLine; VATClause."Description 2")
                {
                }
                column(VATAmount_VATClauseLine; "VAT Amount")
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(NoOfVATClauses; Count())
                {
                }

                trigger OnAfterGetRecord()
                begin
                    if "VAT Clause Code" = '' then
                        CurrReport.Skip();
                    if not VATClause.Get("VAT Clause Code") then
                        CurrReport.Skip();
                    VATClauseText := VATClause.GetDescriptionText(Header);
                end;

                trigger OnPreDataItem()
                begin
                    if Count = 0 then
                        VATClausesText := ''
                    else
                        VATClausesText := VATClausesLbl;
                end;
            }
            */
            dataitem(ReportTotalsLine; "Report Totals Buffer")
            {
                DataItemTableView = sorting("Line No.");
                UseTemporary = true;
                column(Description_ReportTotalsLine; Description)
                {
                }
                column(Amount_ReportTotalsLine; Amount)
                {
                }
                column(AmountFormatted_ReportTotalsLine; "Amount Formatted")
                {
                }
                column(FontBold_ReportTotalsLine; "Font Bold")
                {
                }
                column(FontUnderline_ReportTotalsLine; "Font Underline")
                {
                }

                trigger OnPreDataItem()
                begin
                    CreateReportTotalLines();
                end;
            }
            /*dataitem(PaymentReportingArgument; "Payment Reporting Argument")
            {
                DataItemTableView = sorting(Key);
                UseTemporary = true;
                column(PaymentServiceLogo; Logo)
                {
                }
                column(PaymentServiceLogo_UrlText; "URL Caption")
                {
                }
                column(PaymentServiceLogo_Url; GetTargetURL())
                {
                }
                column(PaymentServiceText_UrlText; "URL Caption")
                {
                }
                column(PaymentServiceText_Url; GetTargetURL())
                {
                }
            }
            */
            dataitem(LetterText; Integer)
            {
                DataItemTableView = sorting(Number) where(Number = const(1));
                column(GreetingText; GreetingLbl)
                {
                }
                column(BodyText; BodyContentText)
                {
                }
                column(ClosingText; ClosingLbl)
                {
                }
                column(PmtDiscText; PmtDiscText)
                {
                }

                trigger OnPreDataItem()
                begin
                    PmtDiscText := '';
                    if Header."Payment Discount %" <> 0 then
                        PmtDiscText := StrSubstNo(PmtDiscTxt, Header."Pmt. Discount Date", Header."Payment Discount %");
                end;
            }
            dataitem(Totals; Integer)
            {
                DataItemTableView = sorting(Number) where(Number = const(1));
                column(TotalNetAmount; TotalAmount)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(TotalVATBaseLCY; TotalVATBaseLCY)
                {
                }
                column(TotalAmountIncludingVAT; TotalAmountInclVAT)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(TotalVATAmount; TotalAmountVAT)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(TotalVATAmountLCY; TotalVATAmountLCY)
                {
                }
                column(TotalInvoiceDiscountAmount; TotalInvDiscAmount)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(TotalPaymentDiscountOnVAT; TotalPaymentDiscOnVAT)
                {
                }
                //column(TotalVATAmountText; VATAmountLine.VATAmountText()) //marked removal
                //{
                //}
                column(TotalExcludingVATText; TotalExclVATText)
                {
                }
                column(TotalIncludingVATText; TotalInclVATText)
                {
                }
                column(TotalSubTotal; TotalSubTotal)
                {
                    AutoFormatExpression = Header."Currency Code";
                    AutoFormatType = 1;
                }
                column(TotalSubTotalMinusInvoiceDiscount; TotalSubTotal + TotalInvDiscAmount)
                {
                }
                column(TotalText; TotalText)
                {
                }
                trigger OnAfterGetRecord()
                begin
                    //message('alt toplam : ' + format(TotalAmountInclVAT));
                end;
            }
            trigger OnAfterGetRecord()
            var
                CurrencyExchangeRate: Record "Currency Exchange Rate";
                Country: Record "Country/Region";
                ShipToAddrRec: Record "Ship-to Address";
                //PaymentServiceSetup: Record "Payment Service Setup";
#if not CLEAN22
                ArchiveManagement: Codeunit ArchiveManagement;
#endif
                SalesPost: Codeunit "Sales-Post";
                //PackageNotFoundErr: Label 'Package Not Found';
                paletcode: Code[20];
            begin
                FirstLineHasBeenOutput := false;
                Clear(Line);
                //VATAmountLine.DeleteAll(true); //marked removal
                //VATClauseLine.DeleteAll(true); //marked removal
                Line.DeleteAll(false);
                Clear(SalesPost);
                SalesPost.GetSalesLines(Header, Line, 0);
                OnAfterSalesPostGetSalesLines(Header, Line);

                //Line.CalcVATAmountLines(0, Header, Line, VATAmountLine); //marked removal
                //Line.UpdateVATOnLines(0, Header, Line, VATAmountLine); //marked removal
                //OnHeaderOnAfterGetRecordOnAfterUpdateVATOnLines(Header, Line, VATAmountLine); //marked removal

                CurrReport.Language := Languagex.GetLanguageIdOrDefault("Language Code");
                CurrReport.FormatRegion := Languagex.GetFormatRegionOrDefault("Format Region");
                FormatAddr.SetLanguageCode("Language Code");
                SupplierNumber := '';
                if Cust.Get("Sell-to Customer No.") then
                    SupplierNumber := Cust."Supplier Number FLX";

                DateText := GetDateText(Header."Posting Date");

                CalcFields("Work Description");
                ShowWorkDescription := "Work Description".HasValue();

                FormatAddr.GetCompanyAddr("Responsibility Center", RespCenter, CompanyInfo, CompanyAddr);
                FormatAddr.SalesHeaderBillTo(CustAddr, Header);
                ShowShippingAddr := FormatAddr.SalesHeaderShipTo(ShipToAddr, CustAddr, Header);
                if not Country.Get(Cust."Country/Region Code") then
                    Country.Init();
                SellToPhoneNo := Cust."Phone No.";
                SellToEmail := Cust."E-Mail";
                CustAddr[1] := Cust.Name;
                CustAddr[2] := Cust."Name 2";
                CustAddr[3] := Cust.Address;
                CustAddr[4] := Cust."Address 2";
                CustAddr[5] := CopyStr(Cust.City + ' ' + Cust.County + ' ' + Country.Name + ' ' + Cust."Post Code", 1, 100);
                CompressArray(CustAddr);
                if not Country.Get("Ship-to Country/Region Code") then
                    Country.Init();
                ShipToPhoneNo := "Ship-to Phone No.";
                ShipToEmail := '';
                if ("Ship-to Code" <> '') and (ShipToAddrRec.Get("Sell-to Customer No.", "Ship-to Code")) then begin
                    ShipToEmail := ShipToAddrRec."E-Mail";
                    ShipToPhoneNo := ShipToAddrRec."Phone No.";
                end;
                ShipToAddr[1] := "Ship-to Name";
                ShipToAddr[2] := "Ship-to Name 2";
                ShipToAddr[3] := "Ship-to Address";
                ShipToAddr[4] := "Ship-to Address 2";
                ShipToAddr[5] := CopyStr("Ship-to City" + ' ' + "Ship-to County" + ' ' + Country.Name + ' ' + "Ship-to Post Code", 1, 100);
                CompressArray(ShipToAddr);

                DocumentTitleText := SalesConfirmationLbl;
                YourDocumentTitleText := StrSubstNo(YourDocLbl, SalesConfirmationLbl);
                InvoiceNoText := InvNoLbl;
                BodyContentText := BodyLbl;
                ChecksPayableText := StrSubstNo(ChecksPayableLbl, CompanyInfo.Name);

                if not CompanyBankAccount.Get(Header."Company Bank Account Code") then
                    CompanyBankAccount.CopyBankFieldsFromCompanyInfo(CompanyInfo);

                if not Cust.Get("Bill-to Customer No.") then
                    Clear(Cust);

                if "Currency Code" <> '' then begin
                    CurrencyExchangeRate.FindCurrency("Posting Date", "Currency Code", 1);
                    CalculatedExchRate :=
                      Round(1 / "Currency Factor" * CurrencyExchangeRate."Exchange Rate Amount", 0.000001);
                    ExchangeRateText := StrSubstNo(ExchangeRateTxt, CalculatedExchRate, CurrencyExchangeRate."Exchange Rate Amount");
                end;

                //PaymentServiceSetup.CreateReportingArgs(PaymentReportingArgument, Header);

                FormatDocumentFields(Header);
                if not SellToContact.Get("Sell-to Contact No.") then
                    SellToContact.Init();
                if not BillToContact.Get("Bill-to Contact No.") then
                    BillToContact.Init();

#if not CLEAN22
                if not IsReportInPreviewMode() and ArchiveDocument then
                    ArchiveManagement.StoreSalesDocument(Header, false);
#endif

                TotalSubTotal := 0;
                TotalInvDiscAmount := 0;
                TotalAmount := 0;
                TotalAmountVAT := 0;
                TotalAmountInclVAT := 0;

                //NAV17 den gelen
                GLSetup.Get();
                if Header."Currency Code" = '' then
                    CurrencyCode := GLSetup."LCY Code"
                else
                    CurrencyCode := Header."Currency Code";

                ContainerNumber := '';
                plasticwooddesc := WoodenPalletsLbl;
                if plasticpallet then
                    plasticwooddesc := PlasticPalletsLbl;

                lcSalesLine.Reset();
                lcSalesLine.SetRange("Document Type", Header."Document Type");
                lcSalesLine.SetRange("Document No.", Header."No.");
                lcSalesLine.SetFilter("Piece FLX", '>0');
                if lcSalesLine.FindSet() then
                    repeat
                        if Item.Get(lcSalesLine."No.") then //begin
                            if (Item."Tariff No." <> '') then begin
                                if StrPos(CustomTariffNumber, Item."Tariff No.") = 0 then begin
                                    TempSalesLineGTIP.Reset();
                                    TempSalesLineGTIP := lcSalesLine;
                                    TempSalesLineGTIP.Quantity := Round(lcSalesLine.Quantity * lcSalesLine."Qty. per Unit of Measure", 0.01);
                                    TempSalesLineGTIP."Net Weight" := GetWeight(lcSalesLine);
                                    TempSalesLineGTIP."Description 2" := Item."Tariff No.";
                                    TempSalesLineGTIP.Insert(false);
                                    if CustomTariffNumber <> '' then
                                        CustomTariffNumber += ', ';
                                    CustomTariffNumber += Item."Tariff No.";
                                end else begin
                                    TempSalesLineGTIP.Reset();
                                    //TempSalesLineGTIP.SetRange("Tariff No", lcSalesLine."Tariff No");
                                    TempSalesLineGTIP.SetRange("Description 2", Item."Tariff No.");
                                    if TempSalesLineGTIP.FindFirst() then begin
                                        TempSalesLineGTIP.Quantity += Round(lcSalesLine.Quantity * lcSalesLine."Qty. per Unit of Measure", 0.01);
                                        TempSalesLineGTIP."Net Weight" += GetWeight(lcSalesLine);
                                        TempSalesLineGTIP."Line Amount" += lcSalesLine."Line Amount";
                                        TempSalesLineGTIP.Modify(false);
                                    end;
                                end;
                                TempSalesLineGTIP."Qty. to Ship" := Round(TempSalesLineGTIP.Quantity / 0.305, 0.01);
                                TempSalesLineGTIP.Modify(false);
                            end;
                    //end;
                    until lcSalesLine.Next() = 0;
                CountryOfOrigin := 'TR';

                //EntryNo := 0;
                LineNumberNo := 0;
                PackageDesc := '';
                BulkPieces := 0;
                PalletQty := 0;
                TotalTareWeight := 0;
                TotalGrossWeight := 0;
                TotalNetWeight := 0;
                //TotalVolume := 0;
                pallettotalmetre := 0;
                pallettotalfeet := 0;
                farkmiktar_th := 0;
                paletcode := '';
                CombinedShipmentHeader.Reset();
                CombinedShipmentHeader.SetRange("Sales Invoice No.", Header."No.");
                if CombinedShipmentHeader.FindLast() then begin
                    ContainerNumber := CombinedShipmentHeader."Container No.";
                    CombinedShipmentHeader.CalcFields("Calcualted Shipment Weight");
                    if CombinedShipmentHeader."Scaled Shipment Weight" > 0 then
                        farkmiktar_th := CombinedShipmentHeader."Scaled Shipment Weight" - CombinedShipmentHeader."Calcualted Shipment Weight";

                    CombineShipPackDetail.Reset();
                    CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeader."No.");
                    if CombineShipPackDetail.FindFirst() then
                        CombineShipPackDetail.DeleteAll(true);
                    CombineShipLineDetail.Reset();
                    CombineShipLineDetail.SetCurrentKey("Parent Package No.");
                    CombineShipLineDetail.SetRange("Document No.", CombinedShipmentHeader."No.");
                    if CombineShipLineDetail.FindFirst() then
                        repeat
                            CombineShipLineDetail.CalcFields("Parent Package Type");
                            if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) or
                               (CombineShipLineDetail."Parent Package No." = '') then begin
                                TareDesc := '';
                                TareWeight := 0;
                                BulkPieces += 1;
                            end else begin
                                CombineShipPackDetail.Reset();
                                CombineShipPackDetail.SetRange("Combined Shipment No.", CombinedShipmentHeader."No.");
                                CombineShipPackDetail.SetRange("Transfer-from Package No.", CombineShipLineDetail."Parent Package No.");
                                if not CombineShipPackDetail.FindFirst() then begin
                                    PalletQty += 1;
                                    PackageTransferOut.Reset();
                                    PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                                    if PackageTransferOut.FindFirst() then
                                        if PackageTransferOut."Palette Item No. FLX" <> '' then
                                            if Item.Get(PackageTransferOut."Palette Item No. FLX") then begin
                                                TareDesc := Item.Description;
                                                TareWeight := Item."Net Weight";
                                                TotalTareWeight += Item."Net Weight";
                                                TotalGrossWeight += Item."Net Weight";
                                                paletcode := Item."No.";
                                            end;
                                end;
                            end;

                            /*if CombineShipLineDetail."Item No." <> '' then
                                if Item.Get(CombineShipLineDetail."Item No.") then begin
                                    TareDesc := Item.Description;
                                    TareWeight := Item."Net Weight";
                                    TotalTareWeight += Item."Net Weight";
                                    TotalGrossWeight += Item."Net Weight";
                                end;
                            */
                            Package.Reset();
                            Package.SetRange(Package."Package No.", CombineShipLineDetail."Package No.");
                            Package.SetRange(Package."Combined Shipment No. FLX", CombinedShipmentHeader."No.");
                            Package.FindFirst();
                            //FLEX-230 BEGIN
                            OrderNo := Package."Sales Order No. FLX";
                            CustOrderNo := Package."Your Reference FLX";
                            if CombineShipLineDetail."Parent Package No." <> '' then begin
                                PackageTransferOut.Reset();
                                PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                                if PackageTransferOut.FindFirst() then begin
                                    if PackageTransferOut."Sales Order No. FLX" <> '' then
                                        OrderNo := PackageTransferOut."Sales Order No. FLX";
                                    if PackageTransferOut."Your Reference FLX" <> '' then
                                        CustOrderNo := PackageTransferOut."Your Reference FLX";
                                end;
                            end;
                            //FLEX-230 END
                            EntryNo += 1;
                            CombineShipPackDetail.Init();
                            CombineShipPackDetail."Combined Shipment No." := CombinedShipmentHeader."No.";
                            CombineShipPackDetail."Package Type" := CombineShipLineDetail."Parent Package Type";
                            CombineShipPackDetail."Package No." := CombineShipLineDetail."Package No.";
                            CombineShipPackDetail."Transfer-from Package No." := CombineShipLineDetail."Parent Package No.";
                            CombineShipPackDetail."Source Document No." := OrderNo;//FLEX-230 added line
                            CombineShipPackDetail."Your Reference" := CustOrderNo; //FLEX-230 added line
                            CombineShipPackDetail."Entry No" := EntryNo;
                            CombineShipPackDetail.Type := CombineShipPackDetail.Type::Item;
                            CombineShipPackDetail."No." := CombineShipLineDetail."Item No.";
                            CombineShipPackDetail.Description := CombineShipLineDetail."Item Description";
                            CombineShipPackDetail.Quantity := CombineShipLineDetail.Quantity;
                            CombineShipPackDetail."Coil Lenght" := CombineShipLineDetail.Quantity;
                            CombineShipPackDetail."Hose Length" := CombineShipLineDetail.Quantity;
                            //CombineShipPackDetail."Serial No." := CombineShipLineDetail."Serial No.";
                            CombineShipPackDetail."Lot No." := CombineShipLineDetail."Lot No.";
                            //LotNoInf.Reset();
                            //LotNoInf.SetRange("Item No.", CombineShipLineDetail."Item No.");
                            //LotNoInf.SetRange("Lot No.", CombineShipLineDetail."Lot No.");
                            //if LotNoInf.FindFirst() then
                            //CombineShipPackDetail."Coil Weight" := ROUND(LotNoInf."Weight per Qty." * CombineShipPackDetail.Quantity, 0.01);
                            CombineShipPackDetail."Coil Weight" := CombineShipLineDetail."Package Weight (KG)";
                            //CombineShipPackDetail."Expiration Date" := PackageContentTreeView."Expiration Date";
                            //CombineShipPackDetail."Pallet Code" := Package."Package Item No.";
                            CombineShipPackDetail.Piece := 1;
                            if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or
                               (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) or
                               (CombineShipLineDetail."Parent Package No." = '') then
                                CombineShipPackDetail."Pallet Line Number" := 'BULK'
                            else begin
                                CombineShipPackDetail."Pallet Line Number" := Format(PalletQty);
                                if StrLen(CombineShipPackDetail."Pallet Line Number") = 1 then
                                    CombineShipPackDetail."Pallet Line Number" := CopyStr('0' + CombineShipPackDetail."Pallet Line Number", 1, 100);
                                CombineShipPackDetail."Pallet Line Number" += CopyStr('-' + CombineShipLineDetail."Parent Package No.", 1, 100);
                                CombineShipPackDetail."Pallet Description" := TareDesc;
                                CombineShipPackDetail."Tare Weight" := TareWeight;
                                CombineShipPackDetail."Pallet Code" := paletcode;//CombineShipLineDetail."Item No.";
                            end;
                            CombineShipPackDetail.Insert(true);
                            pallettotalmetre += CombineShipPackDetail.Quantity;
                            pallettotalfeet += Round(CombineShipPackDetail.Quantity / 0.305, 0.01);
                            TotalGrossWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                            TotalNetWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                        //if Item.Get(CombineShipPackDetail."No.") then
                        //    TotalVolume += Round(Item."Unit Volume" * CombineShipPackDetail.Piece, 0.01);
                        until CombineShipLineDetail.Next() = 0;

                    //CalcTotalNetKG := TotalNetWeight;
                    if farkmiktar_th <> 0 then begin
                        TotalNetWeight := CombinedShipmentHeader."Scaled Shipment Weight";
                        TotalGrossWeight := CombinedShipmentHeader."Scaled Shipment Weight" + TotalTareWeight;
                    end;

                    TotalPackage := Format(PalletQty + BulkPieces) + ' PACKAGES';
                    if PalletQty > 0 then
                        PackageDesc := '( ' + Format(PalletQty) + ' PALLETS';
                    if BulkPieces > 0 then begin
                        if PackageDesc = '' then
                            PackageDesc += '( '
                        else
                            PackageDesc += ' / ';
                        PackageDesc += Format(BulkPieces) + ' BULK )';
                    end else
                        PackageDesc += ' )';

                    GrandPallettotaltxt := 'MT ' + Format(pallettotalmetre) + ' (FT ' + Format(pallettotalfeet) + ')';
                end;
            end;

            trigger OnPreDataItem()
            begin
                FirstLineHasBeenOutput := false;
            end;
        }
    }

    requestpage
    {
        SaveValues = true;

        layout
        {
            area(Content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field(LogInteractionField; LogInteraction)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Log Interaction';
                        Enabled = LogInteractionEnable;
                        ToolTip = 'Specifies that interactions with the contact are logged.';
                    }
#if not CLEAN22
                    field(ArchiveDocumentx; ArchiveDocument)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Archive Document';
                        ToolTip = 'Specifies if the document is archived after you print it. Note: This option is going to be discontinued in future releases. Instead, for sales invoice versioning you can use the feature to attach draft invoice printout as PDF document.';
                        ObsoleteReason = 'Archiving of Sales Invoice document is not supported.';
                        ObsoleteState = Pending;
                        ObsoleteTag = '22.0';
                    }
#endif
                }
            }
        }

        actions
        {
        }

        trigger OnInit()
        begin
            LogInteractionEnable := true;
#if not CLEAN22
            ArchiveDocument := SalesSetup."Archive Orders";
#endif
        end;

        trigger OnOpenPage()
        begin
            InitLogInteraction();
            LogInteractionEnable := LogInteraction;
        end;
    }

    labels
    {
    }

    trigger OnInitReport()
    var
        //SalesHeader: Record "Sales Header";
        IsHandled: Boolean;
    begin
        GLSetup.Get();
        CompanyInfo.SetAutoCalcFields(Picture);
        CompanyInfo.Get();
        CompanyInfo.CalcFields(Picture);
        SalesSetup.Get();
        CompanyInfo.VerifyAndSetPaymentInfo();

        if SalesHeader.GetLegalStatement() <> '' then
            LegalStatementLbl := SalesHeader.GetLegalStatement();

        IsHandled := false;
        OnInitReportForGlobalVariable(IsHandled, LegalOfficeTxt, LegalOfficeLbl, CustomGiroTxt, CustomGiroLbl, LegalStatementLbl);
#if not CLEAN23
        //if not IsHandled then begin
        //LegalOfficeTxt := CompanyInfo.GetLegalOffice();
        //LegalOfficeLbl := CompanyInfo.GetLegalOfficeLbl();
        //CustomGiroTxt := CompanyInfo.GetCustomGiro();
        //CustomGiroLbl := CompanyInfo.GetCustomGiroLbl();
        //end;
#endif
    end;

    trigger OnPreReport()
    begin
        if Header.GetFilters() = '' then
            Error(NoFilterSetErr);

        if not CurrReport.UseRequestPage() then
            InitLogInteraction();

        CompanyLogoPosition := SalesSetup."Logo Position on Documents";
    end;

    trigger OnPostReport()
    begin
        if LogInteraction and not IsReportInPreviewMode() then begin
            Header.SetLoadFields("No.", "Bill-to Contact No.", "Bill-to Customer No.", "Salesperson Code", "Campaign No.", "Posting Description", "Opportunity No.");
            if Header.FindSet() then
                repeat
                    if Header."Bill-to Contact No." <> '' then
                        SegManagement.LogDocument(
                          26, Header."No.", 0, 0,
                          Database::Contact, Header."Bill-to Contact No.",
                          Header."Salesperson Code", Header."Campaign No.", Header."Posting Description", Header."Opportunity No.")
                    else
                        SegManagement.LogDocument(
                          26, Header."No.", 0, 0,
                          Database::Customer, Header."Bill-to Customer No.",
                          Header."Salesperson Code", Header."Campaign No.", Header."Posting Description", Header."Opportunity No.");
                until Header.Next() = 0;
        end;
    end;

    var
        CombinedShipmentHeader: Record "Combined Shipment Header FLX";
        DummyCompanyInfo: Record "Company Information";
        VATClause: Record "VAT Clause";
        CombineShipLineDetail: Record "CombinedShipmentLineDtl FLX";
        CombineShipPackDetail: Record "Combined Ship Package Det. FLX";
        SalesShipmentHD: Record "Sales Shipment Header";
        SalesHeader: Record "Sales Header";
        lcSalesLine: Record "Sales Line";
        TempSalesLineGTIP: Record "Sales Line" temporary;
        //TempSalesLine: Record "Sales Line" temporary;
        Package: Record "Package No. Information";
        PackageTransferOut: Record "Package No. Information";
        Item: Record Item;
        //LotNoInf: Record "Lot No. Information";
        Languagex: Codeunit Language;
        FormatAddr: Codeunit "Format Address";
        FormatDocument: Codeunit "Format Document";
        SegManagement: Codeunit SegManagement;
        WorkDescriptionInstream: InStream;
        WorkDescriptionLine: Text;
        CustAddr: array[8] of Text[100];
        SellToPhoneNo: Text[100];
        SellToEmail: Text[100];
        ShipToPhoneNo: Text[100];
        ShipToEmail: Text[100];
        ChecksPayableText: Text;
        ShipToAddr: array[8] of Text[100];
        CompanyAddr: array[8] of Text[100];
        SalesPersonText: Text[50];
#if not CLEAN22
        ArchiveDocument: Boolean;
#endif
        LogInteractionEnable: Boolean;
        CompanyLogoPosition: Integer;
        CalculatedExchRate: Decimal;
        ExchangeRateText: Text;
        //VATBaseLCY: Decimal; //marked removal
        //VATAmountLCY: Decimal; //marked removal
        TotalVATBaseLCY: Decimal;
        TotalVATAmountLCY: Decimal;
        PrevLineAmount: Decimal;
        PmtDiscText: Text;
        PaymentInstructionsTxt: Text;
        YourDocumentTitleText: Text;
        DocumentTitleText: Text;
        InvoiceNoText: Text;
        //VATClausesText: Text; //marked removal
        BodyContentText: Text;
        NextInvoiceNo: Text;
        SalesConfirmationLbl: Label 'Draft Invoice';
        YourDocLbl: Label 'Your %1', Comment = 'Your Draft Invoice or Your Invoice %1';
        SalespersonLbl: Label 'Sales person';
        CompanyInfoBankAccNoLbl: Label 'Account No.';
        CompanyInfoBankNameLbl: Label 'Bank';
        CompanyInfoGiroNoLbl: Label 'Giro No.';
        CompanyInfoPhoneNoLbl: Label 'Phone No.';
        CopyLbl: Label 'Copy';
        EMailLbl: Label 'Email';
        HomePageLbl: Label 'Home Page';
        InvDiscBaseAmtLbl: Label 'Invoice Discount Base Amount';
        InvDiscountAmtLbl: Label 'Invoice Discount';
        InvNoLbl: Label 'Draft Invoice No.';
        LineAmtAfterInvDiscLbl: Label 'Payment Discount on VAT';
        LocalCurrencyLbl: Label 'Local Currency';
        PageLbl: Label 'Page';
        PaymentTermsDescLbl: Label 'Payment Terms';
        PaymentMethodDescLbl: Label 'Payment Method';
        PostedShipmentDateLbl: Label 'Shipment Date';
        SalesInvLineDiscLbl: Label 'Discount %';
        ShipmentLbl: Label 'Shipment';
        ShiptoAddrLbl: Label 'Ship-to Address';
        ShptMethodDescLbl: Label 'Shipment Method';
        SubtotalLbl: Label 'Subtotal';
        TotalLbl: Label 'Total';
        VATAmtSpecificationLbl: Label 'VAT Amount Specification';
        VATAmtLbl: Label 'VAT Amount';
        //VATAmountLCYLbl: Label 'VAT Amount (LCY)'; //marked removal
        VATBaseLbl: Label 'VAT Base';
        //VATBaseLCYLbl: Label 'VAT Base (LCY)'; //marked removal
        VATClausesLbl: Label 'VAT Clause';
        VATIdentifierLbl: Label 'VAT Identifier';
        VATPercentageLbl: Label 'VAT %';
        SellToContactPhoneNoLbl: Label 'Sell-to Contact Phone No.';
        SellToContactMobilePhoneNoLbl: Label 'Sell-to Contact Mobile Phone No.';
        SellToContactEmailLbl: Label 'Sell-to Contact E-Mail';
        BillToContactPhoneNoLbl: Label 'Bill-to Contact Phone No.';
        BillToContactMobilePhoneNoLbl: Label 'Bill-to Contact Mobile Phone No.';
        BillToContactEmailLbl: Label 'Bill-to Contact E-Mail';
        ExchangeRateTxt: Label 'Exchange rate: %1/%2', Comment = '%1 and %2 are both amounts.';
        NoFilterSetErr: Label 'You must specify one or more filters to avoid accidently printing all documents.';
        FromLbl: Label 'From';
        BilledToLbl: Label 'Billed to';
        ChecksPayableLbl: Label 'Please make checks payable to %1', Comment = '%1 = company name';
        QuestionsLbl: Label 'Questions?';
        ThanksLbl: Label 'Thank You!';
        GreetingLbl: Label 'Hello';
        ClosingLbl: Label 'Sincerely';
        PmtDiscTxt: Label 'If we receive the payment before %1, you are eligible for a %2% payment discount.', Comment = '%1 = Discount Due Date %2 = value of Payment Discount % ';
        BodyLbl: Label 'Thank you for your business. Your draft invoice is attached to this message.';
        UnitLbl: Label 'Unit';
        QtyLbl: Label 'Qty', Comment = 'Short form of Quantity';
        PriceLbl: Label 'Price';
        PricePerLbl: Label 'Price per';

        //LCYTxt: Label ' (LCY)'; //marked removal
        //VATClauseText: Text; //marked removal
        CustomGiroLbl, CustomGiroTxt, LegalOfficeLbl, LegalOfficeTxt, LegalStatementLbl : Text;
        //NAV17 var
        WoodenPalletsLbl: Label 'WOODEN PALLETS ACCORDING TO ISPM-15 STANDARD NORM / HEAT TREATED';
        PlasticPalletsLbl: Label 'Goods loaded on plastic pallets';
        PackagingLbl: Label 'Packaging :';
        GrossWeightLbl: Label 'Gross Weight :';
        TareWeightLbl: Label 'Tare Weight :';
        NetWeightLbl: Label 'Net Weight :';
        ContainerNumberLbl: Label 'Container Number :';
        CountryOfOriginLbl: Label 'Country Of Origin :';
        TotalChargesLbl: Label 'Total Charges';
        NetGoodsLbl: Label 'Net Goods';
        TotalAmountLbl: Label 'Total Amount';
        ItemNo_Line_Lbl: Label 'Article No';
        Description_Line_Lbl: Label 'Article Description';
        Quantity_Line_Lbl: Label 'Qty.';
        UnitOfMeasure_Lbl: Label 'U.M';
        UnitPrice_Lbl: Label 'Unit Price';
        LineAmount_Line_Lbl: Label 'Net Amount';
        LineNoLbl: Label 'No';
        CustPoLbl: Label 'Cust. PO.';
        OurPOLbl: Label 'Our PO.';

        LineNumberNo: Integer;
        CustomTariffNumber: Text[250];
        CountryOfOrigin: Text[250];
        CurrencyCode: Code[10];
        TotalGrossWeight: Decimal;
        TotalTareWeight: Decimal;
        TotalNetWeight: Decimal;
        //TotalVolume: Decimal;
        PackageDesc: Text[1024];
        BulkPieces: Decimal;
        PalletQty: Decimal;
        TotalPackage: Text[50];
        //PalletLineNo: Code[20];
        //PalletDesc: Text[50];
        //PalletTare: Decimal;
        EntryNo: Integer;
        TareWeight: Decimal;
        TareDesc: Text[100];
        CustOrderNo: Text[50];
        OrderNo: Text[50];
        //numtext: Text[250];
        //palletnetweight: Decimal;
        //palletgrossweight: Decimal;
        //palletmetretxt: Text[1024];
        //palletfeettxt: Text[1024];
        //pallettotaltxt: Text[1024];
        pallettotalmetre: Decimal;
        pallettotalfeet: Decimal;
        //metrevaluetxt: Text[1024];
        //feetvaluetxt: Text[1024];
        //ekle: Boolean;
        //ShowNotShip: Boolean;
        farkmiktar_th: Decimal;
        farkoran_th: Decimal;
        //farksonuc_th: Decimal;
        //CalcTotalNetKG: Decimal;
        //GrandPallettotalmetre: Decimal;
        //GrandPallettotalfeet: Decimal;
        GrandPallettotaltxt: Text[1024];
        //GrossWeight: Decimal;
        //NetWeight: Decimal;
        plasticpallet: Boolean;
        plasticwooddesc: Text[250];
        ContainerNumber: Text[50];
        toplamKG_th: Decimal;
        toplammiktar: Decimal;
        hesaplananfark_th: Decimal;
        farkdahiltoplam: Decimal;

    protected var
        CompanyBankAccount: Record "Bank Account";
        CompanyInfo: Record "Company Information";
        BillToContact: Record Contact;
        SellToContact: Record Contact;
        Cust: Record Customer;
        GLSetup: Record "General Ledger Setup";
        PaymentMethod: Record "Payment Method";
        PaymentTerms: Record "Payment Terms";
        RespCenter: Record "Responsibility Center";
        SalesSetup: Record "Sales & Receivables Setup";
        SalespersonPurchaser: Record "Salesperson/Purchaser";
        ShipmentMethod: Record "Shipment Method";
        FirstLineHasBeenOutput: Boolean;
        LogInteraction: Boolean;
        MoreLines: Boolean;
        ShowShippingAddr: Boolean;
        ShowWorkDescription: Boolean;
        TotalAmount: Decimal;
        TotalAmountInclVAT: Decimal;
        TotalAmountVAT: Decimal;
        TotalInvDiscAmount: Decimal;
        TotalPaymentDiscOnVAT: Decimal;
        TotalSubTotal: Decimal;
        TransHeaderAmount: Decimal;
        FormattedLineAmount: Text;
        FormattedQuantity: Text;
        FormattedUnitPrice: Text;
        FormattedVATPct: Text;
        LineDiscountPctText: Text;
        DateText: Text[30];
        TotalExclVATText: Text[50];
        TotalInclVATText: Text[50];
        TotalText: Text[50];
        SupplierNumber: Text[100];

    local procedure InitLogInteraction()
    begin
        LogInteraction := SegManagement.FindInteractionTemplateCode(Enum::"Interaction Log Entry Document Type"::"Sales Draft Invoice") <> '';
    end;

    local procedure GetDateText(ComingDate: Date) result: Text[30]
    var
        month: Integer;
        monthtext: Text[3];
    begin
        result := Format(Date2DMY(ComingDate, 1)) + '-';
        month := Date2DMY(ComingDate, 2);
        case month of
            1:
                monthtext := 'Jan';
            2:
                monthtext := 'Feb';
            3:
                monthtext := 'Mar';
            4:
                monthtext := 'Apr';
            5:
                monthtext := 'May';
            6:
                monthtext := 'Jun';
            7:
                monthtext := 'Jul';
            8:
                monthtext := 'Aug';
            9:
                monthtext := 'Sep';
            10:
                monthtext := 'Oct';
            11:
                monthtext := 'Nov';
            12:
                monthtext := 'Dec';
        end;
        result += monthtext + '-' + Format(Date2DMY(ComingDate, 3));
    end;

    local procedure FormatDocumentFields(var lcSalesHeader: Record "Sales Header")
    begin
        //with SalesHeader do begin
        FormatDocument.SetTotalLabels(lcSalesHeader.GetCurrencySymbol(), TotalText, TotalInclVATText, TotalExclVATText);
        FormatDocument.SetSalesPerson(SalespersonPurchaser, lcSalesHeader."Salesperson Code", SalesPersonText);
        FormatDocument.SetPaymentTerms(PaymentTerms, lcSalesHeader."Payment Terms Code", lcSalesHeader."Language Code");
        FormatDocument.SetPaymentMethod(PaymentMethod, lcSalesHeader."Payment Method Code", lcSalesHeader."Language Code");
        FormatDocument.SetShipmentMethod(ShipmentMethod, lcSalesHeader."Shipment Method Code", lcSalesHeader."Language Code");
        //end;
    end;

    protected procedure IsReportInPreviewMode(): Boolean
    var
        MailManagement: Codeunit "Mail Management";
    begin
        exit(CurrReport.Preview() or MailManagement.IsHandlingGetEmailBody());
    end;

    local procedure GetWeight(var wSalesLine: Record "Sales Line"): Decimal
    var
        lcItem: Record Item;
        HamProdBomLine: Record "Production BOM Line";
        ProductionBOMLine: Record "Production BOM Line";
        VersionMgt: Codeunit VersionManagement;
        ActiveVersionCode: Code[20];
        Gtip: Code[20];
        hamprodbomno: Code[20];
        prodbomno: Code[20];
        //firemiktar: Decimal;
        lctoplammiktar: Decimal;
        toplamkg: Decimal;
    begin

        toplamkg := 0;
        Gtip := '';
        prodbomno := '';
        if lcItem.Get(wSalesLine."No.") then begin
            Gtip := lcItem."Tariff No.";
            prodbomno := lcItem."Production BOM No.";
        end;
        if Gtip = '' then
            Gtip := wSalesLine."No.";
        ActiveVersionCode := VersionMgt.GetBOMVersion(prodbomno, WorkDate(), true);
        ProductionBOMLine.Reset();
        ProductionBOMLine.SetRange("Production BOM No.", prodbomno);
        ProductionBOMLine.SetRange("Version Code", ActiveVersionCode);
        ProductionBOMLine.SetRange("Unit of Measure Code", 'KG');
        if ProductionBOMLine.FindSet() then
            repeat
                hamprodbomno := '';
                if lcItem.Get(ProductionBOMLine."No.") then
                    hamprodbomno := lcItem."Production BOM No.";

                if hamprodbomno <> '' then begin
                    lctoplammiktar := Round(ProductionBOMLine."Quantity per" * wSalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                    //firemiktar := Round(lctoplammiktar * ProductionBOMLine."Scrap %" / 100, 0.01);
                    ActiveVersionCode := VersionMgt.GetBOMVersion(hamprodbomno, WorkDate(), true);
                    HamProdBomLine.Reset();
                    HamProdBomLine.SetRange("Production BOM No.", hamprodbomno);
                    HamProdBomLine.SetRange("Version Code", ActiveVersionCode);
                    HamProdBomLine.SetRange("Unit of Measure Code", 'KG');
                    if HamProdBomLine.FindSet() then
                        repeat
                            toplamkg += Round(HamProdBomLine."Quantity per" * lctoplammiktar, 0.01);
                        until HamProdBomLine.Next() = 0;
                end else //begin
                    toplamkg += Round(ProductionBOMLine."Quantity per" * wSalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                                                                                                              //end;
            until ProductionBOMLine.Next() = 0;
        toplamKG_th += toplamkg;
        exit(toplamkg);
    end;

    local procedure CreateReportTotalLines()
    begin
        ReportTotalsLine.DeleteAll(false);
        if (TotalInvDiscAmount <> 0) or (TotalAmountVAT <> 0) then
            ReportTotalsLine.Add(SubtotalLbl, TotalSubTotal, true, false, false);
        if TotalInvDiscAmount <> 0 then begin
            ReportTotalsLine.Add(InvDiscountAmtLbl, TotalInvDiscAmount, false, false, false);
            if TotalAmountVAT <> 0 then
                ReportTotalsLine.Add(TotalExclVATText, TotalAmount, true, false, false);
        end;
        //if TotalAmountVAT <> 0 then begin //marked removal
        //    ReportTotalsLine.Add(VATAmountLine.VATAmountText(), TotalAmountVAT, false, true, false);
        //    if TotalVATAmountLCY <> TotalAmountVAT then
        //        ReportTotalsLine.Add(VATAmountLine.VATAmountText() + LCYTxt, TotalVATAmountLCY, false, true, false);
        //end;
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterSalesPostGetSalesLines(var SalesHeader: Record "Sales Header"; var SalesLine: Record "Sales Line")
    begin
    end;

    //local procedure ShowVATClause(VATClauseCode: Code[20]): Boolean //marked removal
    //begin
    //    if VATClauseCode = '' then
    //        exit(false);
    //    exit(true);
    //end;

    local procedure FormatLineValues(CurrLine: Record "Sales Line")
    var
        IsHandled: Boolean;
    begin
        IsHandled := false;
        OnBeforeFormatLineValues(CurrLine, FormattedQuantity, FormattedUnitPrice, FormattedVATPct, FormattedLineAmount, IsHandled);
        if not IsHandled then
            FormatDocument.SetSalesLine(CurrLine, FormattedQuantity, FormattedUnitPrice, FormattedVATPct, FormattedLineAmount);
    end;

    //[IntegrationEvent(false, false)] //marked removal
    //local procedure OnHeaderOnAfterGetRecordOnAfterUpdateVATOnLines(var SalesHeader: Record "Sales Header"; var SalesLine: Record "Sales Line"; var VATAmountLine: Record "VAT Amount Line")
    //begin
    //end;

    [IntegrationEvent(false, false)]
    local procedure OnLineOnAfterGetRecordOnAfterCalcTotals(var SalesHeader: Record "Sales Header"; var SalesLine: Record "Sales Line"; var VATBaseAmount: Decimal; var VATAmount: Decimal; var TotalAmountInclVAT: Decimal)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeFormatLineValues(SalesLine: Record "Sales Line"; var FormattedQuantity: Text; var FormattedUnitPrice: Text; var FormattedVATPercentage: Text; var FormattedLineAmount: Text; var IsHandled: Boolean)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnInitReportForGlobalVariable(var IsHandled: Boolean; var LegalOfficeTxt: Text; var LegalOfficeLbl: Text; var CustomGiroTxt: Text; var CustomGiroLbl: Text; var LegalStatementLbl: Text)
    begin
    end;
}

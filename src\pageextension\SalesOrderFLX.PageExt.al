pageextension 60023 "Sales Order FLX" extends "Sales Order"
{
    layout
    {
        addafter(Status)
        {
            field("Note FLX"; Rec."Note FLX")
            {
                ApplicationArea = All;
                MultiLine = true;
            }
        }
    }
    actions
    {
        addafter("&Print")
        {
            action("OrderConfirmFlex FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Order Confirm Flexati';
                Image = Print;
                ToolTip = 'Order Confirm Flexati.';

                trigger OnAction()
                var
                    SalesHd: Record "Sales Header";
                begin
                    SalesHd.SetRange("No.", Rec."No.");
                    Report.RunModal(Report::"Sales Order Confirm FLX", true, false, SalesHd);
                end;
            }
        }
    }
}
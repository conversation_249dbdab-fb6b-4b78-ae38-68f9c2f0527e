pageextension 60004 "Lot No. Information List FLX" extends "Lot No. Information List"
{
    layout
    {
        modify("Item No.")
        {
            Visible = true;
        }
        addlast(Control1)
        {
            field("Your Reference FLX"; Rec."Your Reference FLX")
            {
                ApplicationArea = All;
            }
            field("Sell-to Customer No. FLX"; Rec."Sell-to Customer No. FLX")
            {
                ApplicationArea = All;
            }
            field("Sell-to Customer Name FLX"; Rec."Sell-to Customer Name FLX")
            {
                ApplicationArea = All;
            }
            field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
            {
                ApplicationArea = All;
            }
            field("Sales Order No. FLX"; Rec."Sales Order No. FLX")
            {
                ApplicationArea = All;
            }
            field("Sales Order Line No. FLX"; Rec."Sales Order Line No. FLX")
            {
                ApplicationArea = All;
            }
            field("Production Order No. FLX"; Rec."Production Order No. FLX")
            {
                ApplicationArea = All;
            }
            field("Hose Lenght FLX"; Rec."Hose Lenght FLX")
            {
                ApplicationArea = All;
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.SetAscending(SystemCreatedAt, false);
    end;
}
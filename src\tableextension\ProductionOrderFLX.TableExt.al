tableextension 60018 "Production Order FLX" extends "Production Order"
{
    fields
    {
        field(60000; "Note FLX"; Text[500])
        {
            Caption = 'Note';
            ToolTip = 'Specifies the value of the Note field.';
        }
        field(60001; "Source Sales Order No. FLX"; Code[20])
        {
            Caption = 'Source Sales Order No.';
            AllowInCustomizations = Always;
        }
        field(60002; "Source Sls.Order Line No. FLX"; Integer)
        {
            Caption = 'Source Sales Order Line No.';
            AllowInCustomizations = Always;
        }
        field(60003; "Task Status FLX"; Enum "Task Status FLX")
        {
            Caption = 'Task Status';
            AllowInCustomizations = Always;
        }
        field(60004; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            AllowInCustomizations = Always;
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Sell-to Customer No. FLX"));
            ToolTip = 'Specifies the value of the Ship-to Code field.';
            trigger OnValidate()
            var
                LotNoInformation: Record "Lot No. Information";
                PackageNoInformation: Record "Package No. Information";
                SalesHeader: Record "Sales Header";
            begin
                SalesHeader.Get(SalesHeader."Document Type"::Order, Rec."Source Sales Order No. FLX");
                SalesHeader.Validate("Ship-to Code", Rec."Ship-to Code FLX");
                SalesHeader.Modify(true);

                LotNoInformation.SetRange("Sales Order No. FLX", Rec."Source Sales Order No. FLX");
                LotNoInformation.ModifyAll("Ship-to Code FLX", Rec."Ship-to Code FLX", true);

                PackageNoInformation.SetRange("Sales Order No. FLX", Rec."Source Sales Order No. FLX");
                PackageNoInformation.ModifyAll("Ship-to Code FLX", Rec."Ship-to Code FLX", true);
            end;
        }
        field(60005; "Sell-to Customer No. FLX"; Code[20])
        {
            Caption = 'Sell-to Customer No.';
            TableRelation = Customer."No.";
            AllowInCustomizations = Always;
        }
    }
}

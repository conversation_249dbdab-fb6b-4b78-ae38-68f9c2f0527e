page 60000 "Prod. Order Lines FLX"
{
    ApplicationArea = All;
    Caption = 'Prod. Order Lines';
    PageType = List;
    SourceTable = "Prod. Order Line";
    SourceTableView = where("Blocked FLX" = const(false), Status = filter(<> 'Finished'));
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Prod. Order - Line No. FLX"; Rec."Prod. Order - Line No. FLX")
                {
                }
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies a value that is copied from the corresponding field on the production order header.';
                }
                field("Prod. Order No."; Rec."Prod. Order No.")
                {
                    ToolTip = 'Specifies the number of the related production order.';
                }
                field("Line No."; Rec."Line No.")
                {
                    ToolTip = 'Specifies the value of the Line No. field.';
                }
                field("Source Line No. FLX"; Rec."Source Line No. FLX")
                {
                    ToolTip = 'Specifies the value of the Source Line No. field.';
                }
                field("Customer No. FLX"; Rec."Customer No. FLX")
                {
                }
                field("Customer Name FLX"; Rec."Customer Name FLX")
                {
                }
                field("Routing No."; Rec."Routing No.")
                {
                    ToolTip = 'Specifies the routing number for the production order line.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the item that is to be produced.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the value of the Description field on the item card. If you enter a variant code, the variant description is copied to this field instead.';
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the quantity to be produced if you manually fill in this line.';
                }
                field("Finished Quantity"; Rec."Finished Quantity")
                {
                    ToolTip = 'Specifies how much of the quantity on this line has been produced.';
                }
                field("Remaining Quantity"; Rec."Remaining Quantity")
                {
                    ToolTip = 'Specifies the difference between the finished and planned quantities, or zero if the finished quantity is greater than the remaining quantity.';
                }
                field("Lot No. FLX"; Rec."Lot No. FLX")
                {
                }
                field("Piece FLX"; Rec."Piece FLX")
                {
                    ToolTip = 'Specifies the value of the Piece field.';
                }
                field("Hose Length FLX"; Rec."Hose Length FLX")
                {
                    ToolTip = 'Specifies the value of the Hose Lenght field.';
                }
                field("Flexati Shipment Date FLX"; Rec."Flexati Shipment Date FLX")
                {
                    ToolTip = 'Specifies the value of the Flexati Shipment Date field.';
                }
                field("Your Reference FLX"; Rec."Your Reference FLX")
                {
                    ToolTip = 'Specifies the value of the Your Reference field.';
                }
                field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
                {
                    ToolTip = 'Specifies the value of the Ship-to Code field.';
                }
                field("Stop FLX"; Rec."Stop FLX")
                {
                    ToolTip = 'Specifies the value of the Stop field.';
                }
                field("Line Note FLX"; Rec."Line Note FLX")
                {
                    ToolTip = 'Specifies the value of the Line Note field.';
                }
                field("Note FLX"; Rec."Note FLX")
                {
                    ToolTip = 'Specifies the value of the Note field.';
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(ProductionPlanningWorksheet)
            {
                ApplicationArea = Manufacturing;
                Caption = 'Production Planning Worksheet';
                Image = PlanningWorksheet;
                ToolTip = 'View or edit the operations list of the parent item on the line.';
                Promoted = true;
                PromotedIsBig = true;
                PromotedOnly = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    FlexatiProductionMngt.OpenProductionPlanningWorksheetFromProdOrderLine(Rec);
                end;
            }
        }
    }
    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
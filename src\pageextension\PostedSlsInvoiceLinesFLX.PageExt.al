pageextension 60008 "Posted Sls Invoice Lines FLX" extends "Posted Sales Invoice Lines"
{
    layout
    {
        addafter("Sell-to Customer No.")
        {
            field("Hose Lenght FLX"; Rec."Hose Length FLX")
            {
                ApplicationArea = All;
            }
            field("Unit Weight (Base) FLX"; Rec."Unit Weight (Base) FLX")
            {
                ApplicationArea = All;
            }
            field("Piece FLX"; Rec."Piece FLX")
            {
                ApplicationArea = All;
            }
            field("Production Order No. FLX"; Rec."Production Order No. FLX")
            {
                ApplicationArea = All;
            }
            // field("Production Order Status FLX"; Rec."Production Order Status FLX")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Production Order Status field.';
            // }
            field("Sell-to Customer Name FLX"; Rec."Sell-to Customer Name FLX")
            {
                ApplicationArea = All;
            }
            field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
            {
                ApplicationArea = All;
            }
            field("Your Reference FLX"; Rec."Your Reference FLX")
            {
                ApplicationArea = All;
            }
            field("Posting Date FLX"; Rec."Posting Date")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Posting Date field.';
            }
            field("Flexati Shipment Date FLX"; Rec."Flexati Shipment Date FLX")
            {
                ApplicationArea = All;
                //ToolTip = 'Specifies the value of the Flexati Shipment Date FLX field.';
            }
            field("Shipment Line No. FLX"; Rec."Shipment Line No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Shipment Line No. field.';
            }
            field("Sales Order Quantity FLX"; Rec."Sales Order Quantity FLX")
            {
                ApplicationArea = All;
                //ToolTip = 'Specifies the value of the Sales Order Quantity field.';
            }
        }
        addafter("Unit Price")
        {
            field("Unit Cost (LCY) FLX"; Rec."Unit Cost (LCY)")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Unit Cost (LCY) field.';
            }
            field("Amount FLX"; Rec.Amount)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Amount field.';
            }
            field("Order No. FLX"; Rec."Order No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Order No. field.';
            }
            field("Unit Cost FLX"; Rec."Unit Cost")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Unit Cost field.';
            }
            field("Units per Parcel FLX"; Rec."Units per Parcel")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Units per Parcel field.';
            }
            field("Quantity (Base) FLX"; Rec."Quantity (Base)")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Quantity (Base) field.';
            }
            field("Currency Code FLX"; Rec."Currency Code FLX")
            {
                ApplicationArea = All;
            }
            field("Currency Factor FLX"; Rec."Currency Factor FLX")
            {
                ApplicationArea = All;
            }
            field("Shipment No. FLX"; Rec."Shipment No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Shipment No field.';
            }
            field("Inv. Discount Amount FLX"; Rec."Inv. Discount Amount")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Inv. Discount Amount field.';
            }
            field("Net Weight FLX"; Rec."Net Weight")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Net Weight field.';
            }
        }
    }
}
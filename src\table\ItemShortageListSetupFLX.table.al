table 60017 "Item Shortage List Setup FLX"
{
    Caption = 'Item Shortage List Setup';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "PR Code"; Code[10])
        {
            Caption = 'PR Code';
            NotBlank = false;
            ToolTip = 'Specifies the value of the PR Code field.';
        }
        field(2; "Reject Location"; Code[10])
        {
            Caption = 'Reject Location';
            TableRelation = Location;
            ToolTip = 'Specifies the value of the Reject Location field.';
        }
        field(3; "UHD Location"; Code[10])
        {
            Caption = 'UHD Location';
            TableRelation = Location;
            ToolTip = 'Specifies the value of the UHD Location field.';
        }
        field(4; "WHSE Location"; Code[10])
        {
            Caption = 'WHSE Location';
            TableRelation = Location;
            ToolTip = 'Specifies the value of the WHSE Location field.';
        }
    }
    keys
    {
        key(PK; "PR Code")
        {
            Clustered = true;
        }
    }
}

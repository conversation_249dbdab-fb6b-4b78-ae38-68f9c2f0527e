table 60019 "Banbury Production FLX"
{
    Caption = 'Banbury Production';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; PK; Code[10])
        {
            Caption = 'PK';
            NotBlank = false;
            AllowInCustomizations = Never;
        }

        field(2; "Production Order No."; Code[20])
        {
            Caption = 'Production Order No.';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Production Order No. field.';
            TableRelation = "Production Order"."No." where(Status = const(Released));
            trigger OnValidate()
            var
                ProductionOrder: Record "Production Order";
                ProdOrderLine: Record "Prod. Order Line";
            begin
                ProductionOrder.Get(ProductionOrder.Status::Released, Rec."Production Order No.");
                Rec.Validate("Item No.", ProductionOrder."Source No.");

                ProdOrderLine.SetRange(Status, ProdOrderLine.Status::Released);
                ProdOrderLine.SetRange("Prod. Order No.", Rec."Production Order No.");
                ProdOrderLine.FindFirst();

                Rec.Validate("Routing No.", ProdOrderLine."Routing No.");
            end;
        }
        field(3; "User ID"; Code[10])
        {
            Caption = 'User ID';
            TableRelation = "Item Journal Batch".Name;
            ToolTip = 'Specifies the value of the User ID field.';
        }
        field(4; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
            Editable = false;
        }
        field(6; "Routing No."; Code[20])
        {
            Caption = 'Routing No.';
            TableRelation = "Routing Header"."No.";
            ToolTip = 'Specifies the value of the Routing No. field.';
            Editable = false;
        }


    }
    keys
    {
        key(PK; PK)
        {
            Clustered = true;
        }
    }
}
page 60020 "Item Shortage List Filters FLX"
{
    ApplicationArea = All;
    Caption = 'Item Shortage List Filters';
    PageType = ListPlus;
    //SourceTable = Integer;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            //Group()
            //{
            field(ProductionForecastName; ProductionForecastName)
            {
                Caption = 'Production Forecast Name';
                ToolTip = 'Specifies the value of the Production Forecast Name field.';
                trigger OnValidate()
                begin
                    SetMatrix();
                end;
            }
            field(LocationFilter; LocationFilter)
            {
                Caption = 'Location Filter';
                ToolTip = 'Specifies the value of the Location Filter field.';
                trigger OnValidate()
                var
                    Location: Record Location;
                begin
                    Location.SetFilter(Code, LocationFilter);
                    LocationFilter := CopyStr(Location.GetFilter(Code), 1, 100);
                    SetMatrix();
                end;

                trigger OnLookup(var Text: Text): Boolean
                var
                    Loc: Record Location;
                    LocList: Page "Location List";
                begin
                    LocList.LookupMode(true);
                    Loc.SetRange("Use As In-Transit", false);
                    LocList.SetTableView(Loc);
                    if not (LocList.RunModal() = Action::LookupOK) then
                        exit(false);

                    Text := LocList.GetSelectionFilter();

                    exit(true);
                end;
            }
            field(PeriodType; PeriodType)
            {
                Caption = 'Period Type';
                ToolTip = 'Specifies the value of the Period Type field.';
                trigger OnValidate()
                var
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    SetWantedg := SetWantedg::First;
                    SetColumns(SetWantedg);
                end;
            }
            field(QtyType; QtyType)
            {
                Caption = 'Qty. Type';
                ToolTip = 'Specifies the value of the Qty Type field.';
                trigger OnValidate()
                begin
                    QtyTypeOnAfterValidate();
                end;
            }
            field(ForecastType; ForecastType)
            {
                Caption = 'Forecast Type';
                ToolTip = 'Specifies the value of the Total Forecast Type field.';
                trigger OnValidate()
                begin
                    ForecastTypeOnAfterValidate();
                end;
            }
            field(DateFilter; DateFilter)
            {
                Caption = 'Date Filter';
                ToolTip = 'Specifies the value of the Date Filter field.';
                trigger OnValidate()
                var
                    //ApplicationManagement: Codeunit "ApplicationManagement Flexati";
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    //IF ApplicationManagement.MakeDateFilter(DateFilter) = 0 THEN;
                    SetWantedg := SetWantedg::First;
                    SetColumns(SetWantedg);
                end;
            }
            field(ItemQtyFilter; ItemQtyFilter)
            {
                Caption = 'Item Qty. Filter';
                ToolTip = 'Specifies the value of the Item Qty. Filter field.';
                trigger OnValidate()
                var
                    //ApplicationManagement: Codeunit "ApplicationManagement Flexati";
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    //IF ApplicationManagement.MakeDateFilter(DateFilter) = 0 THEN;
                    SetWantedg := SetWantedg::First;
                    SetColumns(SetWantedg);
                end;
            }
            field(ShowOnlyNegativeQty; ShowOnlyNegativeQty)
            {
                Caption = 'Show Only Negative Qty.';
                ToolTip = 'Specifies the value of the Show Only Negative Qty field.';
                trigger OnValidate()
                var
                    //ApplicationManagement: Codeunit "ApplicationManagement Flexati";
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    //IF ApplicationManagement.MakeDateFilter(DateFilter) = 0 THEN;
                    SetWantedg := SetWantedg::First;
                    SetColumns(SetWantedg);
                    CurrPage.Update();
                end;
            }
            //}
            // part(Matrix; "Item Shortage List FLX")
            // {
            //     Caption = 'Matrix';
            //     Editable = false;
            //     //UpdatePropagation = Both;
            //     UpdatePropagation = SubPart; //NAV17 böyle ayarlanmış
            // }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CalculatePlan)
            {
                ApplicationArea = All;
                Caption = 'Calculate Plan';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Planning;
                PromotedOnly = true;
                ToolTip = 'Executes the Calculate Plan action.';

                trigger OnAction()
                var
                    //ProdOrder: Record "Production Order";
                    //ShortItemList: Record "Shortage Item List";
                    //pPlanCalculate: Page "Shortage List Filter Popupbase";
                    ShortageListFilters: Page "Shortage List Filter Popupbase";
                    SetWantedg: Enum "SetWanted FLX";
                //ApplicationManagement: Codeunit "ApplicationManagement Flexati";
                begin
                    /*
                    CLEAR(pPlanCalculate);
                                        IF pPlanCalculate.RUNMODAL = ACTION::LookupOK THEN
                                            CurrPage.UPDATE;
                                        CurrPage.UPDATE;
                    */

                    ShortageListFilters.RunModal();
                    //BRST.006.13 BEGIN
                    /*IF ShortageListFilters.GetSourceItemNo = '' THEN
                    BEGIN
                      IF TODAY > ShortageListFilters.GetEndDate THEN
                        DateFilter:=FORMAT(ShortageListFilters.GetEndDate-1)+'..'+FORMAT(ShortageListFilters.GetEndDate)
                      ELSE
                        DateFilter:=FORMAT(TODAY)+'..'+FORMAT(ShortageListFilters.GetEndDate);
                    END ELSE
                    BEGIN
                       ProdOrder.RESET;
                       ProdOrder.SETFILTER(ProdOrder.Status,'<>%1',ProdOrder.Status::Finished);
                       ProdOrder.SETRANGE(ProdOrder."Source Type",ProdOrder."Source Type"::Item);
                       ProdOrder.SETFILTER(ProdOrder."Source No.",ShortageListFilters.GetSourceItemNo);
                       IF ProdOrder.FINDLAST THEN
                       BEGIN
                          IF TODAY > ProdOrder."Due Date" THEN
                             DateFilter:=FORMAT(ProdOrder."Due Date"-1)+'..'+FORMAT(ProdOrder."Due Date")
                          ELSE
                             DateFilter:=FORMAT(TODAY)+'..'+FORMAT(ProdOrder."Due Date");
                       END;
                    END;
                    IF ApplicationManagement.MakeDateFilter(DateFilter) = 0 THEN;
                    */
                    PeriodType := PeriodType::Month;
                    SetWantedg := SetWantedg::First;
                    SetColumns(SetWantedg);
                    //BRST.006.13 END
                    CurrPage.Update();
                end;
            }
            action(PreviousSet)
            {
                ApplicationArea = All;
                Caption = 'Previous Set';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PreviousSet;
                PromotedOnly = true;
                ToolTip = 'Executes the Previous Set action.';

                trigger OnAction()
                var
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    SetWantedg := SetWantedg::Previous;
                    SetColumns(SetWantedg);
                    CurrPage.Update();
                end;
            }
            action(PreviousColumn)
            {
                ApplicationArea = All;
                Caption = 'Previous Column';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PreviousRecord;
                PromotedOnly = true;
                ToolTip = 'Executes the Previous Column action.';

                trigger OnAction()
                var
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    SetWantedg := SetWantedg::"Previous Column";
                    SetColumns(SetWantedg);
                    CurrPage.Update();
                end;
            }
            action(NextColumn)
            {
                ApplicationArea = All;
                Caption = 'Next Column';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NextRecord;
                PromotedOnly = true;
                ToolTip = 'Executes the Next Column action.';

                trigger OnAction()
                var
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    SetWantedg := SetWantedg::"Next Column";
                    SetColumns(SetWantedg);
                    CurrPage.Update();
                end;
            }
            action(NextSet)
            {
                ApplicationArea = All;
                Caption = 'Next Set';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NextSet;
                PromotedOnly = true;
                ToolTip = 'Executes the Next Set action.';

                trigger OnAction()
                var
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    SetWantedg := SetWantedg::Next;
                    SetColumns(SetWantedg);
                    CurrPage.Update();
                end;
            }
            action(RefreshPage)
            {
                ApplicationArea = All;
                Caption = 'Refresh Page 1';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = RefreshPlanningLine;
                PromotedOnly = true;
                ToolTip = 'Executes the Refresh Page action.';

                trigger OnAction()
                var
                    SetWantedg: Enum "SetWanted FLX";
                begin
                    PeriodType := PeriodType::Month;
                    SetWantedg := SetWantedg::First;
                    SetColumns(SetWantedg);
                    CurrPage.Update();
                end;
            }
        }
    }
    trigger OnOpenPage()
    var
        SetWantedg: Enum "SetWanted FLX";
    begin
        //Rec.SetRange(Number, 1);
        if (NewProductionForecastName <> '') and (NewProductionForecastName <> ProductionForecastName) then
            ProductionForecastName := NewProductionForecastName;
        PeriodType := PeriodType::Month;
        SetWantedg := SetWantedg::First;
        ForecastType := ForecastType::Both;
        SetColumns(SetWantedg);
    end;

    procedure SetColumns(var SetWanted: Enum "SetWanted FLX")
    var
        MatrixMgt: Codeunit "Matrix Management";
        OptValue: Option;
    begin
        OptValue := SetWanted.AsInteger() - 1;
        MatrixMgt.GeneratePeriodMatrixData(OptValue, ArrayLen(MatrixRecords), false, PeriodType, DateFilter, PKFirstRecInCurrSet,
          MatrixColumnCaptions, ColumnSet, CurrSetLength, MatrixRecords);
        SetMatrix();
    end;

    procedure SetProductionForecastName(NextProductionForecastName: Text[30])
    begin
        NewProductionForecastName := NextProductionForecastName;
    end;

    procedure SetMatrix()
    var
        RealConsuptDateFind: Record Date;
        ed: Date;
        ccno: Integer;
        year: Integer;
    begin
        ed := Today() + 1000;
        //MatrixRecords[4]."Period End" := 12312199D;
        MatrixRecords[4]."Period End" := ed;
        for ccno := 1 to 4 do begin
            if ccno = 4 then
                MatrixColumnCaptions[ccno] += ' Ve Sonrası';
            DemandColumnCaptions[ccno] := CopyStr(MatrixColumnCaptions[ccno] + ' İhtiyaçlar', 1, 50);
            PurchColumnCaptions[ccno] := CopyStr(MatrixColumnCaptions[ccno] + ' Satınalmalar', 1, 50);
            MatrixColumnCaptions[ccno] += ' Net Stok Miktar';
        end;
        // CurrPage.Matrix.Page.Load(
        //   MatrixColumnCaptions, MatrixRecords, ProductionForecastName, DateFilter, LocationFilter, ForecastType,
        //   //QtyType,CurrSetLength,ItemQtyFilter,ShowOnlyNegativeQty); //BRST.006.13 changed line , BRST.006.17 changed line
        //   QtyType, CurrSetLength, ItemQtyFilter); //BRST.006.13 changed line , BRST.006.17 changed line
        // CurrPage.Matrix.Page.DemandLoad(
        //   DemandColumnCaptions, MatrixRecords, ProductionForecastName, DateFilter, LocationFilter, ForecastType,
        //   //QtyType,CurrSetLength,ItemQtyFilter,ShowOnlyNegativeQty); //BRST.006.13 changed line , BRST.006.17 changed line
        //   QtyType, CurrSetLength, ItemQtyFilter); //BRST.006.13 changed line , BRST.006.17 changed line
        // CurrPage.Matrix.Page.PurchaseLoad(
        //   PurchColumnCaptions, MatrixRecords, ProductionForecastName, DateFilter, LocationFilter, ForecastType,
        //   //QtyType,CurrSetLength,ItemQtyFilter,ShowOnlyNegativeQty); //BRST.006.13 changed line , BRST.006.17 changed line
        //   QtyType, CurrSetLength, ItemQtyFilter); //BRST.006.13 changed line , BRST.006.17 changed line

        ccno := 4;
        year := Date2DMY(MatrixRecords[1]."Period Start", 3);
        /*RealConsuptDateFind.Reset();
        RealConsuptDateFind.SetRange("Period Type", RealConsuptDateFind."Period Type"::Month);
        RealConsuptDateFind.SetRange("Period Start", DMY2Date(1, 1, year - 1), MatrixRecords[1]."Period Start");
        if RealConsuptDateFind.FindLast() then
            repeat
                ccno -= 1;
                year := Date2DMY(RealConsuptDateFind."Period Start", 3);
                RealConsumpColumnCaptions[ccno] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Tüketimler';
                if ccno < 3 then begin //FLEX-240 added line
                    DemandColumnCaptionsBefore[ccno + 1] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Ve Öncesi İhtiyaçlar'; //FLEX-240 added line
                    PurchColumnCaptionsBefore[ccno + 1] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Ve Öncesi Satınalmalar';//FLEX-240 added line
                    DemandPurchBeforeDate[ccno + 1] := RealConsuptDateFind; //FLEX-240 added line
                end;
                RealConsuptDate[ccno] := RealConsuptDateFind;
            until (RealConsuptDateFind.Next(-1) = 0) or (ccno = 1);
            */
        RealConsuptDateFind.Reset();
        RealConsuptDateFind.SetCurrentKey("Period Type", "Period Start");
        RealConsuptDateFind.SetAscending("Period Start", false);
        RealConsuptDateFind.SetRange("Period Type", RealConsuptDateFind."Period Type"::Month);
        RealConsuptDateFind.SetRange("Period Start", DMY2Date(1, 1, year - 1), MatrixRecords[1]."Period Start");
        if RealConsuptDateFind.FindSet() then
            repeat
                ccno -= 1;
                year := Date2DMY(RealConsuptDateFind."Period Start", 3);
                RealConsumpColumnCaptions[ccno] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Tüketimler';
                if ccno < 3 then begin //FLEX-240 added line
                    DemandColumnCaptionsBefore[ccno + 1] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Ve Öncesi İhtiyaçlar'; //FLEX-240 added line
                    PurchColumnCaptionsBefore[ccno + 1] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Ve Öncesi Satınalmalar';//FLEX-240 added line
                    DemandPurchBeforeDate[ccno + 1] := RealConsuptDateFind; //FLEX-240 added line
                end;
                RealConsuptDate[ccno] := RealConsuptDateFind;
            until (RealConsuptDateFind.Next() = 0) or (ccno = 1);
        DemandColumnCaptionsBefore[1] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Ve Öncesi İhtiyaçlar'; //FLEX-240 added line
        PurchColumnCaptionsBefore[1] := CopyStr(RealConsuptDateFind."Period Name", 1, 3) + ' ' + Format(year) + ' Ve Öncesi Satınalmalar';//FLEX-240 added line
        DemandPurchBeforeDate[1] := RealConsuptDateFind; //FLEX-240 added line

        // CurrPage.Matrix.Page.RealConsumpLoad(
        //   RealConsumpColumnCaptions, RealConsuptDate, ProductionForecastName, DateFilter, LocationFilter, ForecastType,
        //   QtyType, 3, ItemQtyFilter);

        // //FLEX-240 BEGIN
        // CurrPage.Matrix.Page.DemandBeforeLoad(
        //   DemandColumnCaptionsBefore, DemandPurchBeforeDate, ProductionForecastName, DateFilter, LocationFilter, ForecastType,
        //   QtyType, 3, ItemQtyFilter);
        // CurrPage.Matrix.Page.PurchaseBeforeLoad(
        //   PurchColumnCaptionsBefore, DemandPurchBeforeDate, ProductionForecastName, DateFilter, LocationFilter, ForecastType,
        //   QtyType, 3, ItemQtyFilter);
        //FLEX-240 END
        CurrPage.Update(false);
    end;

    local procedure ForecastTypeOnAfterValidate()
    begin
        SetMatrix();
    end;

    local procedure QtyTypeOnAfterValidate()
    begin
        SetMatrix();
    end;

    var
        DemandPurchBeforeDate: array[3] of Record Date;
        MatrixRecords: array[4] of Record Date;
        RealConsuptDate: array[3] of Record Date;
        //recDate: Record Date;
        ShowOnlyNegativeQty: Boolean;
        ItemQtyFilter: Decimal;
        ForecastType: Enum "ForecastType FLX";
        PeriodType: Enum "Period Type FLX";
        QtyType: Enum "QtyType FLX";
        CurrSetLength: Integer;
        ColumnSet: Text;
        PKFirstRecInCurrSet: Text;
        NewProductionForecastName: Text[30];
        ProductionForecastName: Text[30];
        DemandColumnCaptions: array[4] of Text[50];
        DemandColumnCaptionsBefore: array[3] of Text[50];
        PurchColumnCaptions: array[4] of Text[50];
        PurchColumnCaptionsBefore: array[3] of Text[50];
        RealConsumpColumnCaptions: array[3] of Text[50];
        DateFilter: Text[1024];
        LocationFilter: Text[1024];
        MatrixColumnCaptions: array[4] of Text[1024];
}
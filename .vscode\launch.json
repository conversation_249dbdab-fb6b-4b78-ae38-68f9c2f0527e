{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Microsoft cloud sandbox",
            "request": "launch",
            "type": "al",
            "environmentType": "Sandbox",
            //"environmentName": "Erkan",
            "environmentName": "Flexati_Test",
            "tenant": "flexati.onmicrosoft.com",
            "startupObjectId": 9305,
            "startupObjectType": "Page",
            "breakOnError": "ExcludeTry",
            "launchBrowser": false,
            "enableLongRunningSqlStatements": true,
            "enableSqlInformationDebugger": true,
            //"schemaUpdateMode": "ForceSync"
        }
    ]
}
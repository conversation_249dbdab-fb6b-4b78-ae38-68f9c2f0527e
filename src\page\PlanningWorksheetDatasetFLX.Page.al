page 60032 "Planning Worksheet Dataset FLX"
{
    ApplicationArea = All;
    Caption = 'Planning Worksheet Dataset';
    PageType = List;
    SourceTable = "Prod. Order Line";
    UsageCategory = ReportsAndAnalysis;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Sales Order - Line No. FLX"; Rec."Sales Order - Line No. FLX")
                {
                }
                field("Prod. Order - Line No. FLX"; Rec."Prod. Order - Line No. FLX")
                {
                }
                field("Customer No. FLX"; Rec."Customer No. FLX")
                {
                }
                field("Customer Name FLX"; Rec."Customer Name FLX")
                {
                }
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the value of the Status field.';
                }
                field("Prod. Order No."; Rec."Prod. Order No.")
                {
                    ToolTip = 'Specifies the value of the Prod. Order No. field.';
                }
                field("Line No."; Rec."Line No.")
                {
                    ToolTip = 'Specifies the value of the Line No. field.';
                }
                field("Your Reference FLX"; Rec."Your Reference FLX")
                {
                }
                field("Source Line No. FLX"; Rec."Source Line No. FLX")
                {
                }
                field("Ship-to Code FlowField FLX"; Rec."Ship-to Code FlowField FLX")
                {
                }
                field("Routing No."; Rec."Routing No.")
                {
                    ToolTip = 'Specifies the routing number for the production order line.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the value of the Item No. field.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the value of the Description field.';
                }
                /*field(Brand; FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(Rec."Item No.", 'LL'))
                {
                    ToolTip = 'Specifies the value of the Brand field.';
                }
                field("Brand Description"; FlexatiProductionMngt.GetBomItemNoDesc_ByItemCategoryCode(Rec."Item No.", 'LL'))
                {
                    ToolTip = 'Specifies the value of the Brand Description field.';
                }
                field(Cloth; FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(Rec."Item No.", 'TR'))
                {
                    ToolTip = 'Specifies the value of the Cloth field.';
                }
                field(Paste; FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(Rec."Item No.", 'RC'))
                {
                    ToolTip = 'Specifies the value of the Paste field.';
                }
                field(Wire; FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(Rec."Item No.", 'WR'))
                {
                    ToolTip = 'Specifies the value of the Wire field.';
                }
                */
                field(Brand; Rec."Brand FLX")
                {
                    Editable = false;
                }
                field("Brand Description"; Rec."Brand Description FLX")
                {
                    Editable = false;
                }
                field(Cloth; Rec."Cloth FLX")
                {
                    Editable = false;
                }
                field(Paste; Rec."Paste FLX")
                {
                    Editable = false;
                }
                field(Wire; Rec."Wire FLX")
                {
                    Editable = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the value of the Quantity field.';
                }
                field("Finished Quantity"; Rec."Finished Quantity")
                {
                    ToolTip = 'Specifies the value of the Finished Quantity field.';
                }
                field(PositiveFinishedQuantity; FlexatiProductionMngt.CalculatePositiveFinishedQtyFromProdOrderLine(Rec))
                {
                    Caption = 'Positive Finished Quantity';
                    ToolTip = 'Specifies the value of the Positive Finished Quantity field.';
                }
                field(PositiveRemaningQty; Rec.Quantity - FlexatiProductionMngt.CalculatePositiveFinishedQtyFromProdOrderLine(Rec))
                {
                    Caption = 'Positive Remaining Quantity';
                    ToolTip = 'Specifies the value of the Positive Remaining Quantity field.';
                }
                field("Remaining Quantity"; Rec."Remaining Quantity")
                {
                    ToolTip = 'Specifies the value of the Remaining Quantity field.';
                }
                field("Inventory FLX"; Rec."Inventory FLX")
                {
                }
                field("Sales Order Qty. FLX"; Rec."Sales Order Qty. FLX")
                {
                }
                field("Shipped Qty. FLX"; Rec."Shipped Qty. FLX")
                {
                }
                field("Qty. to Ship FLX"; Rec."Sales Order Qty. FLX" - Abs(Rec."Shipped Qty. FLX"))
                {
                    Caption = 'Qty. to Ship';
                    ToolTip = 'Specifies the value of the Qty. to Ship FLX field.';
                }
                field("FG Qty. FLX"; Rec."FG Qty. FLX")
                {
                }
                field("KALITE Qty. FLX"; Rec."KALITE Qty. FLX")
                {
                }
                field("YURUMEYEN Qty. FLX"; Rec."YURUMEYEN Qty. FLX")
                {
                }
                field("UHD Qty. FLX"; Rec."UHD Qty. FLX")
                {
                }
                field("ISKARTA Qty. FLX"; Rec."ISKARTA Qty. FLX")
                {
                }
                field("SUPHELI Qty. FLX"; Rec."SUPHELI Qty. FLX")
                {
                }
                field("Piece FLX"; Rec."Piece FLX")
                {
                }
                field("Hose Length FLX"; Rec."Hose Length FLX")
                {
                }
                field("ID (mm) FLX"; Rec."ID (mm) FLX")
                {
                }
                field("Sales Order Date FLX"; Rec."Sales Order Date FLX")
                {
                }
                field("Planned Shipment Date FLX"; Rec."Planned Shipment Date FLX")
                {
                }
                field("Flexati Shipment Date FLX"; Rec."Flexati Shipment Date FLX")
                {
                }
                field("Stop FLX"; Rec."Stop FLX")
                {
                }
                field("Line Note FLX"; Rec."Line Note FLX")
                {
                }
                field("Note FLX"; Rec."Note FLX")
                {
                }
                //field("BOM Low Level Code FLX"; Rec."BOM Low Level Code FLX")
                //{
                //    ToolTip = 'Specifies the value of the BOM Low Level Code field.';
                //}
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(UpdateBrandCodes)
            {
                ApplicationArea = All;
                Caption = 'Update Brand Codes';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = UpdateDescription;
                PromotedOnly = true;
                ToolTip = 'Executes the Update Brand Codes action.';
                trigger OnAction()
                begin
                    UpdateBrandCode();
                end;
            }
        }
    }
    trigger OnOpenPage()
    begin
        //Rec.SetRange("BOM Low Level Code FLX", 1);
        //Rec.SetRange("Prod. Order No.", 'SBRS24-000011');
        Rec.SetFilter(Status, '%1|%2', Rec.Status::Released, Rec.Status::"Firm Planned");
        //Rec.SetFilter("Prod. Order No.", 'STSP*');
    end;

    procedure UpdateBrandCode()
    var
        UpdateMsg: Label 'Brand Codes Updated';
    begin
        ProdOrderLine.Reset();
        ProdOrderLine.SetRange(Status, ProdOrderLine.Status::Released);
        if ProdOrderLine.FindSet() then
            repeat
                ProdOrderLine."Brand FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'LL');
                ProdOrderLine."Brand Description FLX" := FlexatiProductionMngt.GetBomItemNoDesc_ByItemCategoryCode(ProdOrderLine, 'LL');
                ProdOrderLine."Cloth FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'TR');
                ProdOrderLine."Paste FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'RC');
                ProdOrderLine."Wire FLX" := FlexatiProductionMngt.GetBomItemNoList_ByItemCategoryCode(ProdOrderLine, 'WR');
                ProdOrderLine.Modify(true);
            until ProdOrderLine.Next() = 0;
        Message(UpdateMsg);
    end;

    var
        ProdOrderLine: Record "Prod. Order Line";
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
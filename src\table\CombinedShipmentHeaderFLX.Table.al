/// <summary>
/// Table Combined Shipment Header FLX (ID 60005).
/// </summary>
table 60005 "Combined Shipment Header FLX"
{
    Caption = 'Combined Shipment Header';
    LookupPageId = "Combined Shipment List FLX";
    DrillDownPageId = "Combined Shipment List FLX";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            begin
                Clear(NoSeries);
                if "No." <> xRec."No." then begin
                    FlexatiSetup.Get();
                    NoSeries.TestManual(FlexatiSetup."Combined Shipment No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(3; "Ship-to Name"; Text[100])
        {
            Caption = 'Ship-to Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Name field.';
        }
        field(4; "Ship-to Name 2"; Text[50])
        {
            Caption = 'Ship-to Name 2';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Name 2 field.';
        }
        field(5; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            AllowInCustomizations = Always;
        }
        field(6; "Ship-to Address"; Text[100])
        {
            Caption = 'Ship-to Address';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Address field.';
        }
        field(7; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(8; "Ship-to Address 2"; Text[50])
        {
            Caption = 'Ship-to Address 2';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Address 2 field.';
        }
        field(10; "Ship-to City"; Text[30])
        {
            Caption = 'Ship-to City';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to City field.';
        }
        field(11; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            var
                CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
                Customer: Record Customer;
                NotEmptyErr: Label 'You need to delete all read packages before updating Combined Shipment lines.';
            begin
                CombinedShipmentLineDtl.SetRange("Document No.", Rec."No.");
                if not CombinedShipmentLineDtl.IsEmpty() then
                    Error(NotEmptyErr);

                Customer.Get("Customer No.");
                "Customer Name" := Customer.Name;
                "Ship-to Address" := Customer.Address;
                "Ship-to Address 2" := Customer."Address 2";
                "Ship-to City" := Customer.City;
                "Ship-to Country/Region Code" := Customer."Country/Region Code";
                "Ship-to County" := Customer.County;
                "Ship-to Name" := Customer.Name;
                "Ship-to Name 2" := Customer."Name 2";
                "Ship-to Post Code" := Customer."Post Code";
            end;
        }
        field(12; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(13; "Total Package Count"; Integer)
        {
            Caption = 'Total Package Count';
            FieldClass = FlowField;
            CalcFormula = count("CombinedShipmentLineDtl FLX" where("Document No." = field("No.")));
            Editable = false;
            ToolTip = 'Specifies the value of the Total Package Quantity field.';
        }
        field(14; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            begin
                FlexatiSalesManagement.ProcessLabel(Rec);
            end;
        }
        field(15; "Ship-to Code"; Code[10])
        {
            Caption = 'Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Customer No."));
            ToolTip = 'Specifies the value of the Ship-to Code field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
                ShiptoAddress: Record "Ship-to Address";
            begin
                if ShiptoAddress.Get("Customer No.", "Ship-to Code") then begin
                    "Ship-to Address" := ShiptoAddress.Address;
                    "Ship-to Address 2" := ShiptoAddress."Address 2";
                    "Ship-to City" := ShiptoAddress.City;
                    "Ship-to Country/Region Code" := ShiptoAddress."Country/Region Code";
                    "Ship-to County" := ShiptoAddress.County;
                    "Ship-to Name" := ShiptoAddress.Name;
                    "Ship-to Name 2" := ShiptoAddress."Name 2";
                    "Ship-to Post Code" := ShiptoAddress."Post Code";
                end
                else begin
                    Customer.Get("Customer No.");
                    "Customer Name" := Customer.Name;
                    "Ship-to Address" := Customer.Address;
                    "Ship-to Address 2" := Customer."Address 2";
                    "Ship-to City" := Customer.City;
                    "Ship-to Country/Region Code" := Customer."Country/Region Code";
                    "Ship-to County" := Customer.County;
                    "Ship-to Name" := Customer.Name;
                    "Ship-to Name 2" := Customer."Name 2";
                    "Ship-to Post Code" := Customer."Post Code";
                end;
            end;
        }
        field(16; "Ship-to Country/Region Code"; Code[10])
        {
            Caption = 'Ship-to Country/Region Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Country/Region Code field.';
        }
        field(17; "Ship-to Post Code"; Code[20])
        {
            Caption = 'Ship-to Post Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Post Code field.';
        }
        field(18; "Ship-to County"; Text[30])
        {
            Caption = 'Ship-to County';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to County field.';
        }
        field(19; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(20; "Container No."; Code[50])
        {
            Caption = 'Container No.';
            ToolTip = 'Specifies the value of the Container No. field.';
        }
        field(21; "Ship Name"; Text[100])
        {
            Caption = 'Ship Name';
            ToolTip = 'Specifies the value of the Ship Name field.';
        }
        field(22; "Voyage No."; Code[20])
        {
            Caption = 'Voyage No.';
            ToolTip = 'Specifies the value of the Voyage No. field.';
        }
        field(23; "Sales Invoice No."; Code[20])
        {
            Caption = 'Sales Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Sales Invoice No. field.';
        }
        field(24; "Posted Sls. Invoice No."; Code[20])
        {
            Caption = 'Posted Sls. Invoice No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Posted Sls. Invoice No. field.';
        }
        field(25; "Calcualted Shipment Weight"; Decimal)
        {
            Caption = 'Calcualted Shipment Weight (KG)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("CombinedShipmentLineDtl FLX"."Package Weight (KG)" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Calcualted Shipment Weight (KG) field.';
        }
        field(26; "Scaled Shipment Weight"; Decimal)
        {
            Caption = 'Scaled Shipment Weight';
            ToolTip = 'Specifies the value of the Scaled Shipment Weight field.';
        }
        field(27; Status; Enum "Combined Shipment Status FLX")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
        }
        field(28; "Remove Package"; Boolean)
        {
            Caption = 'Remove Package';
            ToolTip = 'Specifies the value of the Remove Package field.';
        }
        field(29; "Shipment Method Code"; Code[20])
        {
            Caption = 'Shipment Method Code';
            Editable = true;
            TableRelation = "Shipment Method".Code;
            ToolTip = 'Specifies the value of the Shipment Method Code field.';
        }
        field(30; "Shipment No."; Code[20])
        {
            Caption = 'Shipment No.';
            ToolTip = 'Specifies the value of the Shipment No. field.';
            Editable = true;
            AllowInCustomizations = Always;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    begin
        Clear(NoSeries);
        if "No." = '' then begin
            FlexatiSetup.Get();
            FlexatiSetup.TestField("Combined Shipment No. Series");

            Rec."No. Series" := FlexatiSetup."Combined Shipment No. Series";
            if NoSeries.AreRelated(FlexatiSetup."Combined Shipment No. Series", xRec."No. Series") then
                Rec."No. Series" := xRec."No. Series";
            Rec."No." := NoSeries.GetNextNo(Rec."No. Series");

            //NoSeriesManagement.InitSeries(FlexatiSetup."Combined Shipment No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        end;

        Rec.Validate(Status, Rec.Status::New);
        Rec.Validate("Posting Date", WorkDate());
    end;

    trigger OnDelete()
    var
        CombinedShipmentLineDetail: Record "CombinedShipmentLineDtl FLX";
        CombinedShipmentLine: Record "Combined Shipment Line FLX";
    begin
        //Rec.TestField("Transferred to Sales Order", false);
        Rec.TestField(Status, Rec.Status::New);
        CombinedShipmentLine.SetRange("Document No.", Rec."No.");
        CombinedShipmentLine.DeleteAll(true);

        CombinedShipmentLineDetail.SetRange("Document No.", Rec."No.");
        CombinedShipmentLineDetail.DeleteAll(true);
    end;

    var
        FlexatiSetup: Record "Flexati Setup FLX";
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
        //NoSeries: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
}

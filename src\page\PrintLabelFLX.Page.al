page 60041 "Print Label FLX"
{
    ApplicationArea = All;
    Caption = 'Barcode Read Customer Label Print';
    PageType = Card;
    UsageCategory = Documents;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Label Text"; PackageNo)
                {
                    ToolTip = 'Specifies Label field.';
                    Caption = 'Label Text';
                    trigger OnValidate()
                    begin
                        PackageNoInformation.Reset();
                        PackageNoInformation.SetRange("Package No.", PackageNo);
                        PackageNoInformation.FindFirst();
                        case PackageNoInformation."Package Type FLX" of
                            "Package Type FLX"::Coil, "Package Type FLX"::Head:
                                Report.Run(Report::"QR Label FLX", true, true, PackageNoInformation);
                            "Package Type FLX"::Palette, "Package Type FLX"::Bulk:
                                Report.Run(Report::"Palette Label FLX", true, true, PackageNoInformation);
                        end;
                    end;
                }
            }
        }
    }
    var
        PackageNoInformation: Record "Package No. Information";
        PackageNo: Code[20];
}
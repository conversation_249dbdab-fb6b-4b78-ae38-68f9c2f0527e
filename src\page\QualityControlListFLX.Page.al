page 60016 "Quality Control List FLX"
{
    ApplicationArea = All;
    Caption = 'Quality Control List';
    PageType = List;
    SourceTable = "Quality Control Header FLX";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Quality Control FLX";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Date; Rec.Date)
                {
                }
                field(Posted; Rec.Posted)
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
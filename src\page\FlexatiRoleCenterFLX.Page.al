page 60030 "Flexati Role Center FLX"
{
    ApplicationArea = All;
    Caption = 'Flexati Role Center';
    PageType = RoleCenter;

    layout
    {
        area(RoleCenter)
        {
            group("Flexati Activities")
            {
                Caption = 'Flexati Activities';
                part(ActivitiiesPart; "Flexati Activities FLX")
                {
                }
            }
        }
    }
}
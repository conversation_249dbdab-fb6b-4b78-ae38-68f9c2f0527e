/// <summary>
/// Codeunit Flexati Sales Management FLX (ID 60000).
/// </summary>
codeunit 60000 "Flexati Sales Management FLX"
{
    procedure UpdateSalesLineQuantity(var SalesLine: Record "Sales Line")
    var
        UnitOfMeasure: Record "Unit of Measure";
    begin
        if SalesLine.Type <> SalesLine.Type::Item then
            exit;

        // Check if manual entry is enabled for this unit of measure
        if UnitOfMeasure.Get(SalesLine."Unit of Measure Code") and UnitOfMeasure."Manual Entry FLX" then
            exit; // Skip automatic calculation when manual entry is enabled

        // Perform automatic calculation (existing logic)
        SalesLine.Validate(Quantity, SalesLine."Piece FLX" * SalesLine."Hose Length FLX");
    end;

    procedure CalculateCoilWeightFromItemNo(ItemNo: Code[20]): Decimal
    var
        Item: Record Item;
        ProductionBOMLine: Record "Production BOM Line";
        VersionManagement: Codeunit VersionManagement;
        TotalWeight: Decimal;
        VersionCode: Code[20];
    begin
        if not Item.Get(ItemNo) then
            exit;

        if Item."Production BOM No." = '' then
            exit;
        TotalWeight := 0;
        VersionCode := VersionManagement.GetBOMVersion(Item."Production BOM No.", WorkDate(), true);
        ProductionBOMLine.SetCurrentKey("Production BOM No.", "Version Code", Type, "Unit of Measure Code");
        ProductionBOMLine.SetLoadFields("Quantity per");
        ProductionBOMLine.SetRange("Production BOM No.", Item."Production BOM No.");
        if VersionCode <> '' then
            ProductionBOMLine.SetRange("Version Code", VersionCode);
        if ProductionBOMLine.FindSet() then
            repeat
                if ProductionBOMLine."Unit of Measure Code" = 'KG' then
                    TotalWeight += ProductionBOMLine."Quantity per"
                else begin
                    if (Item.Get(ProductionBOMLine."No.")) and (Item."Production BOM No." <> '') then
                        TotalWeight += CalculateCoilWeightFromItemNo_Child(Item."No.", ProductionBOMLine."Quantity per");
                    TotalWeight += ProductionBOMLine."Bom Kg FLX";
                end;
            until ProductionBOMLine.Next() = 0;
        exit(TotalWeight);
    end;

    procedure CalculateCoilWeightFromItemNo_Child(ItemNo: Code[20]; FatherQtyPer: Decimal): Decimal
    var
        Item: Record Item;
        ProductionBOMLine: Record "Production BOM Line";
        VersionManagement: Codeunit VersionManagement;
        TotalWeight: Decimal;
        SubQtyPer: Decimal;
        VersionCode: Code[20];
    begin
        if not Item.Get(ItemNo) then
            exit;

        if Item."Production BOM No." = '' then
            exit;
        TotalWeight := 0;
        VersionCode := VersionManagement.GetBOMVersion(Item."Production BOM No.", WorkDate(), true);
        ProductionBOMLine.SetCurrentKey("Production BOM No.", "Version Code", Type, "Unit of Measure Code");
        ProductionBOMLine.SetLoadFields("Quantity per");
        ProductionBOMLine.SetRange("Production BOM No.", Item."Production BOM No.");
        if VersionCode <> '' then
            ProductionBOMLine.SetRange("Version Code", VersionCode);
        if ProductionBOMLine.FindSet() then
            repeat
                SubQtyPer := FatherQtyPer * ProductionBOMLine."Quantity per";
                if ProductionBOMLine."Unit of Measure Code" = 'KG' then
                    TotalWeight += SubQtyPer
                else begin
                    if (Item.Get(ProductionBOMLine."No.")) and (Item."Production BOM No." <> '') then
                        TotalWeight += CalculateCoilWeightFromItemNo_Child(Item."No.", SubQtyPer);
                    TotalWeight += ProductionBOMLine."Bom Kg FLX";
                end;
            until ProductionBOMLine.Next() = 0;
        exit(TotalWeight);
    end;

    procedure CalculateCoilWeightFromItemNo_Old(ItemNo: Code[20]): Decimal
    var
        Item: Record Item;
        ProductionBOMLine: Record "Production BOM Line";
        SubProdBomLine: Record "Production BOM Line";
        VersionManagement: Codeunit VersionManagement;
        TotalWeight: Decimal;
        VersionCode: Code[20];
    begin
        if not Item.Get(ItemNo) then
            exit;

        if Item."Production BOM No." = '' then
            exit;
        VersionCode := VersionManagement.GetBOMVersion(Item."Production BOM No.", WorkDate(), true);
        ProductionBOMLine.SetCurrentKey("Production BOM No.", "Version Code", Type, "Unit of Measure Code");
        ProductionBOMLine.SetLoadFields("Quantity per");
        ProductionBOMLine.SetRange("Production BOM No.", Item."Production BOM No.");
        if VersionCode <> '' then
            ProductionBOMLine.SetRange("Version Code", VersionCode);
        //ProductionBOMLine.SetRange(Type, ProductionBOMLine.Type::Item);
        //ProductionBOMLine.SetRange("Unit of Measure Code", 'KG');
        //ProductionBOMLine.CalcSums("Quantity per");
        if ProductionBOMLine.FindSet() then
            repeat
                if (Item.Get(ProductionBOMLine."No.")) and (Item."Production BOM No." <> '') then begin
                    VersionCode := VersionManagement.GetBOMVersion(Item."Production BOM No.", WorkDate(), true);
                    SubProdBomLine.SetCurrentKey("Production BOM No.", "Version Code", Type, "Unit of Measure Code");
                    SubProdBomLine.SetLoadFields("Quantity per");
                    SubProdBomLine.SetRange("Production BOM No.", Item."Production BOM No.");
                    if VersionCode <> '' then
                        SubProdBomLine.SetRange("Version Code", VersionCode);
                    //SubProdBomLine.SetRange(Type, ProductionBOMLine.Type::Item);
                    //SubProdBomLine.SetRange("Unit of Measure Code", 'KG');
                    if SubProdBomLine.FindSet() then
                        repeat
                            if SubProdBomLine."Unit of Measure Code" = 'KG' then
                                TotalWeight += ProductionBOMLine."Quantity per" * SubProdBomLine."Quantity per";
                        until SubProdBomLine.Next() = 0;
                end else
                    if ProductionBOMLine."Unit of Measure Code" = 'KG' then
                        TotalWeight += ProductionBOMLine."Quantity per";
            until ProductionBOMLine.Next() = 0;

        //exit(ProductionBOMLine."Quantity per");
        exit(TotalWeight);
    end;

    procedure CalculatePackageWeight(var PackageNoInf: Record "Package No. Information"): Decimal
    var
        Item: Record Item;
        ChildPackageNoInf: Record "Package No. Information";
        TotalWeight: Decimal;
    begin
        TotalWeight := 0;

        if (PackageNoInf."Palette Item No. FLX" <> '') and (Item.Get(PackageNoInf."Palette Item No. FLX")) then
            TotalWeight := Item."Net Weight"
        else
            if (PackageNoInf."Item No." <> '') and (Item.Get(PackageNoInf."Item No.")) then
                TotalWeight := Round(PackageNoInf."Hose Lenght FLX" * CalculateCoilWeightFromItemNo(PackageNoInf."Item No."), 0.01);

        ChildPackageNoInf.Reset();
        ChildPackageNoInf.SetCurrentKey("Parent Package No. FLX");
        ChildPackageNoInf.SetLoadFields("Hose Lenght FLX", "Item No.");
        ChildPackageNoInf.SetRange("Parent Package No. FLX", PackageNoInf."Package No.");
        if ChildPackageNoInf.FindSet() then
            repeat
                TotalWeight += Round(ChildPackageNoInf."Hose Lenght FLX" * CalculateCoilWeightFromItemNo(ChildPackageNoInf."Item No."), 0.01);
            until ChildPackageNoInf.Next() = 0;
        exit(TotalWeight);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnAfterAssignItemValues, '', false, false)]
    local procedure OnAfterAssignItemValues(var SalesLine: Record "Sales Line"; Item: Record Item; SalesHeader: Record "Sales Header")
    begin
        SalesLine."Unit Weight (Base) FLX" := CalculateCoilWeightFromItemNo(SalesLine."No.");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterCopySellToCustomerAddressFieldsFromCustomer, '', false, false)]
    local procedure OnAfterCopySellToCustomerAddressFieldsFromCustomer(var SalesHeader: Record "Sales Header"; SellToCustomer: Record Customer; CurrentFieldNo: Integer; var SkipBillToContact: Boolean; var SkipSellToContact: Boolean)
    begin
        SalesHeader.Validate("Company Bank Account Code", SellToCustomer."Company Bank Account Code FLX");
        SalesHeader.Validate("Note FLX", SellToCustomer."Note FLX");
    end;

    procedure ProcessLabel(var CombinedShipmentHeader: Record "Combined Shipment Header FLX")
    var
        PackageNoInformation: Record "Package No. Information";
        StatusErr: Label 'Status must be %1 or %2', Comment = '%1=Status; %2=Status';
    begin
        if CombinedShipmentHeader.Barcode = '' then
            exit;

        if not (CombinedShipmentHeader.Status in [CombinedShipmentHeader.Status::New, CombinedShipmentHeader.Status::Loading]) then
            Error(StatusErr, CombinedShipmentHeader.Status::New, CombinedShipmentHeader.Status::Loading);

        PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.SetRange("Parent Package No. FLX", CombinedShipmentHeader.Barcode);
        if PackageNoInformation.FindSet(false) then begin
            InsertPaletteChecksToCombinedShipment(CombinedShipmentHeader, PackageNoInformation);
            repeat
                InsertOrRemovePackage(CombinedShipmentHeader, PackageNoInformation);
            until PackageNoInformation.Next() = 0
        end
        else begin
            PackageNoInformation.Reset();
            PackageNoInformation.SetRange("Package No.", CombinedShipmentHeader.Barcode);
            PackageNoInformation.FindFirst();
            PackageNoInformation.TestField("Parent Package No. FLX", '');
            InsertOrRemovePackage(CombinedShipmentHeader, PackageNoInformation);
        end;

        CombinedShipmentHeader.Barcode := '';
        CombinedShipmentHeader.Validate(Status, CombinedShipmentHeader.Status::Loading);
    end;

    local procedure InsertOrRemovePackage(var CombinedShipmentHeader: Record "Combined Shipment Header FLX"; var PackageNoInformation: Record "Package No. Information")
    begin
        if CombinedShipmentHeader."Remove Package" then
            RemoveCombinedShipmentLineDetail(CombinedShipmentHeader, PackageNoInformation)
        else
            InsertCombinedShipmentLineDetail(CombinedShipmentHeader, PackageNoInformation);
    end;

    local procedure InsertCombinedShipmentLineDetail(CombinedShipmentHeader: Record "Combined Shipment Header FLX"; PackageNoInformation: Record "Package No. Information")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        CombinedShipmentLine: Record "Combined Shipment Line FLX";
        AlreadyInAnotherCombinedShipmentErr: Label 'Package No. %1 is already read on Combined Shipment No.: %2. You have delete that package from that Combined Shipment to read for this document.', Comment = '%1="Combined Shipment Header FLX".Barcode; %2="CombinedShipmentLineDtl FLX"."Document No."';
        EmptyPackageErr: Label 'You can not process an empty package. Package No.: %1', Comment = '%1 is Package No.';
        IncreaseQtyErr: Label 'Sales Order No.: %1 Line No.: %2 is shipped completely. You must increase Quantity to continue.', Comment = '%1="Combined Shipment Line FLX"."Source Document No."; %2="Combined Shipment Line FLX"."Source Document Line No."';
        QualityControlErr: Label 'Package status must be accepted for reading into combined shipment.';
    begin
        PackageNoInformation.TestField("Package Type FLX", PackageNoInformation."Package Type FLX"::Coil);

        if not QualityControlManagement.IsPackageQualityControlAccepted(PackageNoInformation."Package No.") then
            Error(QualityControlErr);

        PackageNoInformation.CalcFields(Inventory);

        if PackageNoInformation.Inventory = 0 then
            Error(EmptyPackageErr, PackageNoInformation."Package No.");

        CombinedShipmentLine.SetAutoCalcFields("Qty. to Ship (Base)", "Outstanding Qty. (Base)");
        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.SetRange("Item No.", PackageNoInformation."Item No.");
        CombinedShipmentLine.SetRange("Variant Code", PackageNoInformation."Variant Code");
        CombinedShipmentLine.SetRange("Source Document No.", PackageNoInformation."Sales Order No. FLX");
        CombinedShipmentLine.SetRange("Source Document Line No.", PackageNoInformation."Sales Order Line No. FLX");
        CombinedShipmentLine.FindFirst();

        if not ((CombinedShipmentLine."Outstanding Qty. (Base)" - CombinedShipmentLine."Qty. to Ship (Base)") >= PackageNoInformation.Inventory) then
            Error(IncreaseQtyErr, CombinedShipmentLine."Source Document No.", CombinedShipmentLine."Source Document Line No.");

        CombinedShipmentLineDtl.SetRange("Package No.", CombinedShipmentHeader.Barcode);
        if CombinedShipmentLineDtl.FindFirst() then
            Error(AlreadyInAnotherCombinedShipmentErr, CombinedShipmentHeader.Barcode, CombinedShipmentLineDtl."Document No.");

        PackageNoInformation.CalcFields("Location Code FLX");
        PackageNoInformation.TestField("Location Code FLX", CombinedShipmentLine."Location Code");

        CombinedShipmentLineDtl.Init();
        CombinedShipmentLineDtl."Document No." := CombinedShipmentLine."Document No.";
        CombinedShipmentLineDtl."Document Line No." := CombinedShipmentLine."Line No.";
        CombinedShipmentLineDtl.Insert(true);
        CombinedShipmentLineDtl.Validate("Source Document No.", CombinedShipmentLine."Source Document No.");
        CombinedShipmentLineDtl.Validate("Source Document Line No.", CombinedShipmentLine."Source Document Line No.");
        CombinedShipmentLineDtl.Validate("Item No.", CombinedShipmentLine."Item No.");
        CombinedShipmentLineDtl.Validate("Variant Code", CombinedShipmentLine."Variant Code");
        CombinedShipmentLineDtl.Validate("Item Description", CombinedShipmentLine."Item Description");
        CombinedShipmentLineDtl.Validate("Location Code", CombinedShipmentLine."Location Code");
        CombinedShipmentLineDtl.Validate("Bin Code", CombinedShipmentLine."Bin Code");
        CombinedShipmentLineDtl.Validate(Quantity, PackageNoInformation.Inventory);
        CombinedShipmentLineDtl.Validate("Package No.", PackageNoInformation."Package No.");
        CombinedShipmentLineDtl.Validate("Parent Package No.", PackageNoInformation."Parent Package No. FLX");
        CombinedShipmentLineDtl.Validate("Lot No.", PackageNoInformation."Lot No. FLX");
        CombinedShipmentLineDtl.Validate("Package Weight (KG)", CombinedShipmentLineDtl.Quantity * CalculateCoilWeightFromItemNo(CombinedShipmentLineDtl."Item No."));
        CombinedShipmentLineDtl.Validate("Customer Name", CombinedShipmentHeader."Customer Name");
        CombinedShipmentLineDtl.Modify(true);

        // CombinedShipmentLineDtl2.SetRange("Document No.", CombinedShipmentHeader."No.");
        // CombinedShipmentLineDtl2.SetRange("Package No.", CombinedShipmentLineDtl."Package No.");
        // if CombinedShipmentLineDtl2.Count > 1 then
        //     Error(MultipleSamePackageErr, CombinedShipmentLineDtl."Package No.");
    end;

    local procedure RemoveCombinedShipmentLineDetail(CombinedShipmentHeader: Record "Combined Shipment Header FLX"; CoilPackageNoInformation: Record "Package No. Information")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
    begin
        CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLineDtl.SetRange("Package No.", CoilPackageNoInformation."Package No.");
        CombinedShipmentLineDtl.FindFirst();

        CombinedShipmentLineDtl.Delete(true);
    end;

    procedure GetSalesOrderLines(CombinedShipmentHeader: Record "Combined Shipment Header FLX")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        CombinedShipmentLine: Record "Combined Shipment Line FLX";
        SalesReceivablesSetup: Record "Sales & Receivables Setup";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        DeleteAllPackagesErr: Label 'You need to delete all read packages before updating Combined Shipment lines.';
    begin
        CombinedShipmentHeader.TestField("Customer No.");

        SalesReceivablesSetup.GetRecordOnce();
        SalesReceivablesSetup.TestField("Default Quantity to Ship", SalesReceivablesSetup."Default Quantity to Ship"::Blank);

        CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentHeader."No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(DeleteAllPackagesErr);

        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.DeleteAll(true);

        SalesHeader.SetRange("Document Type", SalesHeader."Document Type"::Order);
        SalesHeader.SetRange("Sell-to Customer No.", CombinedShipmentHeader."Customer No.");
        SalesHeader.SetRange(Status, SalesHeader.Status::Released);
        if CombinedShipmentHeader."Ship-to Code" <> '' then
            SalesHeader.SetRange("Ship-to Code", CombinedShipmentHeader."Ship-to Code");

        SalesHeader.FindSet();
        repeat
            SalesLine.SetRange("Document Type", SalesHeader."Document Type");
            SalesLine.SetRange("Document No.", SalesHeader."No.");
            SalesLine.SetRange(Type, SalesLine.Type::Item);
            if SalesLine.FindSet() then
                repeat
                    CombinedShipmentLine.Init();
                    CombinedShipmentLine."Document No." := CombinedShipmentHeader."No.";
                    CombinedShipmentLine.Insert(true);
                    CombinedShipmentLine.Validate("Source Document No.", SalesLine."Document No.");
                    CombinedShipmentLine.Validate("Source Document Line No.", SalesLine."Line No.");
                    CombinedShipmentLine.Validate("Item No.", SalesLine."No.");
                    CombinedShipmentLine.Validate("Variant Code", SalesLine."Variant Code");
                    CombinedShipmentLine.Validate("Item Description", SalesLine.Description);
                    CombinedShipmentLine.Validate("Unit of Measure Code", SalesLine."Unit of Measure Code");
                    CombinedShipmentLine.Validate("Quantity (Base)", SalesLine."Quantity (Base)");
                    CombinedShipmentLine.Validate("Qty. Shipped (Base)", SalesLine."Qty. Shipped (Base)");
                    CombinedShipmentLine.Validate("Location Code", SalesLine."Location Code");
                    CombinedShipmentLine.Validate("Bin Code", SalesLine."Bin Code");
                    CombinedShipmentLine.Validate("Ship-to Code", SalesHeader."Ship-to Code");
                    CombinedShipmentLine.Modify(true);
                until SalesLine.Next() = 0;
        until SalesHeader.Next() = 0;
    end;

    procedure TransferAndShipReadPackages(var CombinedShipmentHeader: Record "Combined Shipment Header FLX")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        CombinedShipmentLine: Record "Combined Shipment Line FLX";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        NoLinesErr: Label 'No Lines found.';
        ProcessAbortedErr: Label 'Process aborted.';
        QtyToShipMoreThanOutstandingQtyLbl: Label 'Total Qty. to Ship is %1 more than Outstanding Quantity. Do you want to continue?', Comment = '%1=CombinedShipmentLine."Qty. to Ship (Base)" - CombinedShipmentLine."Outstanding Qty. (Base)"';
        //NotReadyToShipErr: Label 'All read packages have to be Ready to Ship.';
        SuccesMsg: Label 'Combined Shipment has been Shipped successfully.';
        QtyToShipMoreThanOutstandingQtyQst: Text;
    begin
        CombinedShipmentHeader.TestField(Status, Enum::"Combined Shipment Status FLX"::Loading);
        CombinedShipmentHeader.TestField("Posting Date");

        CombinedShipmentLine.SetAutoCalcFields("Qty. to Ship (Base)", "Outstanding Qty. (Base)");
        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.SetFilter("Qty. to Ship (Base)", '<>0');
        if not CombinedShipmentLine.FindSet() then
            Error(NoLinesErr);

        // CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentHeader."No.");
        // CombinedShipmentLineDtl.SetRange("Ready to Ship", false);
        // if not CombinedShipmentLineDtl.IsEmpty() then
        //     Error(NotReadyToShipErr);

        repeat
            SalesHeader.Get(SalesHeader."Document Type"::Order, CombinedShipmentLine."Source Document No.");
            SalesLine.Get(SalesLine."Document Type"::Order, CombinedShipmentLine."Source Document No.", CombinedShipmentLine."Source Document Line No.");
            QtyToShipMoreThanOutstandingQtyQst := StrSubstNo(QtyToShipMoreThanOutstandingQtyLbl, (CombinedShipmentLine."Qty. to Ship (Base)" - CombinedShipmentLine."Outstanding Qty. (Base)"));
            if CombinedShipmentLine."Qty. to Ship (Base)" > CombinedShipmentLine."Outstanding Qty. (Base)" then
                if not ConfirmManagement.GetResponseOrDefault(QtyToShipMoreThanOutstandingQtyQst, false) then
                    Error(ProcessAbortedErr)
                else begin
                    SalesHeader.Validate(Status, SalesHeader.Status::Open);
                    SalesLine.Validate("Quantity (Base)", SalesLine."Quantity (Base)" + CombinedShipmentLine."Qty. to Ship (Base)" - CombinedShipmentLine."Outstanding Qty. (Base)");
                end;

            CombinedShipmentLineDtl.Reset();
            CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentLine."Document No.");
            CombinedShipmentLineDtl.SetRange("Document Line No.", CombinedShipmentLine."Line No.");
            CombinedShipmentLineDtl.FindSet();
            repeat
                SalesLine.Validate("Qty. to Ship", SalesLine."Qty. to Ship" + Round(CombinedShipmentLineDtl.Quantity / SalesLine."Qty. per Unit of Measure", 0.0001));
                //Message(Format(SalesLine."Qty. to Ship (Base)"));
                SalesLine."Qty. to Ship (Base)" := Round(SalesLine."Qty. to Ship (Base)", 0.0001);
                SalesLine.Modify(true);
                AssignLotAndPackagesNosToSalesLine(SalesLine, CombinedShipmentLineDtl);
            until CombinedShipmentLineDtl.Next() = 0;

            SalesHeader.Validate("Posting Date", CombinedShipmentHeader."Posting Date");
            //SalesHeader.Validate("Shipping No.", CombinedShipmentHeader."No.");
            SalesHeader.Validate("External Document No.", CombinedShipmentHeader."No.");
            SalesHeader.Mark(true);
            SalesHeader.Modify(true);
        until CombinedShipmentLine.Next() = 0;

        //SalesHeader.Reset();

        SalesHeader.MarkedOnly(true);
        SalesHeader.FindSet(true);
        repeat
            SalesHeader.Ship := true;
            Codeunit.Run(Codeunit::"Sales-Post", SalesHeader);
        until SalesHeader.Next() = 0;

        CombinedShipmentHeader.Validate(Status, CombinedShipmentHeader.Status::Shipped);

        //CombinedShipmentHeader.Modify(true);

        Message(SuccesMsg);
    end;

    procedure AssignLotAndPackagesNosToSalesLine(SalesLine: Record "Sales Line"; CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX")
    var
        TempReservationEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservationStatus: Enum "Reservation Status";
    begin
        TempReservationEntry.Init();
        TempReservationEntry."Entry No." := 1;
        TempReservationEntry.Quantity := CombinedShipmentLineDtl.Quantity;
        TempReservationEntry."Lot No." := CombinedShipmentLineDtl."Lot No.";
        TempReservationEntry."Package No." := CombinedShipmentLineDtl."Package No.";
        TempReservationEntry.Insert(false);

        CreateReservEntry.CreateReservEntryFor(Database::"Sales Line", SalesLine."Document Type".AsInteger(),
                                                SalesLine."Document No.", '', 0, SalesLine."Line No.", SalesLine."Qty. per Unit of Measure",
                                                TempReservationEntry.Quantity, TempReservationEntry.Quantity, TempReservationEntry);

        CreateReservEntry.CreateEntry(SalesLine."No.", SalesLine."Variant Code", SalesLine."Location Code", '',
                                        0D, 0D, 0, ReservationStatus::Surplus);
    end;

    // [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeConfirmUpdateCurrencyFactor, '', false, false)]
    // local procedure OnBeforeConfirmUpdateCurrencyFactor(var SalesHeader: Record "Sales Header"; var HideValidationDialog: Boolean);
    // begin
    //     HideValidationDialog := true;
    // end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnValidatePostingDateOnBeforeCheckNeedUpdateCurrencyFactor, '', false, false)]
    local procedure OnValidatePostingDateOnBeforeCheckNeedUpdateCurrencyFactor(var SalesHeader: Record "Sales Header"; var IsConfirmed: Boolean; var NeedUpdateCurrencyFactor: Boolean; xSalesHeader: Record "Sales Header")
    begin
        NeedUpdateCurrencyFactor := false;
    end;

    procedure CreateSalesInvoiceFromCombinedShipmentHeader(CombinedShipmentHeader: Record "Combined Shipment Header FLX")
    var
        SalesHeader: Record "Sales Header";
        SalesShipmentLine: Record "Sales Shipment Line";
        SalesGetShipment: Codeunit "Sales-Get Shipment";
    begin
        CombinedShipmentHeader.TestField("Sales Invoice No.", '');

        SalesHeader.Init();
        SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", CombinedShipmentHeader."Customer No.");
        SalesHeader.Validate("Ship-to Code", CombinedShipmentHeader."Ship-to Code");
        SalesHeader.Modify(true);

        SalesGetShipment.SetSalesHeader(SalesHeader);

        SalesShipmentLine.SetRange("External Document No. FLX", CombinedShipmentHeader."No.");
        SalesShipmentLine.SetRange("Currency Code", SalesHeader."Currency Code");
        SalesShipmentLine.SetRange("Sell-to Customer No.", SalesHeader."Sell-to Customer No.");
        SalesShipmentLine.SetRange("Bill-to Customer No.", SalesHeader."Bill-to Customer No.");
        SalesShipmentLine.SetFilter("Qty. Shipped Not Invoiced", '<>0');
        SalesShipmentLine.SetRange("Authorized for Credit Card", false);
        if SalesShipmentLine.FindSet() then
            repeat
                SalesShipmentLine.Mark(true);
            until SalesShipmentLine.Next() = 0;

        SalesShipmentLine.MarkedOnly(true);

        SalesGetShipment.CreateInvLines(SalesShipmentLine);

        CombinedShipmentHeader."Sales Invoice No." := SalesHeader."No.";
        CombinedShipmentHeader.Modify(true);

        //Page.Run(Page::"Sales Invoice", SalesHeader);
        PageManagement.PageRun(SalesHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Get Shipment", OnAfterInsertLine, '', false, false)]
    local procedure OnAfterInsertLine(var SalesShptLine: Record "Sales Shipment Line"; var SalesLine: Record "Sales Line"; SalesShptLine2: Record "Sales Shipment Line"; TransferLine: Boolean; var SalesHeader: Record "Sales Header")
    begin
        //SalesShptLine2.CalcFields("Your Reference FLX");
        SalesLine."Your Reference FLX" := SalesShptLine2."Your Reference FLX";
        SalesLine.Modify(true);
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterPostSalesDoc, '', false, false)]
    // local procedure OnAfterPostSalesDoc(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; SalesShptHdrNo: Code[20]; RetRcpHdrNo: Code[20]; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20]; CommitIsSuppressed: Boolean; InvtPickPutaway: Boolean; var CustLedgerEntry: Record "Cust. Ledger Entry"; WhseShip: Boolean; WhseReceiv: Boolean; PreviewMode: Boolean)
    // var
    //     CombinedShipmentHeader: Record "Combined Shipment Header FLX";
    // begin
    //     if not SalesHeader.Invoice then
    //         exit;

    //     CombinedShipmentHeader.SetRange("Sales Invoice No.", SalesHeader."No.");
    //     if not CombinedShipmentHeader.FindFirst() then
    //         exit;

    //     CombinedShipmentHeader."Posted Sls. Invoice No." := SalesInvHdrNo;
    //     CombinedShipmentHeader.Modify(true);
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterFinalizePosting, '', false, false)]
    local procedure "Sales-Post_OnAfterFinalizePosting"(var SalesHeader: Record "Sales Header"; var SalesShipmentHeader: Record "Sales Shipment Header"; var SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var ReturnReceiptHeader: Record "Return Receipt Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; CommitIsSuppressed: Boolean; PreviewMode: Boolean)
    var
        CombinedShipmentHeader: Record "Combined Shipment Header FLX";
    begin
        if not SalesHeader.Invoice then
            exit;

        CombinedShipmentHeader.SetRange("Sales Invoice No.", SalesHeader."No.");
        if not CombinedShipmentHeader.FindFirst() then
            exit;

        CombinedShipmentHeader."Posted Sls. Invoice No." := SalesInvoiceHeader."No.";
        CombinedShipmentHeader.Modify(true);
    end;

    procedure CreateNewBulkAndInsertSelectedPackages(var PackageNoInformation: Record "Package No. Information")
    var
        PalettePackageNoInformation: Record "Package No. Information";
        PackagesInsertedMsg: Label 'Total %1 packages added to Palette No.: %2.', Comment = '%1="Package No. Information".Count; %2="Package No. Information"."Package No."';
    //CustomerNo: Code[20];
    begin
        CreateNewParentPackage(PalettePackageNoInformation, Enum::"Package Type FLX"::Bulk);

        PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.FindSet(true);
        PalettePackageNoInformation."Sell-to Customer No. FLX" := PackageNoInformation."Sell-to Customer No. FLX";
        PalettePackageNoInformation."Sell-to Customer Name FLX" := PackageNoInformation."Sell-to Customer Name FLX";
        PalettePackageNoInformation."Ship-to Code FLX" := PackageNoInformation."Ship-to Code FLX";
        PalettePackageNoInformation."Produced At FLX" := CurrentDateTime();
        PalettePackageNoInformation.Modify(true);
        //CustomerNo := PackageNoInformation."Sell-to Customer No. FLX";
        repeat
            // if CustomerNo <> PackageNoInformation."Sell-to Customer No. FLX" then
            //     Error('You can not add different customers packages into same palette.');
            AddPaletteChecks(PackageNoInformation, PalettePackageNoInformation);

            PackageNoInformation."Parent Package No. FLX" := PalettePackageNoInformation."Package No.";
            PackageNoInformation.Modify(true);

        //CustomerNo := PackageNoInformation."Sell-to Customer No. FLX";
        until PackageNoInformation.Next() = 0;

        Message(PackagesInsertedMsg, PackageNoInformation.Count(), PalettePackageNoInformation."Package No.");
    end;

    local procedure AddPaletteChecks(var CoilPackageNoInformation: Record "Package No. Information"; var ParentPackageNoInformation: Record "Package No. Information")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        Location: Record Location;
        PackageNoInformation: Record "Package No. Information";
        DifferentCustomerErr: Label 'You can not add different customers packages into same palette.';
        InventoryZeroErr: Label 'You can not add a package with 0 inventory to a palette.';
        NotAddNewPackageErr: Label 'You can not add new packages to read palette in combined shipment.';
        NotAddNewReadPackageErr: Label 'You can not add a packages that is read in Combined Shipment to a palette.';
        ParentPackageBinErr: Label 'Parent Packages Bin Code must be same with Coils Bin Code.';
    begin
        if ParentPackageNoInformation."Package Type FLX" = ParentPackageNoInformation."Package Type FLX"::Palette then
            ParentPackageNoInformation.TestField("Palette Item No. FLX");

        CoilPackageNoInformation.TestField("Parent Package No. FLX", '');
        CoilPackageNoInformation.TestField("Package Type FLX", CoilPackageNoInformation."Package Type FLX"::Coil);
        CoilPackageNoInformation.TestField("Quality Control Status FLX", CoilPackageNoInformation."Quality Control Status FLX"::Accept);

        CoilPackageNoInformation.CalcFields(Inventory);
        if CoilPackageNoInformation.Inventory = 0 then
            Error(InventoryZeroErr);

        PackageNoInformation.SetRange("Parent Package No. FLX", ParentPackageNoInformation."Package No.");
        PackageNoInformation.SetFilter("Sell-to Customer No. FLX", '<>%1', CoilPackageNoInformation."Sell-to Customer No. FLX");
        if not PackageNoInformation.IsEmpty() then
            Error(DifferentCustomerErr);

        CombinedShipmentLineDtl.SetRange("Parent Package No.", ParentPackageNoInformation."Package No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(NotAddNewPackageErr);

        CombinedShipmentLineDtl.Reset();
        CombinedShipmentLineDtl.SetRange("Package No.", CoilPackageNoInformation."Package No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(NotAddNewReadPackageErr);

        CoilPackageNoInformation.CalcFields("Location Code FLX");

        Location.Get(CoilPackageNoInformation."Location Code FLX");
        Location.TestField("Q. C. Accept Required FLX", true);

        if ParentPackageNoInformation."Parent Package Loc. Code FLX" <> '' then
            CoilPackageNoInformation.TestField("Location Code FLX", ParentPackageNoInformation."Parent Package Loc. Code FLX");

        if ParentPackageNoInformation."Parent Package Bin Code FLX" <> '' then
            if FlexatiProductionMngt.GetBinCodeFromPackageNo(CoilPackageNoInformation."Package No.") <> ParentPackageNoInformation."Parent Package Bin Code FLX" then
                Error(ParentPackageBinErr);

        if ParentPackageNoInformation."Ship-to Code FLX" <> '' then
            CoilPackageNoInformation.TestField("Ship-to Code FLX", ParentPackageNoInformation."Ship-to Code FLX");
    end;

    local procedure RemovePaletteChecks(var CoilPackageNoInformation: Record "Package No. Information"; var ParentPackageNoInformation: Record "Package No. Information")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        NotEmptyErr: Label 'You can not add new packages to read palette to combined shipment.';
    begin
        CoilPackageNoInformation.TestField("Parent Package No. FLX", ParentPackageNoInformation."Package No.");

        CombinedShipmentLineDtl.SetRange("Parent Package No.", ParentPackageNoInformation."Package No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(NotEmptyErr);
    end;

    local procedure InsertPaletteChecksToCombinedShipment(var CombinedShipmentHeader: Record "Combined Shipment Header FLX"; var PackageNoInformation: Record "Package No. Information")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        AlreadyReadPaletteErr: Label 'You can not read a Palette that is already read in a Combined Shipment.';
    begin
        if not CombinedShipmentHeader."Remove Package" then begin
            CombinedShipmentLineDtl.SetRange("Parent Package No.", PackageNoInformation."Parent Package No. FLX");
            if not CombinedShipmentLineDtl.IsEmpty() then
                Error(AlreadyReadPaletteErr);
        end;
    end;

    local procedure BinCodeCheckOnBeforeSalesOrderRelease(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        BinCodeMandatoryErr: Label 'You can not release a sales order that has no Bin Code.';
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        SalesLine.SetRange("Bin Code", '');
        if not SalesLine.IsEmpty() then
            Error(BinCodeMandatoryErr);
    end;

    procedure CreateNewParentPackage(var PalettePackageNoInformation: Record "Package No. Information"; PackageType: Enum "Package Type FLX")
    begin
        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Palette Label No. Series");

        PalettePackageNoInformation.Init();
        PalettePackageNoInformation."Package No." := NoSeries.GetNextNo(FlexatiSetup."Palette Label No. Series", WorkDate(), true);
        PalettePackageNoInformation.Insert(true);
        PalettePackageNoInformation.Validate("Package Type FLX", PackageType);
        PalettePackageNoInformation.Modify(true);
    end;

    procedure InsertRemovePackageToPaletteWithLabelReading(var PackageNoInformation: Record "Package No. Information")
    var
        CoilPackageNoInformation: Record "Package No. Information";
    begin
        if PackageNoInformation."Barcode Text FLX" = '' then
            exit;

        CoilPackageNoInformation.SetRange("Package No.", PackageNoInformation."Barcode Text FLX");
        CoilPackageNoInformation.FindFirst();

        //CoilPackageNoInformation.TestField("Quality Control Status FLX", CoilPackageNoInformation."Quality Control Status FLX"::Accept);

        if PackageNoInformation."Remove Package FLX" then begin
            RemovePaletteChecks(CoilPackageNoInformation, PackageNoInformation);

            CoilPackageNoInformation.Validate("Parent Package No. FLX", '');
            CoilPackageNoInformation.Modify(true);

            CoilPackageNoInformation.Reset();
            CoilPackageNoInformation.SetRange("Parent Package No. FLX", PackageNoInformation."Package No.");
            if CoilPackageNoInformation.IsEmpty() then
                PackageNoInformation."Produced At FLX" := 0DT;
        end
        else begin
            AddPaletteChecks(CoilPackageNoInformation, PackageNoInformation);

            CoilPackageNoInformation."Parent Package No. FLX" := PackageNoInformation."Package No.";
            CoilPackageNoInformation.Modify(true);

            PackageNoInformation."Sell-to Customer No. FLX" := CoilPackageNoInformation."Sell-to Customer No. FLX";
            PackageNoInformation."Sell-to Customer Name FLX" := CoilPackageNoInformation."Sell-to Customer Name FLX";
            PackageNoInformation."Ship-to Code FLX" := CoilPackageNoInformation."Ship-to Code FLX";

            CoilPackageNoInformation.CalcFields("Location Code FLX");
            PackageNoInformation."Parent Package Loc. Code FLX" := CoilPackageNoInformation."Location Code FLX";
            PackageNoInformation."Parent Package Bin Code FLX" := FlexatiProductionMngt.GetBinCodeFromPackageNo(CoilPackageNoInformation."Package No.");

            PackageNoInformation."Produced At FLX" := CurrentDateTime();
        end;

        PackageNoInformation."Barcode Text FLX" := '';
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Release Sales Document", OnBeforePerformManualReleaseProcedure, '', false, false)]
    local procedure OnBeforePerformManualReleaseProcedure(var SalesHeader: Record "Sales Header"; PreviewMode: Boolean; var IsHandled: Boolean)
    begin
        BinCodeCheckOnBeforeSalesOrderRelease(SalesHeader);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeConfirmDeletion, '', false, false)]
    local procedure "Sales Header_OnBeforeConfirmDeletion"(var SalesHeader: Record "Sales Header"; var Result: Boolean; var IsHandled: Boolean)
    begin
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeCheckNoAndShowConfirm, '', false, false)]
    local procedure Sales_Header_OnBeforeCheckNoAndShowConfirm(SalesHeader: Record "Sales Header"; var SalesShptHeader: Record "Sales Shipment Header"; var SalesInvHeader: Record "Sales Invoice Header"; var SalesCrMemoHeader: Record "Sales Cr.Memo Header"; var ReturnRcptHeader: Record "Return Receipt Header"; var SalesInvHeaderPrePmt: Record "Sales Invoice Header"; var SalesCrMemoHeaderPrePmt: Record "Sales Cr.Memo Header"; SourceCode: Record "Source Code"; var Result: Boolean; var IsHandled: Boolean)
    var
        CombinedShipmentHeader: Record "Combined Shipment Header FLX";
    begin
        CombinedShipmentHeader.SetRange("Sales Invoice No.", SalesHeader."No.");
        if CombinedShipmentHeader.FindFirst() then begin
            CombinedShipmentHeader."Sales Invoice No." := '';
            CombinedShipmentHeader.Modify(true);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnBeforePostCommitSalesDoc, '', false, false)]
    local procedure "Sales-Post_OnBeforePostCommitSalesDoc"(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PreviewMode: Boolean; var ModifyHeader: Boolean; var CommitIsSuppressed: Boolean; var TempSalesLineGlobal: Record "Sales Line" temporary)
    begin
        CommitIsSuppressed := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnBeforeSelectMultipleItems, '', false, false)]
    local procedure "Purchase Line_OnBeforeSelectMultipleItems"(var PurchaseLine: Record "Purchase Line"; var IsHandled: Boolean)
    var
        Item: Record Item;
    begin
        IsHandled := true;

        Item.SetRange("Purchasing Blocked", false);
        Item.SetRange("Vendor No.", PurchaseLine."Buy-from Vendor No.");
        PurchaseLine.AddItems(SelectInItemList(Item));
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnBeforeSelectMultipleItems, '', false, false)]
    local procedure "Sales Line_OnBeforeSelectMultipleItems"(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    var
        Item: Record Item;
    begin
        IsHandled := true;

        Item.SetRange("Sales Blocked", false);
        Item.SetRange("Vendor No.", SalesLine."Sell-to Customer No.");
        SalesLine.AddItems(SelectInItemList(Item));
    end;

    procedure SelectInItemList(var Item: Record Item): Text
    var
        ItemListPage: Page "Item List";
    begin
        Item.SetRange(Blocked, false);
        ItemListPage.SetTableView(Item);
        ItemListPage.LookupMode(true);
        if ItemListPage.RunModal() = Action::LookupOK then
            exit(ItemListPage.GetSelectionFilter());
    end;

    var
        FlexatiSetup: Record "Flexati Setup FLX";
        ConfirmManagement: Codeunit "Confirm Management";
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
        NoSeries: Codeunit "No. Series";
        PageManagement: Codeunit "Page Management";
        QualityControlManagement: Codeunit "Quality Control Management FLX";
}

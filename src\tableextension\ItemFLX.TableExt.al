tableextension 60008 "Item FLX" extends Item
{
    fields
    {
        field(60000; "ID (mm) FLX"; Decimal)
        {
            Caption = 'ID (mm)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the ID (mm) field.';
        }
        field(60001; "Production Output Location FLX"; Code[10])
        {
            Caption = 'Production Output Location';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Production Output Location field.';
        }
        field(60002; "OD (mm) FLX"; Decimal)
        {
            Caption = 'OD (mm)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the OD (mm) field.';
        }
        field(60003; "WP (bar) FLX"; Decimal)
        {
            Caption = 'WP (bar)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the WP (bar) field.';
        }
        field(60004; "BP (bar) FLX"; Decimal)
        {
            Caption = 'BP (bar)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the BP (bar) field.';
        }
        field(60005; "Production Output Bin Code FLX"; Code[20])
        {
            Caption = 'Production Output Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Production Output Location FLX"));
            ToolTip = 'Specifies the value of the Production Output Bin Code field.';
        }
        field(60006; "Production BOM Status FLX"; Enum "BOM Status")
        {
            Caption = 'Production BOM Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Production BOM Header".Status where("No." = field("Production BOM No.")));
            AllowInCustomizations = Always;
        }
        field(60007; "Routing Status FLX"; Enum "Routing Status")
        {
            Caption = 'Routing Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Routing Header".Status where("No." = field("Routing No.")));
            AllowInCustomizations = Always;
        }
        field(60008; "Hide Dlg for Calc Std Cost FLX"; Boolean)
        {
            Caption = 'Hide Dialog for Calc. Standard Cost';
            AllowInCustomizations = Never;
        }
        field(60009; "Notes FLX"; Text[250])
        {
            Caption = 'Notes';
            ToolTip = 'Specifies the value of the Notes field.';
        }
        field(60010; "Customer No. FLX"; Code[20])
        {
            Caption = 'Customer No.';
            ToolTip = 'Specifies the value of the Customer No. field.';
            TableRelation = Customer."No.";
        }
        // field(60011; "WP FLX"; Integer)
        // {
        //     Caption = 'WP';
        //     ToolTip = 'Specifies the value of the WP field.';
        // }
        // field(60012; "ID FLX"; Decimal)
        // {
        //     Caption = 'ID';
        // }
        // field(60022; "OD FLX"; Decimal)
        // {
        //     Caption = 'OD';
        // }
        field(60013; "Type FLX"; Code[50])
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(60014; "Cat FLX"; Code[50])
        {
            Caption = 'Cat';
            ToolTip = 'Specifies the value of the Cat field.';
        }
        field(60015; "Col FLX"; Code[50])
        {
            Caption = 'Col';
            ToolTip = 'Specifies the value of the Col field.';
        }
        field(60016; "Tube FLX"; Code[50])
        {
            Caption = 'Tube';
            ToolTip = 'Specifies the value of the Tube field.';
        }
        field(60017; "Cover FLX"; Code[50])
        {
            Caption = 'Cover';
            ToolTip = 'Specifies the value of the Cover field.';
        }
        field(60018; "Info FLX"; Text[250])
        {
            Caption = 'Info';
            ToolTip = 'Specifies the value of the Info field.';
        }
        field(60019; "F FLX"; Decimal)
        {
            Caption = 'F';
            ToolTip = 'Specifies the value of the F field.';
        }
        field(60020; "LD FLX"; Decimal)
        {
            Caption = 'LD';
            ToolTip = 'Specifies the value of the LD field.';
        }
        field(60021; "Width FLX"; Decimal)
        {
            Caption = 'Width';
            ToolTip = 'Specifies the value of the Width field.';
        }
        // field(60023; "BP FLX"; Integer)
        // {
        //     Caption = 'BP';
        // }

    }
}

// Name	Type	Values	Blocked
// WP	Integer		No
// ID	Decimal		No
// Type	Option	D,SD,CH,C,FC,SR,RH,	No
// Cat	Option	Black Rubber Suction,Tank Truck,Oilfield,Blender,Chemical,Hot Water,Concrete,SandBlast,Food,Air,Water,Oil / Fuel,Steam,Hot Air,Wire Concrete,Rotary,Spiral,Wire Compresor,Wire Steam,	No
// Col	Option	Black,Blue,Green,Red,Brown,Yellow,Gray,Beige,White,Orange,	No
// OD	Decimal		No
// BP	Integer		No
// Tube	Option	ACM,BIIR,BR,CIIR,CM,CR,CSM,EAM,ECO,EPDM,EVA,FKM,HNBR,IIR,IR,NBR,NR,NVC,SBR,VMQ,NBR/SBR,UHMW-PE,NR/SBR,EPM,	No
// Cover	Option	ACM,BIIR,BR,CIIR,CM,CR,CSM,EAM,ECO,EPDM,EVA,FKM,HNBR,IIR,IR,NBR,NR,NVC,SBR,VMQ,Cam Elyaf,,NR/SBR,UHMW-PE	No
// Info	Text		No
// F	Decimal		No
// LD	Decimal		No
// Width	Decimal		No
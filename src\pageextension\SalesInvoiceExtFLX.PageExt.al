pageextension 60014 "Sales Invoice Ext. FLX" extends "Sales Invoice"
{
    actions
    {
        addafter("&Invoice")
        {
            action("Sbuf Report FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Sbuf Report';
                Image = Print;
                ToolTip = 'Sbuf Report.';

                trigger OnAction()
                var
                    SalesHd: Record "Sales Header";
                begin
                    SalesHd.SetRange("No.", Rec."No.");
                    Report.RunModal(Report::"Sbuf Report FLX", true, false, SalesHd);
                end;
            }
        }
    }
}
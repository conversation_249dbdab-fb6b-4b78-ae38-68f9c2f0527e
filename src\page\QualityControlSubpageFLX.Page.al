page 60015 "Quality Control Subpage FLX"
{
    ApplicationArea = All;
    Caption = 'Quality Control Subpage';
    PageType = ListPart;
    SourceTable = "Quality Control Line FLX";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Quality Control Status"; Rec."Quality Control Status")
                {
                }
                field("Reject Reason Code"; Rec."Reject Reason Code")
                {
                    Editable = Rec."Quality Control Status" = Rec."Quality Control Status"::Reject;
                    ShowMandatory = Rec."Quality Control Status" = Rec."Quality Control Status"::Reject;
                }
            }
        }
    }
}
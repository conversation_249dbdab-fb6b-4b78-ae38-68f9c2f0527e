{"name": "Company rule set", "description": "These rules must be respected by all the AL code written within the company.", "rules": [{"id": "AA0072", "action": "None", "justification": "To improve readability the name of variables and parameters must be suffixed with the type or object name."}, {"id": "AA0232", "action": "None", "justification": "You can potentially increase performance if fields that are used in FlowFields are added to SumIndexedFields of the corresponding key."}, {"id": "AA0137", "action": "Error", "justification": "Justification"}, {"id": "AA0074", "action": "Error", "justification": "Justification"}, {"id": "AA0205", "action": "Error", "justification": "Justification"}, {"id": "AA0008", "action": "Error", "justification": "Justification"}, {"id": "LC0051", "action": "None", "justification": "Justification"}, {"id": "AA0139", "action": "Error", "justification": "Justification"}, {"id": "AA0021", "action": "Error", "justification": "Justification"}, {"id": "AA0013", "action": "Error", "justification": "Justification"}, {"id": "AA0206", "action": "Error", "justification": "Justification"}, {"id": "AA0233", "action": "Error", "justification": "Justification"}, {"id": "AA0181", "action": "Error", "justification": "Justification"}, {"id": "AA0175", "action": "Error", "justification": "Justification"}, {"id": "AA0005", "action": "Error", "justification": "Justification"}, {"id": "AA0073", "action": "Error", "justification": "Justification"}, {"id": "LC0040", "action": "Error", "justification": "Justification"}, {"id": "LC0015", "action": "Error", "justification": "Justification"}, {"id": "LC0016", "action": "Error", "justification": "Justification"}, {"id": "AL0603", "action": "Error", "justification": "Justification"}, {"id": "AA0225", "action": "Error", "justification": "Justification"}, {"id": "AA0470", "action": "Error", "justification": "Justification"}, {"id": "AA0228", "action": "Error", "justification": "Justification"}, {"id": "LC0024", "action": "Error", "justification": "Justification"}, {"id": "AA0204", "action": "Error", "justification": "Justification"}, {"id": "AL0432", "action": "Error", "justification": "Justification"}, {"id": "AA0198", "action": "Error", "justification": "Justification"}, {"id": "AL0755", "action": "Error", "justification": "Justification"}, {"id": "AA0244", "action": "Error", "justification": "Justification"}, {"id": "AL0606", "action": "Error", "justification": "Justification"}, {"id": "AW0006", "action": "Error", "justification": "Justification"}, {"id": "LC0048", "action": "Error", "justification": "Justification"}, {"id": "LC0045", "action": "Error", "justification": "Justification"}, {"id": "LC0026", "action": "Error", "justification": "Justification"}, {"id": "LC0010", "action": "None", "justification": "Justification"}, {"id": "AA0215", "action": "Error", "justification": "Justification"}, {"id": "AL0789", "action": "Error", "justification": "Justification"}, {"id": "AL0424", "action": "Error", "justification": "Justification"}, {"id": "LC0036", "action": "Error", "justification": "Justification"}, {"id": "LC0001", "action": "Error", "justification": "Justification"}, {"id": "LC0013", "action": "Error", "justification": "Justification"}, {"id": "LC0035", "action": "Warning", "justification": "Justification"}, {"id": "LC0004", "action": "Error", "justification": "Justification"}, {"id": "LC0041", "action": "Error", "justification": "Justification"}, {"id": "LC0027", "action": "Error", "justification": "Justification"}, {"id": "LC0068", "action": "None", "justification": "Justification"}, {"id": "LC0064", "action": "Error", "justification": "Justification"}, {"id": "AA0218", "action": "Error", "justification": "Justification"}, {"id": "LC0069", "action": "Error", "justification": "Justification"}, {"id": "LC0066", "action": "Error", "justification": "Justification"}, {"id": "AW0008", "action": "Error", "justification": "Justification"}, {"id": "AW0003", "action": "Error", "justification": "Justification"}, {"id": "LC0005", "action": "Error", "justification": "Justification"}, {"id": "LC0075", "action": "Error", "justification": "Justification"}, {"id": "LC0078", "action": "Error", "justification": "Justification"}, {"id": "LC0077", "action": "Error", "justification": "Justification"}, {"id": "LC0083", "action": "None", "justification": "Justification"}, {"id": "LC0084", "action": "None", "justification": "Justification"}, {"id": "LC0002", "action": "Error", "justification": "Justification"}, {"id": "LC0023", "action": "None", "justification": "Justification"}, {"id": "LC0086", "action": "Error", "justification": "Justification"}, {"id": "LC0085", "action": "Error", "justification": "Justification"}, {"id": "LC0090", "action": "None", "justification": "Justification"}, {"id": "LC0091", "action": "None", "justification": "Justification"}]}
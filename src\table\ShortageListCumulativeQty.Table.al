table 60015 "Shortage List - Cumulative Qty"
{
    Caption = 'Shortage List - Cumulative Qty';
    DataClassification = ToBeClassified;
    LookupPageId = "Shortage List - Cumulative Qty";
    DrillDownPageId = "Shortage List - Cumulative Qty";

    fields
    {
        field(1; "Document Date"; Date)
        {
            Caption = 'Document Date';
            ToolTip = 'Specifies the value of the Document Date field.';
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(3; "Positive Qty."; Decimal)
        {
            Caption = 'Positive Qty.';
            ToolTip = 'Specifies the value of the Positive Qty. field.';
        }
        field(4; "Negative Qty."; Decimal)
        {
            Caption = 'Negative Qty.';
            ToolTip = 'Specifies the value of the Negative Qty. field.';
        }
        field(5; "Net Qty."; Decimal)
        {
            Caption = 'Net Qty.';
            ToolTip = 'Specifies the value of the Net Qty. field.';
        }
        field(6; "Cumulative Net Qty."; Decimal)
        {
            Caption = 'Cumulative Net Qty.';
            ToolTip = 'Specifies the value of the Cumulative Net Qty. field.';
        }
        field(7; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(8; "User Id"; Code[50])
        {
            Caption = 'User Id';
            AllowInCustomizations = Always;
        }
        field(9; "Real Consump Qty"; Decimal)
        {
            Caption = 'Real Consump Qty';
            ToolTip = 'Specifies the value of the Real Consump Qty field.';
        }
        field(10; "Item Description 2"; Text[100])
        {
            Caption = 'Item Description 2';
            AllowInCustomizations = Always;
        }
    }
    keys
    {
        key(PK; "Document Date", "Item No.", "User Id")
        {
            Clustered = true;
        }
        key(Key2; "Item No.", "Document Date")
        {
        }
    }
}
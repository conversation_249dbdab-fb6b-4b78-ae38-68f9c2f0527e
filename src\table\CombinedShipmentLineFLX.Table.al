table 60007 "Combined Shipment Line FLX"
{
    Caption = 'Combined Shipment Line';

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(3; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }
        field(4; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
            ToolTip = 'Specifies the value of the Source Document Line No. field.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(6; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(8; "Quantity (Base)"; Decimal)
        {
            Caption = 'Quantity (Base)';
            ToolTip = 'Specifies the value of the Quantity (Base) field.';
        }
        field(9; "Qty. Shipped (Base)"; Decimal)
        {
            Caption = 'Qty. Shipped (Base)';
            ToolTip = 'Specifies the value of the Qty. Shipped (Base) field.';
        }
        field(10; "Package Count"; Integer)
        {
            Caption = 'Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("CombinedShipmentLineDtl FLX" where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Package Count field.';
        }
        field(11; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(13; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(14; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
            ToolTip = 'Specifies the value of the Bin Code field.';
            trigger OnValidate()
            var
                SalesLine: Record "Sales Line";
            //BinCodeErr: Label 'You need to delete read packages before changing Bin Code';
            begin
                if xRec."Bin Code" = '' then
                    exit;

                // Rec.CalcFields("Line Package Count");
                // if Rec."Line Package Count" <> 0 then
                //     Error(BinCodeErr);

                if Rec."Bin Code" = xRec."Bin Code" then
                    exit;

                SalesLine.Get(SalesLine."Document Type"::Order, Rec."Source Document No.", Rec."Source Document Line No.");
                SalesLine.Validate("Bin Code", Rec."Bin Code");
                SalesLine.Modify(true);
            end;
        }
        field(15; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            AllowInCustomizations = Always;
        }
        field(16; "Qty. to Ship (Base)"; Decimal)
        {
            Caption = 'Qty. to Ship (Base)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("CombinedShipmentLineDtl FLX".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Qty. to Ship (Base) field.';
        }
        // field(17; "Outstanding Qty. (Base)"; Decimal)
        // {
        //     Caption = 'Outstanding Qty. (Base)';
        // }
        field(17; "Outstanding Qty. (Base)"; Decimal)
        {
            Caption = 'Outstanding Qty. (Base)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Outstanding Qty. (Base)" where("Document Type" = const(Order), "Document No." = field("Source Document No."), "Line No." = field("Source Document Line No.")));
            ToolTip = 'Specifies the value of the Outstanding Qty. (Base) field.';
        }
        field(12; "Ship-to Code"; Code[10])
        {
            Caption = 'Ship-to Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(18; "Unit Price"; Decimal)
        {
            Caption = 'Unit Price';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Unit Price" where("Document Type" = const(Order), "Document No." = field("Source Document No."), "Line No." = field("Source Document Line No.")));
            ToolTip = 'Specifies the value of the Unit Price field.';
        }
    }

    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; "Source Document No.", "Source Document Line No.", "Item No.", "Variant Code")
        {
        }
    }
    trigger OnInsert()
    var
        CombinedShipmentLine: Record "Combined Shipment Line FLX";
    begin
        CombinedShipmentLine.SetRange("Document No.", Rec."Document No.");
        if CombinedShipmentLine.FindLast() then
            Rec."Line No." := CombinedShipmentLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}
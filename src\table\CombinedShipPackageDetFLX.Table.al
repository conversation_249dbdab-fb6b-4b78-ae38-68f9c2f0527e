/// <summary>
/// Table Combined Ship Package Det. FLX (ID 60013).
/// </summary>
table 60013 "Combined Ship Package Det. FLX"
{
    Caption = 'Combined Ship Package Det. FLX';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Package No."; Code[50])
        {
            Caption = 'Package No.';
        }
        //field(2; "Package Type"; Code[20])
        //{
        //    Caption = 'Package Type';
        //}
        field(2; "Package Type"; Enum "Package Type FLX")
        {
            Caption = 'Package Type';
            Editable = false;
        }
        field(3; "Entry No"; Integer)
        {
            Caption = 'Entry No';
        }
        field(4; Type; Enum "Package Item Type FLX")
        {
            Caption = 'Type';
        }
        field(5; "No."; Code[20])
        {
            Caption = 'No.';
        }
        field(6; Description; Text[100])
        {
            Caption = 'Description';
        }
        field(7; "Unit of Measure"; Code[10])
        {
            Caption = 'Unit of Measure';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
        }
        field(9; "Shipped Quantity"; Decimal)
        {
            Caption = 'Shipped Quantity';
        }
        field(10; "Outstanding Quantity"; Decimal)
        {
            Caption = 'Outstanding Quantity';
        }
        field(11; "Ship-to Quantity"; Decimal)
        {
            Caption = 'Ship-to Quantity';
        }
        field(12; "Ship-to Code"; Code[20])
        {
            Caption = 'Ship-to Code';
        }
        field(13; "Ship-to Name"; Text[100])
        {
            Caption = 'Ship-to Name';
        }
        field(14; "Coil Lenght"; Decimal)
        {
            Caption = 'Coil Lenght';
        }
        field(15; "Coil Quantity"; Decimal)
        {
            Caption = 'Coil Quantity';
        }
        field(16; "Line Weight"; Decimal)
        {
            Caption = 'Line Weight';
        }
        field(17; "Outstanding Quantity (Base)"; Decimal)
        {
            Caption = 'Outstanding Quantity (Base)';
        }
        field(18; "Quantity per UoM"; Decimal)
        {
            Caption = 'Quantity per UoM';
        }
        field(19; Processed; Boolean)
        {
            Caption = 'Processed';
        }
        field(20; "Source Document No."; Code[50])
        {
            Caption = 'Source Document No.';
        }
        field(21; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
        }
        field(22; "Combined Shipment No."; Code[20])
        {
            Caption = 'Combined Shipment No.';
        }
        field(23; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
        }
        field(24; "Serial No."; Code[50])
        {
            Caption = 'Serial No.';
        }
        field(25; "Expiration Date"; Date)
        {
            Caption = 'Expiration Date';
        }
        field(26; "Transfer-from Package No."; Code[50])
        {
            Caption = 'Transfer-from Package No.';
        }
        field(27; Piece; Decimal)
        {
            Caption = 'Piece';
        }
        field(28; "Hose Length"; Decimal)
        {
            Caption = 'Hose Length';
        }
        field(29; "Pallet Code"; Code[20])
        {
            Caption = 'Pallet Code';
        }
        field(30; "Tare Weight"; Decimal)
        {
            Caption = 'Tare Weight';
        }
        field(31; "Coil Weight"; Decimal)
        {
            Caption = 'Coil Weight';
        }
        field(32; "Pallet Line Number"; Text[100])
        {
            Caption = 'Pallet Line Number';
        }
        field(33; "Source OrderLine No."; Integer)
        {
            Caption = 'Source OrderLine No.';
        }
        field(34; "Pallet Description"; Text[100])
        {
            Caption = 'Pallet Description';
        }
        field(35; "Your Reference"; Text[50])
        {
            Caption = 'Your Reference';
        }
        field(36; "Shipment No FLX"; Code[20])
        {
            Caption = 'Shipment No';
            Editable = true;
        }
    }
    keys
    {
        key(PK; "Package No.", "Package Type", "Entry No")
        //key(PK; "Package No.", "Entry No")
        {
            Clustered = true;
        }
        key(key2; "Pallet Line Number", "No.", "Hose Length")
        {
        }
    }
}
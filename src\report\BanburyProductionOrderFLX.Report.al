report 60013 "Banbury Production Order FLX"
{
    ApplicationArea = All;
    Caption = 'Banbury Production Order';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(ProductionOrder; "Production Order")
        {
            column(No; "No.")
            {
            }
            column(SourceNo; "Source No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            dataitem("Prod. Order Line"; "Prod. Order Line")
            {
                DataItemLink = Status = field("Status"), "Prod. Order No." = field("No.");

                column(ProductionBOMVersionCode_ProdOrderLine; "Production BOM Version Code")
                {
                }
                column(RoutingVersionCode_ProdOrderLine; "Routing Version Code")
                {
                }
                column(RoutingNo_ProdOrderLine; "Routing No.")
                {
                }
                column(ProductionOrderNoQRCode; ProductionOrderNoQRCode)
                {
                }
                column(RemainingQuantity_ProdOrderLine; "Remaining Quantity")
                {
                }
            }
            trigger OnAfterGetRecord()
            var
                //Item: Record "Item";
                BarcodeString: Text;
                //BarcodeFontProvider: Interface "Barcode Font Provider";
                BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";

            begin
                // Declare the barcode provider using the barcode provider interface and enum
                //BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;

                // Item.SetLoadFields(Item.Description);
                // Item.Get("Item No.");
                // Description := Item.Description;

                // Set data string source 
                if ProductionOrder."No." <> '' then begin
                    BarcodeString := ProductionOrder."No.";
                    // Validate the input
                    //BarcodeFontProvider.ValidateInput(BarcodeString, BarcodeSymbology);
                    // Encode the data string to the barcode font
                    //SerialNoCode := BarcodeFontProvider.EncodeFont(BarcodeString, BarcodeSymbology);
                    ProductionOrderNoQRCode := BarcodeFontProvider2D.EncodeFont(BarcodeString, BarcodeSymbology2D);
                end
            end;

        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
    trigger OnInitReport()
    begin
        //BarcodeSymbology := Enum::"Barcode Symbology"::Code39;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
    end;

    var
        //BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        //SerialNoCode: Text;
        ProductionOrderNoQRCode: Text;

}
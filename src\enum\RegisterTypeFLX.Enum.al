enum 60008 "Register Type FLX"
{
    Extensible = true;
    value(0; " ")
    {
        Caption = ' ', Locked = true;
    }
    value(1; "On Hand")
    {
        Caption = 'On Hand';
    }
    value(2; Purchase)
    {
        Caption = 'Purchase';
    }
    value(3; Consuption)
    {
        Caption = 'Consuptionh';
    }
    value(4; Output)
    {
        Caption = 'Output';
    }
    value(5; Sale)
    {
        Caption = 'Sale';
    }
    value(6; RealConsump)
    {
        Caption = 'RealConsump';
    }
}
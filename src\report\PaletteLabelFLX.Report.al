report 60009 "Palette Label FLX"
{
    Caption = 'Palette Label';
    UsageCategory = None;
    dataset
    {
        dataitem(PalettePackageNoInformation; "Package No. Information")
        {
            CalcFields = Inventory;
            column(IDmmFLX_PalettePackageNoInformation; "ID (mm) FLX")
            {
            }
            column(ODmmFLX_PalettePackageNoInformation; "OD (mm) FLX")
            {
            }
            column(ProductionOrderNoFLX_PalettePackageNoInformation; "Production Order No. FLX")
            {
            }
            column(SalesOrderNoFLX_PalettePackageNoInformation; PalettePackageNoInformation."Sales Order No. FLX")
            {
            }
            column(BPbarFLX_PalettePackageNoInformation; "BP (bar) FLX")
            {
            }
            column(WPbarFLX_PalettePackageNoInformation; "WP (bar) FLX")
            {
            }
            column(YourReferenceFLX_PalettePackageNoInformation; "Your Reference FLX")
            {
            }
            column(DeliveryNo_PalettePackageNoInformation; PaletDeliveryNo)
            {
            }
            column(BarcodeDeliveryNo_PalettePackageNoInformation; BarcodePaletDeliveryNo)
            {
            }
            column(SupplierNo_PalettePackageNoInformation; SupplierNo)
            {
            }
            column(BarcodeSupplierNo_PalettePackageNoInformation; BarcodeSupplierNo)
            {
            }
            column(SelltoCustomerNameFLX_PalettePackageNoInformation; "Sell-to Customer Name FLX")
            {
            }
            column(Today_PalettePackageNoInformation; Format(Today()))
            {
            }
            column(Description_PalettePackageNoInformation; Description)
            {
            }
            column(TotalHoseLenghtFLX_PalettePackageNoInformation; TotalHoseLength)
            {
            }
            column(TotalWeight_PalettePackageNoInformation; TotalWeight)
            {
            }
            column(LabelLenghtFLX_PalettePackageNoInformation; "Label Lenght FLX")
            {
            }
            column(PackageNo_PalettePackageNoInformation; "Package No.")
            {
            }
            column(ItemNo_PalettePackageNoInformation; "Item No.")
            {
            }
            column(Inventory_PalettePackageNoInformation; Inventory)
            {
            }
            column(SalesOrderLineNoFLX_PalettePackageNoInformation; "Sales Order Line No. FLX")
            {
            }
            column(QrCode_PalettePackageNoInformation; QrCode)
            {
            }
            column(ProductionOrderLineNoFLX_PalettePackageNoInformation; "Production Order Line No. FLX")
            {
            }
            column(Ship_to_Code_FLX_PalettePackageNoInformation; "Ship-to Code FLX")
            {
            }
            dataitem("Package No. Information"; "Package No. Information")
            {
                //DataItemLink = "Package No." = field("Parent Package No. FLX");
                DataItemLink = "Parent Package No. FLX" = field("Package No.");
                CalcFields = Inventory;
                column(IDmmFLX_PackageNoInformation; "ID (mm) FLX")
                {
                }
                column(ODmmFLX_PackageNoInformation; "OD (mm) FLX")
                {
                }
                column(PosNo_PackageNoInformation; PosNo)
                {
                }
                column(ProductionOrderNoFLX; "Production Order No. FLX")
                {
                }
                column(SalesOrderNoFLX_SubPalet; "Sales Order No. FLX")
                {
                }
                column(SalesOrderLineNoFLX_PackageNoInformation; "Sales Order Line No. FLX")
                {
                }
                column(ProductionOrderLineNoFLX_PackageNoInformation; "Production Order Line No. FLX")
                {
                }
                column(BPbarFLX_PackageNoInformation; "BP (bar) FLX")
                {
                }
                column(WPbarFLX_PackageNoInformation; "WP (bar) FLX")
                {
                }
                column(SelltoCustomerNameFLX; "Sell-to Customer Name FLX")
                {
                }
                column(SelltoCustomerRegisterName; CustRegisterName)
                {
                }
                column(Description; Description)
                {
                }
                column(Description2; ItemDesc2)
                {
                }
                column(CustLabelDesc; CustLabelDesc)
                {

                }
                column(HoseLenghtFLX; "Hose Lenght FLX")
                {
                }
                column(LabelLenghtFLX; "Label Lenght FLX")
                {
                }
                column(PackageNo; "Package No.")
                {
                }
                column(YourReferenceFLX_PackageNoInformation; "Your Reference FLX")
                {
                }
                column(ItemNo_PackageNoInformation; "Item No.")
                {
                }
                column(ItemSearchDescPalet_PackageNoInformation; ItemSearchDescPalet)
                {
                }
                column(BarcodeItemSearchDescPalet_PackageNoInformation; BarcodeItemSearchDescPalet)
                {
                }
                column(ItemSearchDesc_PackageNoInformation; ItemSearchDesc)
                {
                }
                column(BarcodeItemSearchDesc_PackageNoInformation; BarcodeItemSearchDesc)
                {
                }
                column(Inventory_PackageNoInformation; Inventory)
                {
                }
                column(BarcodePackageNo_PackageNoInformation; BarcodePackageNo)
                {
                }
                column(BarcodePO_PackageNoInformation; BarcodePO)
                {
                }
                column(BarcodeDeliveryNo_PackageNoInformation; BarcodeDeliveryNo)
                {
                }
                column(DeliveryNo_PackageNoInformation; DeliveryNo)
                {
                }
                column(BarcodeItemCrossRef_PackageNoInformation; BarcodeItemCrossRef)
                {
                }
                column(BarcodeQty_PackageNoInformation; BarcodeQty)
                {
                }
                column(BarcodeQtyLabel_PackageNoInformation; BarcodeQtyLabel)
                {
                }
                column(Ship_to_Code_FLX; "Ship-to Code FLX")
                {
                }
                column(LotNo_PackageNoInformation; "Lot No. FLX")
                {
                }
                trigger OnAfterGetRecord()
                begin
                    GetCustSubLabelInfo();
                    GenerateCustSubLabelQRCode();
                end;
            }
            trigger OnAfterGetRecord()
            var
                FlexSalesMng: Codeunit "Flexati Sales Management FLX";
            begin
                TotalHoseLength := 0;
                if PalettePackageNoInformation."Scaled Weight FLX" > 0 then
                    TotalWeight := PalettePackageNoInformation."Scaled Weight FLX"
                else
                    TotalWeight := FlexSalesMng.CalculatePackageWeight(PalettePackageNoInformation);
                GenerateQRCode();
            end;
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(processing)
    //         {
    //         }
    //     }
    // }
    local procedure GenerateQRCode()
    var
        Item: Record Item;
        SalesShipmentHd: Record "Sales Shipment Header";
        SalesShipmentLine: Record "Sales Shipment Line";
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        BarcodeFontProvider: Interface "Barcode Font Provider";
        BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
    begin
        BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
        QrCode := BarcodeFontProvider2D.EncodeFont(PalettePackageNoInformation."Package No.", BarcodeSymbology2D);
        PaletDeliveryNo := PalettePackageNoInformation."Shipment No FLX";
        ItemSearchDescPalet := ItemSearchDesc;
        if Item.Get(PalettePackageNoInformation."Item No.") then
            ItemSearchDescPalet := Item."Search Description";

        Cust.Get(PalettePackageNoInformation."Sell-to Customer No. FLX");
        SupplierNo := Cust."Supplier Number FLX";

        if PaletDeliveryNo = '' then begin
            SalesShipmentLine.Reset();
            SalesShipmentLine.SetRange("Order No.", "Package No. Information"."Sales Order No. FLX");
            SalesShipmentLine.SetRange("Order Line No.", "Package No. Information"."Sales Order Line No. FLX");
            if SalesShipmentLine.FindLast() then begin
                SalesShipmentHd.Get(SalesShipmentLine."Document No.");
                PaletDeliveryNo := SalesShipmentHd."External Document No.";
            end;
        end;
        BarcodePaletDeliveryNo := PaletDeliveryNo;
        BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
        BarcodeSymbology := Enum::"Barcode Symbology"::Code39;
        BarcodePaletDeliveryNo := BarcodeFontProvider.EncodeFont(PaletDeliveryNo, BarcodeSymbology);
        BarcodeItemSearchDescPalet := BarcodeFontProvider.EncodeFont(ItemSearchDescPalet, BarcodeSymbology);
    end;

    local procedure GenerateCustSubLabelQRCode()
    var
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeFontProvider: Interface "Barcode Font Provider";
    begin
        BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
        BarcodeSymbology := Enum::"Barcode Symbology"::Code39;
        BarcodeQty := BarcodeFontProvider.EncodeFont(Format("Package No. Information"."Hose Lenght FLX"), BarcodeSymbology);
        BarcodeQtyLabel := 'Qty / Qte ' + Format("Package No. Information"."Hose Lenght FLX") + ' FT';
        BarcodePO := BarcodeFontProvider.EncodeFont("Package No. Information"."Your Reference FLX", BarcodeSymbology);
        BarcodeDeliveryNo := BarcodeFontProvider.EncodeFont(DeliveryNo, BarcodeSymbology);
        BarcodeSupplierNo := BarcodeFontProvider.EncodeFont(SupplierNo, BarcodeSymbology);
        BarcodePackageNo := BarcodeFontProvider.EncodeFont("Package No. Information"."Package No.", BarcodeSymbology);
        BarcodeItemCrossRef := BarcodeFontProvider.EncodeFont(ItemCrossRef, BarcodeSymbology);
        // EAN 13 barkodlar
        BarcodeSymbology := Enum::"Barcode Symbology"::"EAN-13";
        BarcodeItemSearchDesc := BarcodeFontProvider.EncodeFont(ItemSearchDesc, BarcodeSymbology);
        PosNo += 10;
    end;

    local procedure GetCustSubLabelInfo()
    var
        Item: Record Item;
        ItemCrossReference: Record "Item Reference";
        SalesShipmentHd: Record "Sales Shipment Header";
        SalesShipmentLine: Record "Sales Shipment Line";
        SalesLine: Record "Sales Line";
    begin
        Item.Get("Package No. Information"."Item No.");
        ItemSearchDesc := Item."Search Description";
        ItemDesc2 := Item."Description 2";
        CustLabelDesc := "Package No. Information".Description;
        if ("Package No. Information"."Sales Order No. FLX" <> '') and ("Package No. Information"."Sales Order Line No. FLX" > 0) then begin
            SalesLine.Reset();
            SalesLine.SetRange("Document No.", "Package No. Information"."Sales Order No. FLX");
            SalesLine.SetRange("Line No.", "Package No. Information"."Sales Order Line No. FLX");
            if SalesLine.FindFirst() then
                CustLabelDesc := SalesLine.Description;
        end;

        //TotalWeight += Round("Package No. Information"."Hose Lenght FLX" * FlexSalesMng.CalculateCoilWeightFromItemNo("Package No. Information"."Item No."), 0.01);
        TotalHoseLength += "Package No. Information"."Hose Lenght FLX";
        Cust.Get("Package No. Information"."Sell-to Customer No. FLX");
        CustRegisterName := Cust."Registration Name INF";
        if SupplierNo = '' then
            SupplierNo := Cust."Supplier Number FLX";

        if CustRegisterName = '' then
            CustRegisterName := "Package No. Information"."Sell-to Customer Name FLX";

        DeliveryNo := "Package No. Information"."Shipment No FLX";
        if DeliveryNo = '' then begin
            SalesShipmentLine.Reset();
            SalesShipmentLine.SetRange("Order No.", "Package No. Information"."Sales Order No. FLX");
            SalesShipmentLine.SetRange("Order Line No.", "Package No. Information"."Sales Order Line No. FLX");
            if SalesShipmentLine.FindLast() then begin
                SalesShipmentHd.Get(SalesShipmentLine."Document No.");
                DeliveryNo := SalesShipmentHd."External Document No.";
            end;
        end;
        ItemCrossRef := '';
        ItemCrossReference.Reset();
        ItemCrossReference.SetRange("Item No.", "Package No. Information"."Item No.");
        ItemCrossReference.SetRange("Reference Type", ItemCrossReference."Reference Type"::"Bar Code");
        if ItemCrossReference.FindFirst() then
            ItemCrossRef := ItemCrossReference."Reference No.";
    end;

    var
        Cust: Record Customer;
        TotalHoseLength: Decimal;
        TotalWeight: Decimal;
        PosNo: Integer;
        BarcodeDeliveryNo: Text;
        BarcodeItemCrossRef: Text;
        BarcodeItemSearchDesc: Text;
        BarcodeItemSearchDescPalet: Text;
        BarcodePackageNo: Text;
        BarcodePO: Text;
        BarcodeQty: Text;
        BarcodeQtyLabel: Text;
        QrCode: Text;
        SupplierNo: Text;
        BarcodePaletDeliveryNo: Text;
        BarcodeSupplierNo: Text;
        CustRegisterName: Text[100];
        DeliveryNo: Text[100];
        ItemCrossRef: Text[100];
        ItemDesc2: Text[100];
        ItemSearchDesc: Text[100];
        ItemSearchDescPalet: Text[100];
        PaletDeliveryNo: Text[100];
        CustLabelDesc: Text[100];
}
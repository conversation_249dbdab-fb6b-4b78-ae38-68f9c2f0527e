table 60014 "Shortage List Detail FLX"
{
    Caption = 'Shortage List Detail';
    DataClassification = ToBeClassified;
    LookupPageId = "Shortage List Detail FLX";
    DrillDownPageId = "Shortage List Detail FLX";

    fields
    {
        field(1; "Register No."; Integer)
        {
            AllowInCustomizations = Always;
            Caption = 'Register No.';
            ToolTip = 'Specifies the value of the Register No. field.';
        }
        field(2; "Register Type"; Enum "Register Type FLX")
        {
            Caption = 'Register Type';
            ToolTip = 'Specifies the value of the Register Type field.';
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(4; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(5; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(6; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            ToolTip = 'Specifies the value of the Source No. field.';
        }
        field(7; "Source Name/Description"; Text[100])
        {
            Caption = 'Source Name/Description';
            ToolTip = 'Specifies the value of the Source Name/Description field.';
        }
        field(8; "Quantity On Reject Location"; Decimal)
        {
            Caption = 'Quantity On Reject Location';
            ToolTip = 'Specifies the value of the Quantity On Reject Location field.';
        }
        field(9; "Quantity On Subcontractors"; Decimal)
        {
            Caption = 'Quantity On Subcontractors';
            ToolTip = 'Specifies the value of the Quantity On Subcontractors field.';
        }
        field(10; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(11; Date; Date)
        {
            Caption = 'Date';
            ToolTip = 'Specifies the value of the Date field.';
        }
        field(12; "User Id"; Code[50])
        {
            AllowInCustomizations = Always;
            Caption = 'User Id';
            ToolTip = 'Specifies the value of the User Id field.';
        }
        field(13; "Quantity On UHD Location"; Decimal)
        {
            Caption = 'Quantity On UHD Location';
            ToolTip = 'Specifies the value of the Quantity On UHD Location field.';
        }
        field(14; "Quantity On WHSE Location"; Decimal)
        {
            Caption = 'Quantity On WHSE Location';
            ToolTip = 'Specifies the value of the Quantity On WHSE Location field.';
        }
    }
    keys
    {
        key(PK; "Register No.")
        {
            Clustered = true;
        }
    }
}
report 60006 "Posted Sbuf Report FLX"
{
    ApplicationArea = All;
    Caption = 'Sbuf Report';
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/PostedSbufReport.rdlc';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(SalesHeader; "Sales Invoice Header")
        {
            column(PostingDate; Format("Posting Date"))
            {
            }
            column(SelltoCustomerNo; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomerName; "Sell-to Customer Name")
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(farkmiktar_th; farkmiktar_th)
            {
            }
            column(CalcTotalNetKG; CalcTotalNetKG)
            {
            }
            column(tartilankg; tartilankg)
            {
            }
            column(TareWeight; TareWeight)
            {
            }
            column(PackageDesc; PackageDesc)
            {
            }
            column(TotalPackage; TotalPackage)
            {
            }
            column(TotalGrossWeight; TotalGrossWeight)
            {
            }
            column(TotalTareWeight; TotalTareWeight)
            {
            }
            column(TotalNetWeight; TotalNetWeight)
            {
            }
            column(TotalVolume; TotalVolume)
            {
            }
            column(containerno; containerno)
            {
            }
            dataitem(SalesLine; "Sales Invoice Line")
            {
                DataItemLinkReference = SalesHeader;
                DataItemLink = "Document No." = field("No.");
                DataItemTableView = sorting("Document No.", "Line No.") where(Type = const(Item), Quantity = filter(> 0));
                column(No_SalesLine; "No.")
                {
                }
                dataitem(ProductionBOMLine; "Production BOM Line")
                {
                    DataItemLinkReference = SalesLine;
                    DataItemTableView = sorting("Production BOM No.", "Version Code", "Line No.");
                    trigger OnPreDataItem()
                    begin
                        ActiveVersionCode := VersionMgt.GetBOMVersion(prodbomno, WorkDate(), true);
                        ProductionBOMLine.Reset();
                        ProductionBOMLine.SetRange("Production BOM No.", prodbomno);
                        ProductionBOMLine.SetRange("Version Code", ActiveVersionCode);
                    end;

                    trigger OnAfterGetRecord()
                    begin
                        if ProductionBOMLine."Unit of Measure Code" <> 'KG' then begin
                            TempProdBOMLine := ProductionBOMLine;
                            //TempProdBOMLine.Quantity:=ROUND(ProductionBOMLine."Quantity per"*SalesLine.Quantity,0.01); //FLEX-233 cancel
                            TempProdBOMLine.Quantity := Round(ProductionBOMLine."Quantity per" * SalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                            TempProdBOMLine.Depth := SalesLine."Line No.";
                            TempProdBOMLine."Production BOM No." := TempProdBOMLine."Production BOM No." + Format(SalesLine."Line No.");
                            TempProdBOMLine."Mamul GTIP FLX" := TempSalesLine."Job No.";
                            TempProdBOMLine.Insert(false);
                        end else begin
                            hamprodbomno := '';
                            if Item.Get(ProductionBOMLine."No.") then
                                hamprodbomno := Item."Production BOM No.";

                            if hamprodbomno <> '' then begin
                                //toplammiktar:=ROUND(ProductionBOMLine."Quantity per"*SalesLine.Quantity,0.01); //FLEX-233 cancel
                                toplammiktar := Round(ProductionBOMLine."Quantity per" * SalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                                firemiktar := Round(toplammiktar * ProductionBOMLine."Scrap %" / 100, 0.01);
                                //toplammiktar+=firemiktar; //FLEX-233 cancel
                                ActiveVersionCode := VersionMgt.GetBOMVersion(hamprodbomno, WorkDate(), true);
                                ProdBomLine.Reset();
                                ProdBomLine.SetRange("Production BOM No.", hamprodbomno);
                                ProdBomLine.SetRange("Version Code", ActiveVersionCode);
                                if ProdBomLine.FindFirst() then
                                    repeat
                                        TempProdBOMLine := ProdBomLine;
                                        TempProdBOMLine.Quantity := Round(ProdBomLine."Quantity per" * toplammiktar, 0.01);
                                        TempProdBOMLine.Depth := SalesLine."Line No.";
                                        TempProdBOMLine."Production BOM No." := TempProdBOMLine."Production BOM No." + Format(SalesLine."Line No.");
                                        TempProdBOMLine."Mamul GTIP FLX" := TempSalesLine."Job No.";
                                        TempProdBOMLine.Insert(false);
                                    until ProdBomLine.Next() = 0;
                            end else begin
                                TempProdBOMLine := ProductionBOMLine;
                                //TempProdBOMLine.Quantity:=ROUND(ProductionBOMLine."Quantity per"*SalesLine.Quantity,0.01); //FLEX-233 cancel
                                TempProdBOMLine.Quantity := Round(ProductionBOMLine."Quantity per" * SalesLine."Quantity (Base)", 0.01); //FLEX-233 added
                                TempProdBOMLine.Depth := SalesLine."Line No.";
                                TempProdBOMLine."Production BOM No." := TempProdBOMLine."Production BOM No." + Format(SalesLine."Line No.");
                                TempProdBOMLine."Mamul GTIP FLX" := TempSalesLine."Job No.";
                                TempProdBOMLine.Insert(false);
                            end;
                        end;
                    end;
                }
                trigger OnPreDataItem()
                begin
                    //sales line onpre
                end;

                trigger OnAfterGetRecord()
                begin
                    //sales line onafter
                    Gtip := SalesLine."Tariff No. INF";
                    prodbomno := '';
                    if Item.Get(SalesLine."No.") then begin
                        if Gtip = '' then
                            Gtip := Item."Tariff No.";
                        prodbomno := Item."Production BOM No.";
                    end;
                    if prodbomno = '' then
                        CurrReport.Skip();
                    if Gtip = '' then
                        Gtip := SalesLine."No.";
                    if MamulGTIPGrupsuz then
                        Gtip := 'Grupsuz';
                    if SalesLine."Unit of Measure Code" = 'FT' then
                        SalesLine.Quantity := Round(SalesLine.Quantity * 0.305, 0.01)
                    else
                        if (SalesLine."Unit of Measure Code" = 'ADET') or
                           (SalesLine."Unit of Measure Code" = 'LG') or
                           (SalesLine."Unit of Measure Code" = 'LH') then begin//FLEX-251
                            ItemUnitofMeasure.Reset();
                            ItemUnitofMeasure.SetRange("Item No.", SalesLine."No.");
                            ItemUnitofMeasure.SetRange(Code, SalesLine."Unit of Measure Code");
                            if ItemUnitofMeasure.FindFirst() then
                                SalesLine.Quantity := Round(SalesLine.Quantity * ItemUnitofMeasure."Qty. per Unit of Measure", 0.01);
                        end;
                    SalesLine."Unit of Measure Code" := 'MT';
                    TempSalesLine.SetRange("Job No.", Gtip);
                    if not TempSalesLine.FindFirst() then begin
                        TempSalesLine := SalesLine;
                        TempSalesLine."Job No." := Gtip;
                        TempSalesLine."Blanket Order No." := prodbomno;
                        TempSalesLine.Insert(false);
                    end else begin
                        TempSalesLine.Quantity += SalesLine.Quantity;
                        TempSalesLine.Modify(false);
                    end;
                end;
            }
            dataitem(Integer; Integer)
            {
                DataItemLinkReference = SalesHeader;
                DataItemTableView = sorting(Number);
                column(UnitofMeasureCode_SalesLine; TempSalesLine."Unit of Measure Code")
                {
                }
                column(Quantity_SalesLine; TempSalesLine.Quantity)
                {
                }
                column(Description_SalesLine; malcinsi)
                {
                }
                column(Gtip; Gtip)
                {
                }
                dataitem(TmpBom; Integer)
                {
                    DataItemLinkReference = Integer;
                    DataItemTableView = sorting(Number);
                    column(hammiktar_ProductionBOMLine; hammiktar)
                    {
                    }
                    column(UnitofMeasureCode_ProductionBOMLine; TempProdBOMLine."Unit of Measure Code")
                    {
                    }
                    column(Depth_ProductionBOMLine; TempProdBOMLine.Depth)
                    {
                    }
                    column(MamulGTIP_ProductionBOMLine; TempProdBOMLine."Mamul GTIP FLX")
                    {
                    }
                    column(Description_ProductionBOMLine; hammalcinsi)
                    {
                    }
                    column(hamgtip; hamgtip)
                    {
                    }
                    column(firemiktar; firemiktar)
                    {
                    }
                    column(toplammiktar; toplammiktar)
                    {
                    }
                    column(geneltoplammiktar; geneltoplammiktar)
                    {
                    }
                    column(hammaddegeneltoplam; hammaddegeneltoplam)
                    {
                    }
                    column(firegeneltoplam; firegeneltoplam)
                    {
                    }
                    column(geneltoplammiktarMT; geneltoplammiktarMT)
                    {
                    }
                    column(hammaddegeneltoplamMT; hammaddegeneltoplamMT)
                    {
                    }
                    column(firegeneltoplamMT; firegeneltoplamMT)
                    {
                    }
                    trigger OnPreDataItem()
                    begin
                        //tmpbom on pre
                        TempProdBOMLine.Reset();
                        //TempProdBOMLine.SETRANGE("Unit of Measure Code",'KG');
                        TempProdBOMLine.SetRange("Mamul GTIP FLX", Gtip);
                        if TempProdBOMLine.FindFirst() then
                            SetRange(Number, 1, TempProdBOMLine.Count());
                    end;

                    trigger OnAfterGetRecord()
                    begin
                        //tmpbom onafter
                        if TmpBom.Number = 1 then
                            TempProdBOMLine.FindFirst()
                        else
                            TempProdBOMLine.Next();

                        hamgtip := '';
                        hamprodbomno := '';
                        if Item.Get(TempProdBOMLine."No.") then
                            hamgtip := Item."Tariff No.";
                        if hamgtip = '' then
                            hamgtip := TempProdBOMLine."No.";

                        hammalcinsi := TempProdBOMLine.Description;
                        if (TariffNumber.Get(hamgtip)) and (TariffNumber.Description <> '') then
                            hammalcinsi := TariffNumber.Description;
                        hammiktar := TempProdBOMLine.Quantity;
                        toplammiktar := TempProdBOMLine.Quantity;
                        firemiktar := Round(toplammiktar * TempProdBOMLine."Scrap %" / 100, 0.01);
                        //toplammiktar+=firemiktar; //FLEX-233 cancel

                        if (hesapla_th) and (farkmiktar_th <> 0) and (TempProdBOMLine."Unit of Measure Code" = 'KG') and (toplamKG_th > 0) then begin
                            //farkoran_th:=TempProdBOMLine."Quantity per"/toplamKG_th;
                            //hesaplananfark_th:=ROUND(farkmiktar_th*farkoran_th,0.01);
                            //TempProdBOMLine.Quantity:=TempProdBOMLine.Quantity+hesaplananfark_th;
                            farkoran_th := toplammiktar / toplamKG_th;
                            hesaplananfark_th := Round(farkmiktar_th * farkoran_th, 0.01);
                            farkdahiltoplam := toplammiktar + hesaplananfark_th;
                            //hammiktar:=farkdahiltoplam-firemiktar; FLEX-233 cancel
                            hammiktar := farkdahiltoplam; //FLEX-233 added
                            toplammiktar := farkdahiltoplam;
                            TempProdBOMLine.Quantity := hammiktar;
                        end;
                        if TempProdBOMLine."Unit of Measure Code" = 'KG' then begin
                            geneltoplammiktar += toplammiktar;
                            firegeneltoplam += firemiktar;
                            hammaddegeneltoplam += hammiktar;
                        end else
                            if (TempProdBOMLine."Unit of Measure Code" = 'METRE') or
                               (TempProdBOMLine."Unit of Measure Code" = 'MT') then begin
                                TempProdBOMLine."Unit of Measure Code" := 'MT';
                                geneltoplammiktarMT += toplammiktar;
                                firegeneltoplamMT += firemiktar;
                                hammaddegeneltoplamMT += hammiktar;
                            end;
                    end;
                }
                trigger OnPreDataItem()
                begin
                    // Integer onpre
                    TempSalesLine.Reset();
                    SetRange(Number, 1, TempSalesLine.Count());

                    if hesapla_th then begin
                        TempProdBOMLine.Reset();
                        TempProdBOMLine.SetRange("Unit of Measure Code", 'KG');
                        if TempProdBOMLine.FindFirst() then
                            repeat
                                toplammiktar := TempProdBOMLine.Quantity;
                                firemiktar := Round(toplammiktar * TempProdBOMLine."Scrap %" / 100, 0.01);
                                //toplammiktar+=firemiktar; //FLEX-233 cancel
                                toplamKG_th += toplammiktar;
                            until TempProdBOMLine.Next() = 0;
                    end;
                end;

                trigger OnAfterGetRecord()
                begin
                    //integer on after
                    if Integer.Number = 1 then
                        TempSalesLine.FindFirst()
                    else
                        TempSalesLine.Next();
                    Gtip := TempSalesLine."Job No.";
                    prodbomno := TempSalesLine."Blanket Order No.";
                    malcinsi := TempSalesLine.Description;
                    if (TariffNumber.Get(Gtip)) and (TariffNumber.Description <> '') then
                        malcinsi := TariffNumber.Description;
                end;
            }
            trigger OnPreDataItem()
            begin
                //sales header onpre
            end;

            trigger OnAfterGetRecord()
            begin
                //sales header onafter
                farkmiktar_th := 0;
                tartilankg := 0;
                geneltoplammiktar := 0;
                firegeneltoplam := 0;
                hammaddegeneltoplam := 0;
                geneltoplammiktarMT := 0;
                firegeneltoplamMT := 0;
                hammaddegeneltoplamMT := 0;

                //IF hesapla_th THEN
                //BEGIN
                ComShipHD.Reset();
                ComShipHD.SetRange("Posted Sls. Invoice No.", SalesHeader."No.");
                if ComShipHD.FindLast() then
                    containerno := ComShipHD."Container No.";
                ComShipHD.SetFilter("Scaled Shipment Weight", '>0');
                if ComShipHD.FindLast() then begin
                    ComShipHD.CalcFields("Calcualted Shipment Weight");
                    farkmiktar_th := ComShipHD."Scaled Shipment Weight" - ComShipHD."Calcualted Shipment Weight";
                    tartilankg := ComShipHD."Scaled Shipment Weight";
                end;
                //END;
                //--------------sales invoice gibi net brüt kilo hesaplama---------------
                /* packing list kopya öncesi başlangıç
                PackageDesc := '';
                BulkPieces := 0;
                PalletQty := 0;
                TotalTareWeight := 0;
                TotalGrossWeight := 0;
                TotalNetWeight := 0;
                TotalVolume := 0;
                CombinedShipmentHeader.Reset();
                CombinedShipmentHeader.SetRange("Posted Sls. Invoice No.", SalesHeader."No.");
                if CombinedShipmentHeader.FindLast() then begin
                    Package.Reset();
                    Package.SetRange(Package."Combined Shipment No. FLX", CombinedShipmentHeader."No.");
                    if not Package.FindFirst() then
                        Error(PackageInfoErr);
                    repeat
                        if (Package."Package Type FLX" = Package."Package Type FLX"::Bulk) or (Package."Package Type FLX" = Package."Package Type FLX"::Coil) then
                            BulkPieces += 1
                        else
                            PalletQty += 1;
                        TareDesc := '';
                        TareWeight := 0;
                        if Package."Item No." <> '' then //begin
                            if Item.Get(Package."Item No.") then begin
                                TareDesc := Item.Description;
                                TareWeight := Item."Net Weight";
                                TotalTareWeight += Item."Net Weight";
                                TotalGrossWeight += Item."Net Weight";
                            end;
                        //end;
                        OrderNo := Package."Sales Order No. FLX";
                        CustOrderNo := Package."Your Reference FLX";
                        //if PackageContentTreeView."Transfer-from Package No." <> '' then begin
                        if Package."Parent Package No. FLX" <> '' then begin
                            PackageTransferOut.Reset();
                            //PackageTransferOut.SetRange("No.", PackageContentTreeView."Transfer-from Package No.");
                            PackageTransferOut.SetRange("Package No.", Package."Parent Package No. FLX");
                            if PackageTransferOut.FindFirst() then begin
                                OrderNo := PackageTransferOut."Sales Order No. FLX";
                                CustOrderNo := PackageTransferOut."Your Reference FLX";
                            end;
                        end;
                        //FLEX-230 END
                        CombineShipLineDetail.Reset();
                        CombineShipLineDetail.SetRange("Document No.", CombinedShipmentHeader."No.");
                        CombineShipLineDetail.SetRange("Package No.", Package."Package No.");
                        if CombineShipLineDetail.FindFirst() then
                            repeat
                                pallettotalmetre += CombineShipLineDetail.Quantity;
                                pallettotalfeet += Round(CombineShipLineDetail.Quantity / 0.305, 0.01);
                                //TotalGrossWeight += Round(CombineShipLineDetail."Coil Weight" * CombineShipLineDetail.Piece, 0.01);
                                TotalGrossWeight += CombineShipLineDetail."Package Weight (KG)";
                                //TotalNetWeight += Round(CombineShipLineDetail."Coil Weight" * CombineShipLineDetail.Piece, 0.01);
                                TotalNetWeight += CombineShipLineDetail."Package Weight (KG)";
                                //if Item.Get(CombineShipPackDetail."No.") then
                                if Item.Get(CombineShipLineDetail."Item No.") then
                                    //TotalVolume += Round(Item."Unit Volume" * CombineShipLineDetail.Piece, 0.01);
                                    TotalVolume += Item."Unit Volume";
                            until CombineShipLineDetail.Next() = 0;
                    until Package.Next() = 0;

                    CalcTotalNetKG := TotalNetWeight;
                    if farkmiktar_th <> 0 then begin
                        TotalNetWeight := CombinedShipmentHeader."Scaled Shipment Weight";
                        TotalGrossWeight := CombinedShipmentHeader."Scaled Shipment Weight" + TotalTareWeight;
                    end;
                    TotalGrossWeight := Round(TotalGrossWeight, 0.01);
                    TotalNetWeight := Round(TotalNetWeight, 0.01);
                    TotalTareWeight := Round(TotalTareWeight, 0.01);

                    TotalPackage := Format(PalletQty + BulkPieces) + ' PACKAGES';
                    if PalletQty > 0 then
                        PackageDesc := '( ' + Format(PalletQty) + ' PALLETS';
                    if BulkPieces > 0 then begin
                        if PackageDesc = '' then
                            PackageDesc += '( '
                        else
                            PackageDesc += ' / ';
                        PackageDesc += Format(BulkPieces) + ' BULK )';
                    end else
                        PackageDesc += ' )';

                    GrandPallettotaltxt := 'MT ' + Format(pallettotalmetre) + ' (FT ' + Format(pallettotalfeet) + ')';
                end;
                packing list kopya öncesi bitiş */
                // packing listden kopya başlangıç
                EntryNo := 0;
                //LineNumberNo := 0;
                PackageDesc := '';
                BulkPieces := 0;
                PalletQty := 0;
                TotalTareWeight := 0;
                TotalGrossWeight := 0;
                TotalNetWeight := 0;
                //TotalVolume := 0;
                pallettotalmetre := 0;
                pallettotalfeet := 0;
                farkmiktar_th := 0;

                ComShipHD.CalcFields("Calcualted Shipment Weight");
                if ComShipHD."Scaled Shipment Weight" > 0 then
                    farkmiktar_th := ComShipHD."Scaled Shipment Weight" - ComShipHD."Calcualted Shipment Weight";

                CombineShipPackDetail.Reset();
                CombineShipPackDetail.SetRange("Combined Shipment No.", ComShipHD."No.");
                if CombineShipPackDetail.FindFirst() then
                    CombineShipPackDetail.DeleteAll(true);

                CombineShipLineDetail.Reset();
                CombineShipLineDetail.SetRange("Document No.", ComShipHD."No.");
                if CombineShipLineDetail.FindFirst() then
                    repeat
                        CombineShipLineDetail.CalcFields("Parent Package Type");
                        if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) then
                            BulkPieces += 1
                        else begin

                            CombineShipPackDetail.Reset();
                            CombineShipPackDetail.SetRange("Combined Shipment No.", ComShipHD."No.");
                            CombineShipPackDetail.SetRange("Transfer-from Package No.", CombineShipLineDetail."Parent Package No.");
                            if not CombineShipPackDetail.FindFirst() then
                                PalletQty += 1;
                        end;

                        TareDesc := '';
                        TareWeight := 0;
                        if CombineShipLineDetail."Item No." <> '' then //begin
                            if Item.Get(CombineShipLineDetail."Item No.") then begin
                                TareDesc := Item.Description;
                                TareWeight := Item."Net Weight";
                                TotalTareWeight += Item."Net Weight";
                                TotalGrossWeight += Item."Net Weight";
                            end;
                        //end;
                        Package.Reset();
                        Package.SetRange(Package."Package No.", CombineShipLineDetail."Package No.");
                        Package.SetRange(Package."Combined Shipment No. FLX", ComShipHD."No.");
                        Package.FindFirst();
                        //FLEX-230 BEGIN
                        OrderNo := Package."Sales Order No. FLX";
                        CustOrderNo := Package."Your Reference FLX";
                        if CombineShipLineDetail."Parent Package No." <> '' then begin
                            PackageTransferOut.Reset();
                            PackageTransferOut.SetRange("Package No.", CombineShipLineDetail."Parent Package No.");
                            if PackageTransferOut.FindFirst() then begin
                                OrderNo := PackageTransferOut."Sales Order No. FLX";
                                CustOrderNo := PackageTransferOut."Your Reference FLX";
                            end;
                        end;
                        //FLEX-230 END
                        EntryNo += 1;
                        CombineShipPackDetail.Init();
                        CombineShipPackDetail."Combined Shipment No." := ComShipHD."No.";
                        CombineShipPackDetail."Package Type" := CombineShipLineDetail."Parent Package Type";
                        CombineShipPackDetail."Package No." := CombineShipLineDetail."Package No.";
                        CombineShipPackDetail."Transfer-from Package No." := CombineShipLineDetail."Parent Package No.";
                        CombineShipPackDetail."Source Document No." := OrderNo;//FLEX-230 added line
                        CombineShipPackDetail."Your Reference" := CustOrderNo; //FLEX-230 added line
                        CombineShipPackDetail."Entry No" := EntryNo;
                        CombineShipPackDetail.Type := CombineShipPackDetail.Type::Item;
                        CombineShipPackDetail."No." := CombineShipLineDetail."Item No.";
                        CombineShipPackDetail.Description := CombineShipLineDetail."Item Description";
                        CombineShipPackDetail.Quantity := CombineShipLineDetail.Quantity;
                        CombineShipPackDetail."Coil Lenght" := CombineShipLineDetail.Quantity;
                        CombineShipPackDetail."Hose Length" := CombineShipLineDetail.Quantity;
                        //CombineShipPackDetail."Serial No." := CombineShipLineDetail."Serial No.";
                        CombineShipPackDetail."Lot No." := CombineShipLineDetail."Lot No.";
                        LotNoInf.Reset();
                        LotNoInf.SetRange("Item No.", CombineShipLineDetail."Item No.");
                        LotNoInf.SetRange("Lot No.", CombineShipLineDetail."Lot No.");
                        if LotNoInf.FindFirst() then
                            //CombineShipPackDetail."Coil Weight" := ROUND(LotNoInf."Weight per Qty." * CombineShipPackDetail.Quantity, 0.01);
                            CombineShipPackDetail."Coil Weight" := CombineShipLineDetail."Package Weight (KG)";
                        //CombineShipPackDetail."Expiration Date" := PackageContentTreeView."Expiration Date";
                        //CombineShipPackDetail."Pallet Code" := Package."Package Item No.";
                        CombineShipPackDetail.Piece := 1;
                        if (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Bulk) or (CombineShipLineDetail."Parent Package Type" = CombineShipLineDetail."Parent Package Type"::Coil) then
                            CombineShipPackDetail."Pallet Line Number" := 'BULK'
                        else begin
                            CombineShipPackDetail."Pallet Line Number" := Format(PalletQty);
                            if StrLen(CombineShipPackDetail."Pallet Line Number") = 1 then
                                CombineShipPackDetail."Pallet Line Number" := CopyStr('0' + CombineShipPackDetail."Pallet Line Number", 1, 100);
                            CombineShipPackDetail."Pallet Line Number" += CopyStr('-' + CombineShipLineDetail."Parent Package No.", 1, 100);
                            CombineShipPackDetail."Pallet Description" := TareDesc;
                            CombineShipPackDetail."Tare Weight" := TareWeight;
                            CombineShipPackDetail."Pallet Code" := CombineShipLineDetail."Item No.";
                        end;
                        CombineShipPackDetail.Insert(true);
                        pallettotalmetre += CombineShipPackDetail.Quantity;
                        pallettotalfeet += Round(CombineShipPackDetail.Quantity / 0.305, 0.01);
                        TotalGrossWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01) + TareWeight;
                        TotalNetWeight += Round(CombineShipPackDetail."Coil Weight" * CombineShipPackDetail.Piece, 0.01);
                        TotalTareWeight += TareWeight;
                    //if Item.Get(CombineShipPackDetail."No.") then
                    //    TotalVolume += Round(Item."Unit Volume" * CombineShipPackDetail.Piece, 0.01);
                    until CombineShipLineDetail.Next() = 0;

                CalcTotalNetKG := TotalNetWeight;
                if farkmiktar_th <> 0 then begin
                    TotalNetWeight := ComShipHD."Scaled Shipment Weight";
                    TotalGrossWeight := ComShipHD."Scaled Shipment Weight" + TotalTareWeight;
                end;

                TotalPackage := Format(PalletQty + BulkPieces) + ' PACKAGES';
                if PalletQty > 0 then
                    PackageDesc := '( ' + Format(PalletQty) + ' PALLETS';
                if BulkPieces > 0 then begin
                    if PackageDesc = '' then
                        PackageDesc += '( '
                    else
                        PackageDesc += ' / ';
                    PackageDesc += Format(BulkPieces) + ' BULK )';
                end else
                    PackageDesc += ' )';

                GrandPallettotaltxt := 'MT ' + Format(pallettotalmetre) + ' (FT ' + Format(pallettotalfeet) + ')';
            end;
        }
    }
    requestpage
    {
        layout
        {
            area(Content)
            {
                group(GroupName)
                {
                    Caption = 'Options';
                    field(MamulGTIPGrupsuzx; MamulGTIPGrupsuz)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Mamul GTIP Grupsuz';
                        ToolTip = 'Specifies  Mamul GTIP Grupsuz.';
                    }
                    field(hesapla_thx; hesapla_th)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Tartılan Ağırlıkla Hesaplanan Ağırlık Farkını Dahil Et';
                        ToolTip = 'Specifies Include Calculated Weight And Measured Weight Diffrence.';
                    }
                }
            }
        }
        actions
        {
            area(Processing)
            {
            }
        }
        trigger OnOpenPage()
        begin
            hesapla_th := true;
        end;
    }
    var
        ComShipHD: Record "Combined Shipment Header FLX";
        CombineShipLineDetail: Record "CombinedShipmentLineDtl FLX";
        CombineShipPackDetail: Record "Combined Ship Package Det. FLX";
        Item: Record Item;
        ItemUnitofMeasure: Record "Item Unit of Measure";
        LotNoInf: Record "Lot No. Information";
        Package: Record "Package No. Information";
        PackageTransferOut: Record "Package No. Information";
        ProdBomLine: Record "Production BOM Line";
        TempProdBOMLine: Record "Production BOM Line" temporary;
        TempSalesLine: Record "Sales Invoice Line" temporary;
        TariffNumber: Record "Tariff Number";
        VersionMgt: Codeunit VersionManagement;
        hesapla_th: Boolean;
        MamulGTIPGrupsuz: Boolean;
        ActiveVersionCode: Code[20];
        Gtip: Code[20];
        hamgtip: Code[20];
        hamprodbomno: Code[20];
        prodbomno: Code[20];
        containerno: Code[50];
        BulkPieces: Decimal;
        CalcTotalNetKG: Decimal;
        farkdahiltoplam: Decimal;
        farkmiktar_th: Decimal;
        farkoran_th: Decimal;
        firegeneltoplam: Decimal;
        firegeneltoplamMT: Decimal;
        firemiktar: Decimal;
        geneltoplammiktar: Decimal;
        geneltoplammiktarMT: Decimal;
        hammaddegeneltoplam: Decimal;
        hammaddegeneltoplamMT: Decimal;
        hammiktar: Decimal;
        hesaplananfark_th: Decimal;
        PalletQty: Decimal;
        pallettotalfeet: Decimal;
        pallettotalmetre: Decimal;
        TareWeight: Decimal;
        tartilankg: Decimal;
        toplamKG_th: Decimal;
        toplammiktar: Decimal;
        TotalGrossWeight: Decimal;
        TotalNetWeight: Decimal;
        TotalTareWeight: Decimal;
        TotalVolume: Decimal;
        EntryNo: Integer;
        //LineNumberNo: Integer;
        CustOrderNo: Text[50];
        OrderNo: Text[50];
        TotalPackage: Text[50];
        TareDesc: Text[100];
        hammalcinsi: Text[250];
        malcinsi: Text[250];
        GrandPallettotaltxt: Text[1024];
        PackageDesc: Text[1024];
}
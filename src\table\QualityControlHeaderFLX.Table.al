/// <summary>
/// Table Quality Control Header FLX (ID 60008).
/// </summary>
table 60008 "Quality Control Header FLX"
{
    DataClassification = CustomerContent;
    Caption = 'Quality Control Header';
    DrillDownPageId = "Quality Control List FLX";
    LookupPageId = "Quality Control List FLX";
    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                FlexatiSetup: Record "Flexati Setup FLX";
                //NoSeriesManagement: Codeunit NoSeriesManagement; //marked removal future
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    FlexatiSetup.Get();
                    //NoSeriesManagement.TestManual(FlexatiSetup."Quality Control No. Series");
                    NoSeries.TestManual(FlexatiSetup."Quality Control No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; Date; Date)
        {
            Caption = 'Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Date field.';
        }
        field(3; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
        }
        field(4; "Label Text"; Code[50])
        {
            Caption = 'Label Text';
            ToolTip = 'Specifies the value of the Label Text field.';
            trigger OnValidate()
            begin
                QualityControlManagement.ProcessLabelText(Rec);
            end;
        }
        field(5; "Package Count"; Integer)
        {
            Caption = 'Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Quality Control Line FLX" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Package Count field.';
        }
        field(107; "No. Series"; Code[20])
        {
            AllowInCustomizations = Always;
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        FlexatiSetup: Record "Flexati Setup FLX";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            FlexatiSetup.Get();
            FlexatiSetup.TestField("Quality Control No. Series");
            //NoSeriesManagement.InitSeries(FlexatiSetup."Quality Control No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No." := NoSeries.GetNextNo(FlexatiSetup."Quality Control No. Series");
        end;
    end;

    trigger OnDelete()
    var
        QualityControlLine: Record "Quality Control Line FLX";
    begin
        Rec.TestField(Posted, false);

        QualityControlLine.SetRange("Document No.", Rec."No.");
        QualityControlLine.DeleteAll(true);
    end;

    var
        QualityControlManagement: Codeunit "Quality Control Management FLX";
}
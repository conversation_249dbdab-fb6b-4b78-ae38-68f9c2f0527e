page 60035 "Item Attribute Matrix FLX"
{
    ApplicationArea = All;
    Caption = 'Item Attribute Matrix';
    PageType = ListPlus;
    SourceTable = "Item Attribute Value Mapping";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            // repeater(General)
            // {
            field(MATRIX_CaptionRange; MATRIX_CaptionRange)
            {
                Editable = false;
                Caption = 'Column Set';
                ToolTip = 'Specifies the value of the Column Set field.';
            }
            part(MatrixForm; "Items by Item Attribute Matrix")
            {
                Caption = 'Matrix Form';
                Editable = false;
                //UpdatePropagation = Both;
                UpdatePropagation = SubPart; //NAV17 böyle a<PERSON>
            }

            //}
        }
    }
    actions
    {
        area(Processing)
        {
            action(PreviousSet)
            {
                ApplicationArea = All;
                Caption = 'Previous Set';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PreviousSet;
                PromotedOnly = true;
                ToolTip = 'Executes the Previous Set action.';
                trigger OnAction()
                begin
                    SetColumns(MATRIX_SetWanted::Previous);
                end;
            }
            action(NextSet)
            {
                ApplicationArea = All;
                Caption = 'Next Set';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NextSet;
                PromotedOnly = true;
                ToolTip = 'Executes the Next Set action.';
                trigger OnAction()
                begin
                    SetColumns(MATRIX_SetWanted::Next);
                end;
            }
        }
    }

    procedure SetColumns(SetWanted: Enum "MATRIX_SetWanted FLX")
    var
        MatrixMgt: Codeunit "Matrix Management";
        CaptionFieldNo: Integer;
        CurrentMatrixRecordOrdinal: Integer;
        OptValue: Option;
    begin
        Clear(MATRIX_CaptionSet);
        Clear(MatrixRecords);
        CurrentMatrixRecordOrdinal := 1;
        OptValue := SetWanted.AsInteger();

        MatrixRecordRef.GetTable(TempMatrixItemAttribute);
        MatrixRecordRef.SetTable(TempMatrixItemAttribute);

        // IF ShowColumnName THEN
        CaptionFieldNo := TempMatrixItemAttribute.FieldNo(Name);
        // ELSE
        //  CaptionFieldNo := TempMatrixItemAttribute.FIELDNO(ID);

        MatrixMgt.GenerateMatrixData(MatrixRecordRef, OptValue, ArrayLen(MatrixRecords), CaptionFieldNo, MATRIX_PKFirstRecInCurrSet,
          MATRIX_CaptionSet, MATRIX_CaptionRange, MATRIX_CurrSetLength);

        if MATRIX_CaptionSet[1] = '' then begin
            MATRIX_CaptionSet[1] := Text.CopyStr(UnspecifiedLocationCodeTxt, 1, Text.MaxStrLen(MATRIX_CaptionSet[1]));
            MATRIX_CaptionRange := StrSubstNo('%1%2', MATRIX_CaptionSet[1], MATRIX_CaptionRange);
        end;

        if MATRIX_CurrSetLength > 0 then begin
            TempMatrixItemAttribute.SetPosition(MATRIX_PKFirstRecInCurrSet);
            TempMatrixItemAttribute.Find();
            repeat
                MatrixRecords[CurrentMatrixRecordOrdinal].Copy(TempMatrixItemAttribute);
                CurrentMatrixRecordOrdinal := CurrentMatrixRecordOrdinal + 1;
            until (CurrentMatrixRecordOrdinal > MATRIX_CurrSetLength) or (TempMatrixItemAttribute.Next() <> 1);
        end;

        UpdateMatrixSubform();
    end;

    // local procedure ShowColumnNameOnAfterValidate()
    // begin
    //     SetColumns(MATRIX_SetWanted::Same);
    // end;

    local procedure UpdateMatrixSubform()
    begin
        CurrPage.MatrixForm.Page.Load(MATRIX_CaptionSet, MatrixRecords, TempMatrixItemAttribute, MATRIX_CurrSetLength);
        CurrPage.MatrixForm.Page.SetRecord(Rec);
        CurrPage.Update(false);
    end;

    trigger OnInit()
    var
        ItemAttribute: Record "Item Attribute";
    begin
        if ItemAttribute.FindSet() then
            repeat
                TempMatrixItemAttribute.Init();
                TempMatrixItemAttribute.Copy(ItemAttribute);
                //TempMatrixItemAttribute.Insert(true); //compiler hata veriyor temp tablo trigger olmaz diyor
                TempMatrixItemAttribute.Insert(false);
            until ItemAttribute.Next() = 0;
        TempMatrixItemAttribute.FindFirst();
    end;

    trigger OnOpenPage()
    begin
        SetColumns(MATRIX_SetWanted::" ");
    end;

    var
        MatrixRecords: array[32] of Record "Item Attribute";
        TempMatrixItemAttribute: Record "Item Attribute" temporary;
        MatrixRecordRef: RecordRef;
        MATRIX_SetWanted: Enum "MATRIX_SetWanted FLX";
        MATRIX_CurrSetLength: Integer;
        UnspecifiedLocationCodeTxt: Label 'UNSPECIFIED';
        MATRIX_CaptionSet: array[32] of Text[80];
        MATRIX_PKFirstRecInCurrSet: Text;
        MATRIX_CaptionRange: Text;
}
page 60012 "Combined Shipment List FLX"
{
    ApplicationArea = All;
    Caption = 'Combined Shipments';
    PageType = List;
    SourceTable = "Combined Shipment Header FLX";
    UsageCategory = Lists;
    CardPageId = "Combined Shipment FLX";
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Total Package Quantity"; Rec."Total Package Count")
                {
                }
                field("Calcualted Shipment Weight"; Rec."Calcualted Shipment Weight")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Posted Sls. Invoice No."; Rec."Posted Sls. Invoice No.")
                {
                }
                field("Remove Package"; Rec."Remove Package")
                {
                }
                field("Sales Invoice No."; Rec."Sales Invoice No.")
                {
                }
                field("Scaled Shipment Weight"; Rec."Scaled Shipment Weight")
                {
                }
                field("Ship Name"; Rec."Ship Name")
                {
                }
                field("Ship-to Address"; Rec."Ship-to Address")
                {
                }
                field("Ship-to Address 2"; Rec."Ship-to Address 2")
                {
                }
                field("Ship-to City"; Rec."Ship-to City")
                {
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                }
                field("Ship-to Country/Region Code"; Rec."Ship-to Country/Region Code")
                {
                }
                field("Ship-to County"; Rec."Ship-to County")
                {
                }
                field("Ship-to Name"; Rec."Ship-to Name")
                {
                }
                field("Ship-to Name 2"; Rec."Ship-to Name 2")
                {
                }
                field("Ship-to Post Code"; Rec."Ship-to Post Code")
                {
                }
                field("Shipment Method Code"; Rec."Shipment Method Code")
                {
                }
                field("Shipment No."; Rec."Shipment No.")
                {
                    //ToolTip = 'Specifies the value of the Shipment No. field.';
                    Caption = 'Shipment No.';
                }
                field(Status; Rec.Status)
                {
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field("Voyage No."; Rec."Voyage No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
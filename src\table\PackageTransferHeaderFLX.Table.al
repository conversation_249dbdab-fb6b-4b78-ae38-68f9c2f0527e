/// <summary>
/// Table Package Transfer Header FLX (ID 60011).
/// </summary>
table 60011 "Package Transfer Header FLX"
{
    DataClassification = CustomerContent;
    Caption = 'Package Transfer Header';
    DrillDownPageId = "Package Transfer Orders FLX";
    LookupPageId = "Package Transfer Orders FLX";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                FlexatiSetup: Record "Flexati Setup FLX";
                //NoSeriesManagement: Codeunit NoSeriesManagement;
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    FlexatiSetup.Get();
                    //NoSeriesManagement.TestManual(FlexatiSetup."Package Transfer No. Series");
                    NoSeries.TestManual(FlexatiSetup."Package Transfer No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        // field(2; "Transfer-from Code"; Code[10])
        // {
        //     Caption = 'Transfer-from Code';
        //     TableRelation = Location where("Use As In-Transit" = const(false));
        // }
        // field(3; "Transfer-from Bin Code"; Code[20])
        // {
        //     Caption = 'Transfer-from Bin Code';
        //     TableRelation = Bin.Code where("Location Code" = field("Transfer-from Code"));
        // }
        field(4; "Transfer-to Code"; Code[10])
        {
            Caption = 'Transfer-to Code';
            TableRelation = Location where("Use As In-Transit" = const(false));
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
            trigger OnValidate()
            var
                Bin: Record Bin;
            begin
                Bin.SetRange("Location Code", "Transfer-to Code");
                Bin.FindFirst();

                "Transfer-To Bin Code" := Bin.Code;
            end;
        }
        field(5; "Transfer-To Bin Code"; Code[20])
        {
            Caption = 'Transfer-To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Transfer-to Code"));
            ToolTip = 'Specifies the value of the Transfer-To Bin Code field.';
        }
        field(6; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(7; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            begin
                FlexatiPackageTransMgt.ProcessBarcode(Rec);
            end;
        }
        field(8; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
        }
        // field(9; "Production Order No."; Code[20])
        // {
        //     Caption = 'Production Order No.';
        //     TableRelation = "Production Order"."No." where(Status = const(Released));
        // }
        // field(10; "Transferring to Prod. Location"; Boolean)
        // {
        //     Caption = 'Transferring to Prod. Location';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup(Location."Production Location FLX" where(Code = field("Transfer-to Code")));
        // }
        field(11; "Total Package Count"; Integer)
        {
            Caption = 'Total Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Transfer Line FLX" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Package Count field.';
        }
        field(107; "No. Series"; Code[20])
        {
            AllowInCustomizations = Always;
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        FlexatiSetup: Record "Flexati Setup FLX";
        //NoSeriesManagement: Codeunit NoSeriesManagement; //marked removal future
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            FlexatiSetup.Get();
            FlexatiSetup.TestField("Package Transfer No. Series");
            //NoSeriesManagement.InitSeries(FlexatiSetup."Package Transfer No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No." := NoSeries.GetNextNo(FlexatiSetup."Package Transfer No. Series");
        end;

        "Posting Date" := WorkDate();
    end;

    trigger OnDelete()
    begin
        Rec.TestField(Posted, false);
    end;

    var
        FlexatiPackageTransMgt: Codeunit "Package Trans. Mgt. FLX";
}
tableextension 60001 "Lot No. Information FLX" extends "Lot No. Information"
{
    fields
    {
        field(60000; "Your Reference FLX"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(60002; "Sell-to Customer No. FLX"; Code[20])
        {
            Caption = 'Sell-to Customer No.';
            ToolTip = 'Specifies the value of the Sell-to Customer No. field.';
        }
        field(60003; "Sell-to Customer Name FLX"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        field(60007; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(60008; "Sales Order No. FLX"; Code[20])
        {
            Caption = 'Sales Order No.';
            ToolTip = 'Specifies the value of the Sales Order No. field.';
        }
        field(60009; "Sales Order Line No. FLX"; Integer)
        {
            Caption = 'Sales Order Line No.';
            ToolTip = 'Specifies the value of the Sales Order Line No. field.';
        }
        field(60010; "Production Order No. FLX"; Code[20])
        {
            Caption = 'Production Order No.';
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(60011; "Hose Lenght FLX"; Decimal)
        {
            Caption = 'Hose Lenght';
            ToolTip = 'Specifies the value of the Hose Lenght field.';
        }
        field(60001; "ID (mm) FLX"; Decimal)
        {
            Caption = 'ID (mm)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the ID (mm) field.';
        }
        field(60005; "OD (mm) FLX"; Decimal)
        {
            Caption = 'OD (mm)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the OD (mm) field.';
        }
        field(60004; "WP (bar) FLX"; Decimal)
        {
            Caption = 'WP (bar)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the WP (bar) field.';
        }
        field(60006; "BP (bar) FLX"; Decimal)
        {
            Caption = 'BP (bar)';
            DecimalPlaces = 0 : 2;
            ToolTip = 'Specifies the value of the BP (bar) field.';
        }
    }
}
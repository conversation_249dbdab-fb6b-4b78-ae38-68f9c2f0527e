tableextension 60004 "Sales Shipment Line FLX" extends "Sales Shipment Line"
{
    fields
    {
        field(60000; "Piece FLX"; Integer)
        {
            Caption = 'Piece';
            ToolTip = 'Specifies the value of the Piece field.';
        }
        field(60001; "Hose Length FLX"; Decimal)
        {
            Caption = 'Hose Length';
            ToolTip = 'Specifies the value of the Hose Lenght field.';
        }
        field(60002; "Unit Weight (Base) FLX"; Decimal)
        {
            Caption = 'Coil Weight';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Coil Weight field.';
        }
        field(60003; "Production Order Status FLX"; Enum "Production Order Status")
        {
            Caption = 'Production Order Status';
            ToolTip = 'Specifies the value of the Production Order Status field.';
        }
        field(60004; "Production Order No. FLX"; Code[20])
        {
            Caption = 'Production Order No.';
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(60005; "Sell-to Customer Name FLX"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Shipment Header"."Sell-to Customer Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        field(60010; "Your Reference FLX"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
            // Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = lookup("Sales Shipment Header"."Your Reference" where("No." = field("Document No.")));
        }
        field(60007; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Shipment Header"."Ship-to Code" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(60008; "Flexati Shipment Date FLX"; Date)
        {
            Caption = 'Flexati Shipment Date';
            AllowInCustomizations = Always;
        }
        field(60009; "External Document No. FLX"; Text[35])
        {
            Caption = 'External Document No.';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Shipment Header"."External Document No." where("No." = field("Document No.")));
        }
    }
}
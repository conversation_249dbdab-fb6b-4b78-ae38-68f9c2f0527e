{"alOutline.additionalMandatoryAffixesPatterns": [" INF", " FLX"], "alOutline.defaultDataClassification": "Customer<PERSON><PERSON>nt", "alOutline.enableCodeCopFixes": true, "alOutline.fixCaseRemovesQuotesFromDataTypeIdentifiers": true, "alOutline.fixCodeCopMissingParenthesesOnSave": true, "alOutline.noEmptyLinesAtTheEndOfWizardGeneratedFiles": true, "alOutline.openDefinitionInNewTab": true, "linterCop.load-pre-releases": true, "alVarHelper.ignoreALSuffix": "FLX", "alNavigator.ignoreALSuffix": "FLX", "CRS.OnSaveAlFileAction": "Reorganize", "CRS.ObjectNameSuffix": " FLX", "al.codeAnalyzers": ["${CodeCop}", "${UICop}", "${PerTenantExtensionCop}", "${analyzerFolder}BusinessCentral.LinterCop.dll"], "al.ruleSetPath": "infotek.ruleset.json", "editor.snippetSuggestions": "bottom", "ALTB.snippetTargetLanguage": "TRK", "xliffSync.snippetTargetLanguage": "TRK", "al.showDetailedAnalyzerStatistics": false}
page 60037 "Tablet Package No. Information"
{
    ApplicationArea = All;
    Caption = 'Tablet Package No. Information';
    PageType = Card;
    SourceTable = "Package No. Information";
    UsageCategory = None;
    DeleteAllowed = false;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus FLX")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode Text FLX');
                end;
            }
            group(General)
            {
                Caption = 'General';
                Visible = (Rec."Package Type FLX" = Rec."Package Type FLX"::Palette);
                field("Palette Item No. FLX"; Rec."Palette Item No. FLX")
                {
                }
            }
            group("LabelReading FLX")
            {
                Caption = 'Label Reading';
                Visible = (Rec."Package Type FLX" = Rec."Package Type FLX"::Palette) or
                            (Rec."Package Type FLX" = Rec."Package Type FLX"::Bulk);
                field("Remove Package FLX"; Rec."Remove Package FLX")
                {
                }
                field("Barcode Text FLX"; Rec."Barcode Text FLX")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode Text FLX');
                    end;
                }
            }
            part(Packages; "Package No. Info. Subpage FLX")
            {
                Caption = 'Lines';
                SubPageLink = "Parent Package No. FLX" = field("Package No.");
                Visible = (Rec."Package Type FLX" = Rec."Package Type FLX"::Palette)
                        or (Rec."Package Type FLX" = Rec."Package Type FLX"::Bulk);
            }
        }
    }
}

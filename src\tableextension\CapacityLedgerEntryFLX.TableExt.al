tableextension 60015 "Capacity Ledger Entry FLX" extends "Capacity Ledger Entry"
{
    fields
    {
        field(60000; "Production Line No. FLX"; Code[10])
        {
            Caption = 'Production Line No.';
            ToolTip = 'Specifies the value of the Production Line No. field.';
        }
        field(60001; "Item Journal Batch Name FLX"; Code[10])
        {
            Caption = 'Item Journal Batch Name';
            ToolTip = 'Specifies the value of the Item Journal Batch Name field.';
        }
        field(60002; "Package No. FLX"; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(60003; "Starting DateTime FLX"; DateTime)
        {
            Caption = 'Starting DateTime';
            ToolTip = 'Specifies the value of the Starting DateTime field.';
        }
        field(60004; "Ending DateTime FLX"; DateTime)
        {
            Caption = 'Ending DateTime';
            ToolTip = 'Specifies the value of the Ending DateTime field.';
        }
    }
}
page 60040 "PP Production FLX"
{
    ApplicationArea = All;
    Caption = 'PP Production';
    PageType = StandardDialog;
    SourceTable = "Banbury Production FLX";
    UsageCategory = Tasks;
    Editable = true;

    layout
    {
        area(Content)
        {
            field("Production Order No."; Rec."Production Order No.")
            {
            }
            field("Item No."; Rec."Item No.")
            {
            }
            field("Routing No."; Rec."Routing No.")
            {
            }
            field("User ID"; Rec."User ID")
            {
                Editable = false;
            }
            field(Quantity; Rec.Quantity)
            {
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Init();
        // Rec."Production Order No." := 'SBRS24-000027';
        Rec.Validate("User ID", 'USER500');
        // Rec.Quantity := 100;
        Rec.Insert(true);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        FlexatiProductionMngt.PostProduction(Rec);
    end;

    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
page 60005 "Finish Production FLX"
{
    ApplicationArea = All;
    Caption = 'Finish Production';
    PageType = StandardDialog;
    SourceTable = "Production Operation FLX";
    UsageCategory = Tasks;
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            group(FinishProduction)
            {
                Caption = 'Finish Production';
                field("User ID"; Rec."User ID")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Init();
        //Rec."User ID" := 'USER01';
        //Rec.Validate("Production Line No.", 'HAT01');
        Rec.Insert(false);
        //CurrPage.SetRecord(Rec);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        FlexatiProductionMngt.FinishProductionProcess(Rec);
    end;

    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
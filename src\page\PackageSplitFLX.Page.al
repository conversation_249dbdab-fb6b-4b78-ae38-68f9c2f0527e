/// <summary>
/// Page Package Split FLX (ID 60007).
/// </summary>
page 60007 "Package Split FLX"
{
    ApplicationArea = All;
    Caption = 'Package Split';
    PageType = Card;
    SourceTable = "Package Split Header FLX";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus FLX")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Package No.');
                end;
            }

            group(General)
            {
                Caption = 'General';
                Editable = not Rec.Completed;
                field("No."; Rec."No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field("Total Quantity to Split"; Rec."Total Quantity to Split")
                {
                }
                field("Total Split Quantity"; Rec."Total Split Quantity")
                {
                }
                field("Requested Hose Lenght"; Rec."Requested Hose Lenght")
                {
                }
            }
            group(LabelReadingArea)
            {
                Caption = 'Label Reading';
                Editable = not Rec.Completed;
                field("Package No."; Rec."Package No.")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Package No.');
                    end;
                }
            }
            part(Lines; "Package Split Subpage FLX")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = not Rec.Completed;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Post)
            {
                ApplicationArea = All;
                Caption = 'Post';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                ToolTip = 'Executes the Post action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PackageSplitLine: Record "Package Split Line FLX";
                    PackageNoFilter: Text;
                begin
                    PackageSplitManagement.CreateItemReclassificationJournalLinesFromPackageSplitHeader(Rec);
                    Commit();//
                    PackageSplitLine.Reset();
                    PackageSplitLine.SetRange("Document No.", Rec."No.");
                    if PackageSplitLine.FindSet() then
                        repeat
                            if PackageNoFilter <> '' then
                                PackageNoFilter += '|';
                            PackageNoFilter += PackageSplitLine."New Package No.";
                        until PackageSplitLine.Next() = 0;
                    PackageNoInformation.Reset();
                    PackageNoInformation.SetFilter("Package No.", PackageNoFilter);
                    if PackageNoInformation.FindSet() then
                        Report.Run(Report::"Customer Label FLX", true, true, PackageNoInformation);
                    CurrPage.Close();
                end;
            }
            action(CreateNewPackage)
            {
                ApplicationArea = All;
                Caption = 'Create New Package';
                //Promoted = true;
                //PromotedCategory = Process;
                //PromotedIsBig = true;

                Image = New;
                ToolTip = 'Executes the Create New Package action.';
                trigger OnAction()
                begin
                    PackageSplitManagement.CreateNewPackageSplitLineFromHeader(Rec);
                end;
            }
            action(PrintLabel)
            {
                ApplicationArea = All;
                Caption = 'Print Label';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                Image = Print;
                ToolTip = 'Executes the Print Label action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PackageSplitLine: Record "Package Split Line FLX";
                    PackageNoFilter: Text;
                begin
                    PackageSplitLine.Reset();
                    PackageSplitLine.SetRange("Document No.", Rec."No.");
                    if PackageSplitLine.FindSet() then
                        repeat
                            if PackageNoFilter <> '' then
                                PackageNoFilter += '|';
                            PackageNoFilter += PackageSplitLine."New Package No.";
                        until PackageSplitLine.Next() = 0;
                    PackageNoInformation.Reset();
                    PackageNoInformation.SetFilter("Package No.", PackageNoFilter);
                    PackageNoInformation.FindSet();
                    Report.Run(Report::"Customer Label FLX", true, true, PackageNoInformation);
                end;
            }
        }
    }
    var
        PackageSplitManagement: Codeunit "Package Split Management FLX";
}
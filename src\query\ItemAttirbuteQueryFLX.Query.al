query 60002 "Item Attirbute Query FLX"
{
    Caption = 'Item Attirbute Query';
    QueryType = Normal;
    QueryCategory = 'Item List';

    elements
    {
        dataitem(Item; Item)
        {
            column(No; "No.")
            {
            }
            column(Description; Description)
            {
            }
            dataitem(Item_Attribute_Value_Mapping; "Item Attribute Value Mapping")
            {
                DataItemLink = "No." = Item."No.";
                column(ItemAttributeID; "Item Attribute ID")
                {
                }
                column(ItemAttributeValueID; "Item Attribute Value ID")
                {
                }
                dataitem(Item_Attribute_Value; "Item Attribute Value")
                {
                    DataItemLink = "Attribute ID" = Item_Attribute_Value_Mapping."Item Attribute ID", ID = Item_Attribute_Value_Mapping."Item Attribute Value ID";

                    column(AttributeName; "Attribute Name")
                    {
                    }
                    column("Value"; "Value")
                    {
                    }
                }
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}
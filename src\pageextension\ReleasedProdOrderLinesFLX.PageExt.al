pageextension 60011 "Released Prod. Order Lines FLX" extends "Released Prod. Order Lines"
{
    layout
    {
        addafter("Item No.")
        {
            field("Source Line No. FLX"; Rec."Source Line No. FLX")
            {
                ToolTip = 'Specifies the value of the Source Line No. field.';
                ApplicationArea = All;
                Visible = false;
            }
            field("Line No. FLX"; Rec."Line No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Line No. field.';
            }

        }
        addlast(Control1)
        {
            field("Lot No. FLX"; Rec."Lot No. FLX")
            {
                ApplicationArea = All;
                Editable = false;
            }
            field("Flexati Shipment Date FLX"; Rec."Flexati Shipment Date FLX")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Flexati Shipment Date field.';
            }
            field("Line Note FLX"; Rec."Line Note FLX")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Line Note field.';
            }
        }
        addbefore(Quantity)
        {
            field("Piece FLX"; Rec."Piece FLX")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Piece field.';
            }
            field("Hose Length FLX"; Rec."Hose Length FLX")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Hose Lenght field.';
            }
            field("Stop FLX"; Rec."Stop FLX")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Stop field.';
            }
        }
        modify("Location Code")
        {
            Visible = true;
        }
        modify("Bin Code")
        {
            Visible = true;
        }
    }
    actions
    {
        addafter("&Line")
        {
            action("ProductionPlanningWorksheet FLX")
            {
                ApplicationArea = Manufacturing;
                Caption = 'Production Planning Worksheet';
                Image = PlanningWorksheet;
                ToolTip = 'View or edit the operations list of the parent item on the line.';
                // Promoted = true;
                // PromotedIsBig = true;
                // PromotedOnly = true;
                // PromotedCategory = Process;

                trigger OnAction()
                begin
                    FlexatiProductionMngt.OpenProductionPlanningWorksheetFromProdOrderLine(Rec);
                end;
            }
        }
    }
    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";

    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
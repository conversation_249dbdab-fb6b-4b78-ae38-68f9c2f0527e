page 60008 "Package Split Subpage FLX"
{
    ApplicationArea = All;
    Caption = 'Package Split Subpage';
    PageType = ListPart;
    SourceTable = "Package Split Line FLX";
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("New Package No."; Rec."New Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Source Package No."; Rec."Source Package No.")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
        }
    }
}
pageextension 60030 "Item Tracking Lines Ext. FLX" extends "Item Tracking Lines"
{
    actions
    {
        addafter(Barcode)
        {
            action("Item Lot No Barcode FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Item Lot No Label';
                Image = Print;
                ToolTip = 'Item Lot No Label.';

                trigger OnAction()
                var
                    rpTrSpec: Report "Tracking Spec Barcode FLX";
                begin
                    rpTrSpec.SetVar(Rec."Item No.", Rec."Lot No.");
                    rpTrSpec.RunModal();
                end;
            }
        }
    }
}
page 60001 "Production Planning Worksheet"
{
    ApplicationArea = All;
    Caption = 'Production Planning Worksheet';
    PageType = List;
    SourceTable = "Prod. Order Routing Line";
    SourceTableView = where("Line Lenght FLX" = filter(<> 0));
    UsageCategory = None;
    DataCaptionFields = "Prod. Order No.", Description;
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Prod. Order - Line No. FLX"; Rec."Prod. Order - Line No. FLX")
                {
                }
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the status of the routing line.';
                }
                field("Prod. Order No."; Rec."Prod. Order No.")
                {
                    ToolTip = 'Specifies the number of the related production order.';
                }
                field("Routing Reference No."; Rec."Routing Reference No.")
                {
                    ToolTip = 'Specifies the value of the Routing Reference No. field.';
                }
                field("Sales Order Line No. FLX"; Rec."Sales Order Line No. FLX")
                {
                }
                field("Operation No."; Rec."Operation No.")
                {
                    ToolTip = 'Specifies the operation number.';
                }
                field("Routing No."; Rec."Routing No.")
                {
                    ToolTip = 'Specifies the value of the Routing No. field.';
                    Editable = false;
                }
                field(Type; Rec."Type")
                {
                    ToolTip = 'Specifies the type of operation.';
                    Editable = false;
                }
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                    Editable = false;
                }
                field("Input Quantity"; Rec."Input Quantity")
                {
                    ToolTip = 'Specifies the value of the Input Quantity field.';
                    Editable = false;
                }
                field(ShippedQuantity; FlexatiProductionMngt.CalculateShippedQuantity(Rec))
                {
                    Caption = 'Shipped Quantity';
                    ToolTip = 'Specifies the value of the Shipped Quantity field.';
                }
                field("Finished Quantity FLX"; Rec."Finished Quantity FLX")
                {
                }
                field("Positive Finished Quantity"; FlexatiProductionMngt.CalculatePositiveFinishedQuantityFromProdOrderRoutingLine(Rec))
                {
                    Caption = 'Positive Finished Quantity';
                    ToolTip = 'Specifies the value of the Positive Finished Quantity field.';
                }
                field("Positive Remaining Quantity"; Rec."Input Quantity" - FlexatiProductionMngt.CalculatePositiveFinishedQuantityFromProdOrderRoutingLine(Rec))
                {
                    Caption = 'Positive Remaining Quantity';
                    ToolTip = 'Specifies the value of the Positive Remaining Quantity field.';
                }
                field("Quantity to Plan"; Rec."Quantity to Plan FLX")
                {
                    ShowMandatory = true;
                    trigger OnValidate()
                    begin
                        FlexatiProductionMngt.QuantityToPlanChecks(Rec);
                    end;
                }
                field("Printed Head Label Lenght FLX"; Rec."Printed Head Label Lenght FLX")
                {
                }
                // field("Remaining Quantity FLX"; Rec."Remaining Quantity FLX")
                // {
                //     ToolTip = 'Specifies the value of the Remaining Quantity field.';
                // }
                field("Line Lenght FLX"; Rec."Line Lenght FLX")
                {
                }
                field("Hose Lenght FLX"; Rec."Hose Lenght FLX")
                {
                }
                /*field("FG Qty. FLX"; Rec."FG Qty. FLX")
                {
                    ToolTip = 'Specifies the value of the FG Qty. field.';
                }
                field("KALITE Qty. FLX"; Rec."KALITE Qty. FLX")
                {
                    ToolTip = 'Specifies the value of the KALITE Qty. field.';
                }
                field("YURUMEYEN Qty. FLX"; Rec."YURUMEYEN Qty. FLX")
                {
                    ToolTip = 'Specifies the value of the YURUMEYEN Qty. field.';
                }
                field("UHD Qty. FLX"; Rec."UHD Qty. FLX")
                {
                    ToolTip = 'Specifies the value of the UHD Qty. field.';
                }
                */
            }
        }
    }
    actions
    {
        area(Reporting)
        {
            action(PositiveQuantityInStock)
            {
                ApplicationArea = All;
                Caption = 'Positive Quantity In Stock';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Positive Quantity In Stock action.';
                Image = Inventory;
                PromotedOnly = true;
                trigger OnAction()
                var
                    ItemLedgerEntry: Record "Item Ledger Entry";
                    ProdOrderLine: Record "Prod. Order Line";
                begin
                    ProdOrderLine.Get(Rec.Status, Rec."Prod. Order No.", Rec."Routing Reference No.");

                    ItemLedgerEntry.SetRange("Include In Calcualtion FLX", true);
                    ItemLedgerEntry.SetRange("Item No.", ProdOrderLine."Item No.");
                    ItemLedgerEntry.SetRange(Open, true);
                    ItemLedgerEntry.SetRange("Lot No.", ProdOrderLine."Lot No. FLX");

                    Page.Run(Page::"Item Ledger Entries", ItemLedgerEntry)
                end;
            }
        }
        area(Processing)
        {
            action(CreateAndPrintPackageLabels)
            {
                ApplicationArea = All;
                Caption = 'Create and Print Package Labels';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                PromotedOnly = true;
                ToolTip = 'Executes the Create and Print Package Labels action.';

                trigger OnAction()
                begin
                    FlexatiProductionMngt.CreatePackageNoInformationRecords(Rec);
                end;
            }
            action(CreateAndPrintPackageLabelsDirekt)
            {
                ApplicationArea = All;
                Caption = 'Create and Direct Print Package Labels';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                PromotedOnly = true;
                ToolTip = 'Executes the Create and Direct Print Package Labels action.';

                trigger OnAction()
                begin
                    FlexatiProductionMngt.CreatePackageNoInformationRecordsDirect(Rec);
                end;
            }
        }
    }
    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
    //QuantityToPlan: Decimal;
}
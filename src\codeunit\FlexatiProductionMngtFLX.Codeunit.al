codeunit 60001 "Flexati Production Mngt. FLX"
{
    SingleInstance = true;

    #region Coil Production

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnCreateProductionOrderOnBeforeProdOrderLineModify, '', false, false)]
    local procedure OnCreateProductionOrderOnBeforeProdOrderLineModify(var ProdOrderLine: Record "Prod. Order Line"; var SalesLine: Record "Sales Line"; var ProdOrder: Record "Production Order"; var SalesLineReserve: Codeunit "Sales Line-Reserve")
    begin
        AssignLotNoToProdOrderLine(ProdOrderLine, SalesLine);
    end;

    //[EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnCreateProdOrderOnBeforeProdOrderInsert, '', false, false)]
    // local procedure OnCreateProdOrderOnBeforeProdOrderInsert(var ProductionOrder: Record "Production Order"; SalesLine: Record "Sales Line");
    // begin
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Prod. Order Status Management", OnTransProdOrderOnBeforeToProdOrderInsert, '', false, false)]
    local procedure OnTransProdOrderOnBeforeToProdOrderInsert(var ToProdOrder: Record "Production Order"; FromProdOrder: Record "Production Order"; NewPostingDate: Date)
    begin
        ToProdOrder."No." := FromProdOrder."No.";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order Lines", OnCopyFromSalesOrderOnBeforeProdOrderLineModify, '', false, false)]
    local procedure OnCopyFromSalesOrderOnBeforeProdOrderLineModify(var ProdOrderLine: Record "Prod. Order Line"; SalesLine: Record "Sales Line"; SalesPlanningLine: Record "Sales Planning Line"; var NextProdOrderLineNo: Integer)
    begin
        AssignLotNoToProdOrderLine(ProdOrderLine, SalesLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnCreateProdOrderOnBeforeProdOrderInsert, '', false, false)]
    local procedure OnCreateProdOrderOnBeforeProdOrderInsert(var ProductionOrder: Record "Production Order"; SalesLine: Record "Sales Line")
    var
        SalesHeader: Record "Sales Header";
    begin
        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Shipment Location Code");
        FlexatiSetup.TestField("Shipment Bin Code");

        SalesLine.TestField("Location Code", FlexatiSetup."Shipment Location Code");
        SalesLine.TestField("Bin Code", FlexatiSetup."Shipment Bin Code");

        ProductionOrder."No." := SalesLine."Document No.";

        SalesHeader.Get(SalesLine."Document Type", SalesLine."Document No.");
        ProductionOrder."Note FLX" := SalesHeader."Note FLX";

        ProductionOrder."Sell-to Customer No. FLX" := SalesHeader."Sell-to Customer No.";
        ProductionOrder."Ship-to Code FLX" := SalesHeader."Ship-to Code";
    end;

    procedure AssignLotNoToProdOrderLine(var ProdOrderLine: Record "Prod. Order Line"; var SalesLine: Record "Sales Line")
    var
        Item: Record Item;
        NoSeries: Codeunit "No. Series";
    begin
        if not Item.Get(SalesLine."No.") then
            exit;

        if Item."Lot Nos." = '' then
            exit;
        SalesLine.CalcFields("Ship-to Code FLX", "Sell-to Customer Name FLX");

        ProdOrderLine."Lot No. FLX" := NoSeries.GetNextNo(Item."Lot Nos.", WorkDate(), true);
        ProdOrderLine."Piece FLX" := SalesLine."Piece FLX";
        ProdOrderLine."Hose Length FLX" := SalesLine."Hose Length FLX" * SalesLine."Qty. per Unit of Measure";
        ProdOrderLine."Source Line No. FLX" := SalesLine."Line No.";
        ProdOrderLine.Validate(Description, Item.Description);
        ProdOrderLine."Ship-to Code FLX" := SalesLine."Ship-to Code FLX";
        ProdOrderLine."Your Reference FLX" := SalesLine."Your Reference FLX";
        ProdOrderLine."Customer No. FLX" := SalesLine."Sell-to Customer No.";
        ProdOrderLine."Customer Name FLX" := SalesLine."Sell-to Customer Name FLX";
        ProdOrderLine."Prod. Order - Line No. FLX" := Format(ProdOrderLine."Prod. Order No.") + '-' + Format(ProdOrderLine."Line No." / 10000);
        ProdOrderLine."Sales Order - Line No. FLX" := Format(SalesLine."Document No.") + '-' + Format(SalesLine."Line No." / 10000);
        ProdOrderLine."Flexati Shipment Date FLX" := SalesLine."Flexati Shipment Date FLX";
        ProdOrderLine."Stop FLX" := true;
        ProdOrderLine."Sales Line Description FLX" := SalesLine.Description;
        SalesLine."Production Order No. FLX" := ProdOrderLine."Prod. Order No.";
        SalesLine."Production Order Line No. FLX" := ProdOrderLine."Line No.";
        SalesLine.Modify(true);

        CreateLotNoInformationRecord(ProdOrderLine, SalesLine);
    end;

    procedure OpenProductionPlanningWorksheetFromProdOrderLine(ProdOrderLine: Record "Prod. Order Line")
    var
        ProdOrderRoutingLine: Record "Prod. Order Routing Line";
    begin
        FlexatiSetup.Get();
        FlexatiSetup.TestField("Work Center Group for Cost");

        ProdOrderRoutingLine.SetRange(Status, ProdOrderLine.Status);
        ProdOrderRoutingLine.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderRoutingLine.SetRange("Routing Reference No.", ProdOrderLine."Routing Reference No.");
        ProdOrderRoutingLine.SetRange("Routing No.", ProdOrderLine."Routing No.");
        ProdOrderRoutingLine.SetFilter("Work Center Group Code", '<>%1', FlexatiSetup."Work Center Group for Cost");

#pragma warning disable LC0027
        Page.Run(Page::"Production Planning Worksheet", ProdOrderRoutingLine);
#pragma warning restore LC0027
        //PageManagement.PageRun(ProdOrderRoutingLine);
    end;

    procedure CalculatePositiveFinishedQuantityFromProdOrderRoutingLine(ProdOrderRoutingLine: Record "Prod. Order Routing Line"): Decimal
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProdOrderLine: Record "Prod. Order Line";
    //SalesHeader: Record "Sales Header";

    begin
        if not ProdOrderLine.Get(ProdOrderRoutingLine.Status, ProdOrderRoutingLine."Prod. Order No.", ProdOrderRoutingLine."Routing Reference No.") then
            exit(0);

        // if not SalesHeader.Get(SalesHeader."Document Type"::Order, ProdOrderRoutingLine."Prod. Order No.") then
        //     exit(0);

        //ItemLedgerEntry.SetAutoCalcFields("Include In Calcualtion FLX");
        ItemLedgerEntry.SetLoadFields("Remaining Quantity");
        ItemLedgerEntry.SetRange("Include In Calcualtion FLX", true);
        ItemLedgerEntry.SetRange("Item No.", ProdOrderLine."Item No.");
        ItemLedgerEntry.SetRange(Open, true);
        ItemLedgerEntry.SetRange("Lot No.", ProdOrderLine."Lot No. FLX");
        ItemLedgerEntry.CalcSums("Remaining Quantity");

        exit(ItemLedgerEntry."Remaining Quantity" - CalculateShippedQuantity(ProdOrderRoutingLine));
    end;

    procedure CalculatePositiveFinishedQtyFromProdOrderLine(ProdOrderLine: Record "Prod. Order Line"): Decimal
    var
        ProdOrderRoutingLine: Record "Prod. Order Routing Line";
    begin
        ProdOrderRoutingLine.SetRange(Status, ProdOrderLine.Status);
        ProdOrderRoutingLine.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderRoutingLine.SetRange("Routing Reference No.", ProdOrderLine."Line No.");

        ProdOrderRoutingLine.SetFilter("Line Lenght FLX", '<>%1', 0);
        if not ProdOrderRoutingLine.FindFirst() then
            exit(0);

        exit(CalculatePositiveFinishedQuantityFromProdOrderRoutingLine(ProdOrderRoutingLine));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnAfterCreateProdOrder, '', false, false)]
    local procedure OnAfterCreateProdOrder(var ProdOrder: Record "Production Order"; var SalesLine: Record "Sales Line")
    begin
        DeleteReservationEntryRecords(ProdOrder);
        UpdateOutputLocation(ProdOrder);
    end;

    local procedure DeleteReservationEntryRecords(var ProdOrder: Record "Production Order")
    var
        ReservationEntry: Record "Reservation Entry";
        ReservEngineMgt: Codeunit "Reservation Engine Mgt.";
    begin
        ReservationEntry.SetRange("Source Subtype", ReservationEntry."Source Subtype"::"2");
        ReservationEntry.SetRange("Source ID", ProdOrder."No.");
        ReservationEntry.SetRange("Source Ref. No.", 0);
        ReservationEntry.SetRange("Source Batch Name", '');
        ReservationEntry.SetRange("Source Type", Database::"Prod. Order Line");
        ReservationEntry.SetRange("Reservation Status", ReservationEntry."Reservation Status"::Reservation);
        if ReservationEntry.FindSet(true) then
            repeat
                ReservationEntry.TestField("Disallow Cancellation", false);
                ReservEngineMgt.CancelReservation(ReservationEntry);
            until ReservationEntry.Next() = 0;
    end;

    procedure UpdateOutputLocation(var ProdOrder: Record "Production Order")
    var
        Item: Record Item;
        ProdOrderComponent: Record "Prod. Order Component";
        ProdOrderLine: Record "Prod. Order Line";
        ProdOrderRoutingLine: Record "Prod. Order Routing Line";
    begin
        ProdOrderLine.SetRange(Status, ProdOrder.Status);
        ProdOrderLine.SetRange("Prod. Order No.", ProdOrder."No.");
        if ProdOrderLine.FindSet(true) then
            repeat
                Item.Get(ProdOrderLine."Item No.");
                Item.TestField("Production Output Location FLX");

                ProdOrderLine.Validate("Location Code", Item."Production Output Location FLX");
                ProdOrderLine.Modify(true);

                ProdOrderRoutingLine.SetRange("Routing No.", ProdOrderLine."Routing No.");
                ProdOrderRoutingLine.SetRange("Routing Reference No.", ProdOrderLine."Routing Reference No.");
                ProdOrderRoutingLine.SetRange(Status, ProdOrder.Status);
                ProdOrderRoutingLine.SetRange("Prod. Order No.", ProdOrder."No.");
                if ProdOrderRoutingLine.FindSet(true) then
                    repeat
                        ProdOrderRoutingLine.Validate("Location Code", Item."Production Output Location FLX");
                        ProdOrderRoutingLine.Validate("From-Production Bin Code", Item."Production Output Bin Code FLX");
                        ProdOrderRoutingLine.Modify(true);
                    until ProdOrderRoutingLine.Next() = 0;

                ProdOrderComponent.SetRange(Status, ProdOrderLine.Status);
                ProdOrderComponent.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
                ProdOrderComponent.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");
                if ProdOrderComponent.FindSet(true) then
                    repeat
                        ProdOrderComponent.Validate("Location Code", Item."Production Output Location FLX");
                        ProdOrderComponent.Modify(true);
                    until ProdOrderComponent.Next() = 0;
            until ProdOrderLine.Next() = 0;
    end;

    local procedure CreateLotNoInformationRecord(var ProdOrderLine: Record "Prod. Order Line"; var SalesLine: Record "Sales Line")
    var
        Item: Record Item;
        LotNoInformation: Record "Lot No. Information";
    begin
        SalesLine.CalcFields("Ship-to Code FLX", "Sell-to Customer Name FLX");
        Item.Get(SalesLine."No.");

        LotNoInformation.Init();
        LotNoInformation."Item No." := ProdOrderLine."Item No.";
        LotNoInformation."Variant Code" := ProdOrderLine."Variant Code";
        LotNoInformation."Lot No." := ProdOrderLine."Lot No. FLX";
        LotNoInformation.Insert(true);
        LotNoInformation.Validate(Description, ProdOrderLine.Description);
        LotNoInformation.Validate("Hose Lenght FLX", ProdOrderLine."Hose Length FLX");
        LotNoInformation.Validate("Ship-to Code FLX", SalesLine."Ship-to Code FLX");
        LotNoInformation.Validate("Your Reference FLX", SalesLine."Your Reference FLX");
        LotNoInformation.Validate("Sales Order No. FLX", SalesLine."Document No.");
        LotNoInformation.Validate("Sales Order Line No. FLX", SalesLine."Line No.");
        LotNoInformation.Validate("Production Order No. FLX", ProdOrderLine."Prod. Order No.");
        LotNoInformation.Validate("Sell-to Customer No. FLX", SalesLine."Sell-to Customer No.");
        LotNoInformation.Validate("Sell-to Customer Name FLX", SalesLine."Sell-to Customer Name FLX");
        LotNoInformation.Validate("ID (mm) FLX", Item."ID (mm) FLX");
        LotNoInformation.Validate("OD (mm) FLX", Item."OD (mm) FLX");
        LotNoInformation.Validate("WP (bar) FLX", Item."WP (bar) FLX");
        LotNoInformation.Validate("BP (bar) FLX", Item."BP (bar) FLX");
        LotNoInformation.Modify(true);
    end;

    procedure QuantityToPlanChecks(var ProdOrderRoutingLine: Record "Prod. Order Routing Line")
    var
        WorkCenter: Record "Work Center";
        HoseLenghtErr: Label 'Hose Lenght can not be greater than Line Lenght.';
        RoundedMsg: Label 'New Quantity to Plan rounded to %1', Comment = '%1="Prod. Order Routing Line"."Quantity to Plan FLX"';
    begin
        if ProdOrderRoutingLine."Hose Lenght FLX" > ProdOrderRoutingLine."Line Lenght FLX" then
            Error(HoseLenghtErr);

        ProdOrderRoutingLine.CalcFields("Hose Lenght FLX", "Line Lenght FLX");
        // Check if Skip Rounding is enabled for the related Work Center
        if WorkCenter.Get(ProdOrderRoutingLine."Work Center No.") then
            if not WorkCenter."Skip Rounding FLX" then
                if (ProdOrderRoutingLine."Quantity to Plan FLX" mod ProdOrderRoutingLine."Hose Lenght FLX") <> 0 then begin
                    ProdOrderRoutingLine."Quantity to Plan FLX" := ProdOrderRoutingLine."Quantity to Plan FLX" + (ProdOrderRoutingLine."Hose Lenght FLX" - (ProdOrderRoutingLine."Quantity to Plan FLX" mod ProdOrderRoutingLine."Hose Lenght FLX"));
                    Message(RoundedMsg, ProdOrderRoutingLine."Quantity to Plan FLX");
                end;

        if ProdOrderRoutingLine."Input Quantity" - CalculatePositiveFinishedQuantityFromProdOrderRoutingLine(ProdOrderRoutingLine) < ProdOrderRoutingLine."Quantity to Plan FLX" then
            Error(QuantityErr);

        // if ProdOrderRoutingLine."Printed Head Label Lenght FLX" - CalculatePositiveFinishedQuantity(ProdOrderRoutingLine) < ProdOrderRoutingLine."Quantity to Plan FLX" then
        //     Error(QuantityErr);

        //CreatePackageNoInformationRecords(ProdOrderRoutingLine, QuantityToPlan);
    end;

    procedure CreatePackageNoInformationRecords(var ProdOrderRoutingLine: Record "Prod. Order Routing Line")
    var
        Item: Record Item;
        LotNoInformation: Record "Lot No. Information";
        PackageNoInformation: Record "Package No. Information";
        ProdOrderLine: Record "Prod. Order Line";
        NoSeries: Codeunit "No. Series";
        //LocalPrinterMgtBPS: Codeunit "Local Printer Mgt. BPS INF";
        FullHeadLabelLenght: Decimal;
        LastHeadLabelLenght: Decimal;
        CreatedPackageCount: Integer;
        FullHeadLabelCount: Integer;
        PackageNoFilter: Text[1024];
        CreatedPackageCountMsg: Label 'Total %1 Packages has been created.', Comment = '%1=CreatedPackageCount';
    begin
        ProdOrderRoutingLine.TestField("Quantity to Plan FLX");

        ProdOrderLine.Get(ProdOrderRoutingLine.Status, ProdOrderRoutingLine."Prod. Order No.", ProdOrderRoutingLine."Routing Reference No.");
        Item.Get(ProdOrderLine."Item No.");

        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Head Label No. Series");

        if (ProdOrderRoutingLine."Line Lenght FLX" / ProdOrderRoutingLine."Hose Lenght FLX") >= 2 then
            FullHeadLabelLenght := ProdOrderRoutingLine."Hose Lenght FLX" * Round(ProdOrderRoutingLine."Line Lenght FLX" / ProdOrderRoutingLine."Hose Lenght FLX", 1, '<')
        else
            FullHeadLabelLenght := ProdOrderRoutingLine."Hose Lenght FLX";

        FullHeadLabelCount := Round(ProdOrderRoutingLine."Quantity to Plan FLX" / FullHeadLabelLenght, 1, '<');

        LastHeadLabelLenght := ProdOrderRoutingLine."Quantity to Plan FLX" - (FullHeadLabelCount * FullHeadLabelLenght);

        //Message('Tam Etiket Uzunluğu: %1\Tam Etiket Sayısı: %2\Son Etiket Uzunluğu: %3', FullHeadLabelLenght, FullHeadLabelCount, LastHeadLabelLenght);

        CreatedPackageCount := 0;

        LotNoInformation.Get(ProdOrderLine."Item No.", ProdOrderLine."Variant Code", ProdOrderLine."Lot No. FLX");

        if FullHeadLabelCount > 0 then
            repeat
                Clear(PackageNoInformation);
                PackageNoInformation.Init();
                PackageNoInformation."Item No." := LotNoInformation."Item No.";
                PackageNoInformation."Variant Code" := LotNoInformation."Variant Code";
                PackageNoInformation."Package No." := NoSeries.GetNextNo(FlexatiSetup."Head Label No. Series", WorkDate(), true);
                PackageNoInformation.Insert(true);
                PackageNoInformation.Validate(Description, Item.Description);
                PackageNoInformation.Validate("Package Type FLX", PackageNoInformation."Package Type FLX"::Head);
                PackageNoInformation.Validate("Lot No. FLX", LotNoInformation."Lot No.");
                PackageNoInformation.Validate("Your Reference FLX", LotNoInformation."Your Reference FLX");
                PackageNoInformation.Validate("Sell-to Customer No. FLX", LotNoInformation."Sell-to Customer No. FLX");
                PackageNoInformation.Validate("Sell-to Customer Name FLX", LotNoInformation."Sell-to Customer Name FLX");
                PackageNoInformation.Validate("Hose Lenght FLX", LotNoInformation."Hose Lenght FLX");
                PackageNoInformation.Validate("Ship-to Code FLX", LotNoInformation."Ship-to Code FLX");
                PackageNoInformation.Validate("Sales Order No. FLX", LotNoInformation."Sales Order No. FLX");
                PackageNoInformation.Validate("Sales Order Line No. FLX", LotNoInformation."Sales Order Line No. FLX");
                PackageNoInformation.Validate("Sales Order - Line No. FLX", PackageNoInformation."Sales Order No. FLX" + '-' + Format(PackageNoInformation."Sales Order Line No. FLX" / 10000));
                PackageNoInformation.Validate("Production Order No. FLX", LotNoInformation."Production Order No. FLX");
                PackageNoInformation.Validate("Label Lenght FLX", FullHeadLabelLenght);
                PackageNoInformation.Validate("ID (mm) FLX", LotNoInformation."ID (mm) FLX");
                PackageNoInformation.Validate("OD (mm) FLX", LotNoInformation."OD (mm) FLX");
                PackageNoInformation.Validate("BP (bar) FLX", LotNoInformation."BP (bar) FLX");
                PackageNoInformation.Validate("WP (bar) FLX", LotNoInformation."WP (bar) FLX");
                PackageNoInformation.Validate("Work Center No. FLX", ProdOrderRoutingLine."Work Center No.");
                PackageNoInformation.Validate("Production Order Line No. FLX", ProdOrderRoutingLine."Routing Reference No.");
                //PackageNoInformation.Validate("Weight (KG) FLX", PackageNoInformation."Label Lenght FLX" * FlexatiSalesManagement.CalculateCoilWeightFromItemNo(PackageNoInformation."Item No."));
                PackageNoInformation.Modify(true);
                if PackageNoFilter <> '' then
                    PackageNoFilter += '|';
                PackageNoFilter += PackageNoInformation."Package No.";
                //PackageNoInformation.Mark(true);

                // PackageNoInformation.SetRecFilter();
                // Report.Run(Report::"QR Label FLX", false, true, PackageNoInformation);

                CreatedPackageCount += 1;
            until CreatedPackageCount = FullHeadLabelCount;

        if LastHeadLabelLenght <> 0 then begin
            Clear(PackageNoInformation);
            PackageNoInformation.Init();
            PackageNoInformation."Item No." := LotNoInformation."Item No.";
            PackageNoInformation."Variant Code" := LotNoInformation."Variant Code";
            PackageNoInformation."Package No." := NoSeries.GetNextNo(FlexatiSetup."Head Label No. Series", WorkDate(), true);
            PackageNoInformation.Insert(true);
            PackageNoInformation.Validate(Description, Item.Description);
            PackageNoInformation.Validate("Package Type FLX", PackageNoInformation."Package Type FLX"::Head);
            PackageNoInformation.Validate("Lot No. FLX", LotNoInformation."Lot No.");
            PackageNoInformation.Validate("Your Reference FLX", LotNoInformation."Your Reference FLX");
            PackageNoInformation.Validate("Sell-to Customer No. FLX", LotNoInformation."Sell-to Customer No. FLX");
            PackageNoInformation.Validate("Sell-to Customer Name FLX", LotNoInformation."Sell-to Customer Name FLX");
            PackageNoInformation.Validate("Hose Lenght FLX", LotNoInformation."Hose Lenght FLX");
            PackageNoInformation.Validate("Ship-to Code FLX", LotNoInformation."Ship-to Code FLX");
            PackageNoInformation.Validate("Sales Order No. FLX", LotNoInformation."Sales Order No. FLX");
            PackageNoInformation.Validate("Sales Order Line No. FLX", LotNoInformation."Sales Order Line No. FLX");
            PackageNoInformation.Validate("Sales Order - Line No. FLX", PackageNoInformation."Sales Order No. FLX" + '-' + Format(PackageNoInformation."Sales Order Line No. FLX" / 10000));
            PackageNoInformation.Validate("Production Order No. FLX", LotNoInformation."Production Order No. FLX");
            PackageNoInformation.Validate("Label Lenght FLX", LastHeadLabelLenght);
            PackageNoInformation.Validate("ID (mm) FLX", LotNoInformation."ID (mm) FLX");
            PackageNoInformation.Validate("OD (mm) FLX", LotNoInformation."OD (mm) FLX");
            PackageNoInformation.Validate("BP (bar) FLX", LotNoInformation."BP (bar) FLX");
            PackageNoInformation.Validate("WP (bar) FLX", LotNoInformation."WP (bar) FLX");
            PackageNoInformation.Validate("Work Center No. FLX", ProdOrderRoutingLine."Work Center No.");
            PackageNoInformation.Validate("Production Order Line No. FLX", ProdOrderRoutingLine."Routing Reference No.");
            //PackageNoInformation.Validate("Weight (KG) FLX", PackageNoInformation."Label Lenght FLX" * FlexatiSalesManagement.CalculateCoilWeightFromItemNo(PackageNoInformation."Item No."));
            PackageNoInformation.Modify(true);

            //PackageNoInformation.Mark(true);
            if PackageNoFilter <> '' then
                PackageNoFilter += '|';
            PackageNoFilter += PackageNoInformation."Package No.";

            // PackageNoInformation.SetRecFilter();
            // Report.Run(Report::"QR Label FLX", false, true, PackageNoInformation);

            CreatedPackageCount += 1;
        end;

        Message(CreatedPackageCountMsg, CreatedPackageCount);

        ProdOrderRoutingLine."Quantity to Plan FLX" := 0;
        ProdOrderRoutingLine.Modify(true);
        Commit();//

        //PackageNoInformation.MarkedOnly(true);
        PackageNoInformation.SetFilter("Package No.", PackageNoFilter);
        PackageNoInformation.FindFirst();
        Report.RunModal(Report::"QR Label FLX", true, false, PackageNoInformation);
        //LocalPrinterMgtBPS.SendPdfDocumentToLocalPrinter(PackageNoInformation, Report::"QR Label FLX");
        //PackageNoInformation.ClearMarks();
    end;

    procedure CreatePackageNoInformationRecordsDirect(var ProdOrderRoutingLine: Record "Prod. Order Routing Line")
    var
        Item: Record Item;
        LotNoInformation: Record "Lot No. Information";
        PackageNoInformation: Record "Package No. Information";
        ProdOrderLine: Record "Prod. Order Line";
        NoSeries: Codeunit "No. Series";
        LocalPrinterMgtBPS: Codeunit "Local Printer Mgt. BPS INF";
        FullHeadLabelLenght: Decimal;
        LastHeadLabelLenght: Decimal;
        CreatedPackageCount: Integer;
        FullHeadLabelCount: Integer;
        PackageNoFilter: Text[1024];
        CreatedPackageCountMsg: Label 'Total %1 Packages has been created.', Comment = '%1=CreatedPackageCount';
    begin
        ProdOrderRoutingLine.TestField("Quantity to Plan FLX");

        ProdOrderLine.Get(ProdOrderRoutingLine.Status, ProdOrderRoutingLine."Prod. Order No.", ProdOrderRoutingLine."Routing Reference No.");
        Item.Get(ProdOrderLine."Item No.");

        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Head Label No. Series");

        if (ProdOrderRoutingLine."Line Lenght FLX" / ProdOrderRoutingLine."Hose Lenght FLX") >= 2 then
            FullHeadLabelLenght := ProdOrderRoutingLine."Hose Lenght FLX" * Round(ProdOrderRoutingLine."Line Lenght FLX" / ProdOrderRoutingLine."Hose Lenght FLX", 1, '<')
        else
            FullHeadLabelLenght := ProdOrderRoutingLine."Hose Lenght FLX";

        FullHeadLabelCount := Round(ProdOrderRoutingLine."Quantity to Plan FLX" / FullHeadLabelLenght, 1, '<');

        LastHeadLabelLenght := ProdOrderRoutingLine."Quantity to Plan FLX" - (FullHeadLabelCount * FullHeadLabelLenght);

        //Message('Tam Etiket Uzunluğu: %1\Tam Etiket Sayısı: %2\Son Etiket Uzunluğu: %3', FullHeadLabelLenght, FullHeadLabelCount, LastHeadLabelLenght);

        CreatedPackageCount := 0;

        LotNoInformation.Get(ProdOrderLine."Item No.", ProdOrderLine."Variant Code", ProdOrderLine."Lot No. FLX");

        if FullHeadLabelCount > 0 then
            repeat
                Clear(PackageNoInformation);
                PackageNoInformation.Init();
                PackageNoInformation."Item No." := LotNoInformation."Item No.";
                PackageNoInformation."Variant Code" := LotNoInformation."Variant Code";
                PackageNoInformation."Package No." := NoSeries.GetNextNo(FlexatiSetup."Head Label No. Series", WorkDate(), true);
                PackageNoInformation.Insert(true);
                PackageNoInformation.Validate(Description, Item.Description);
                PackageNoInformation.Validate("Package Type FLX", PackageNoInformation."Package Type FLX"::Head);
                PackageNoInformation.Validate("Lot No. FLX", LotNoInformation."Lot No.");
                PackageNoInformation.Validate("Your Reference FLX", LotNoInformation."Your Reference FLX");
                PackageNoInformation.Validate("Sell-to Customer No. FLX", LotNoInformation."Sell-to Customer No. FLX");
                PackageNoInformation.Validate("Sell-to Customer Name FLX", LotNoInformation."Sell-to Customer Name FLX");
                PackageNoInformation.Validate("Hose Lenght FLX", LotNoInformation."Hose Lenght FLX");
                PackageNoInformation.Validate("Ship-to Code FLX", LotNoInformation."Ship-to Code FLX");
                PackageNoInformation.Validate("Sales Order No. FLX", LotNoInformation."Sales Order No. FLX");
                PackageNoInformation.Validate("Sales Order Line No. FLX", LotNoInformation."Sales Order Line No. FLX");
                PackageNoInformation.Validate("Sales Order - Line No. FLX", PackageNoInformation."Sales Order No. FLX" + '-' + Format(PackageNoInformation."Sales Order Line No. FLX" / 10000));
                PackageNoInformation.Validate("Production Order No. FLX", LotNoInformation."Production Order No. FLX");
                PackageNoInformation.Validate("Label Lenght FLX", FullHeadLabelLenght);
                PackageNoInformation.Validate("ID (mm) FLX", LotNoInformation."ID (mm) FLX");
                PackageNoInformation.Validate("OD (mm) FLX", LotNoInformation."OD (mm) FLX");
                PackageNoInformation.Validate("BP (bar) FLX", LotNoInformation."BP (bar) FLX");
                PackageNoInformation.Validate("WP (bar) FLX", LotNoInformation."WP (bar) FLX");
                PackageNoInformation.Validate("Work Center No. FLX", ProdOrderRoutingLine."Work Center No.");
                PackageNoInformation.Validate("Production Order Line No. FLX", ProdOrderRoutingLine."Routing Reference No.");
                //PackageNoInformation.Validate("Weight (KG) FLX", PackageNoInformation."Label Lenght FLX" * FlexatiSalesManagement.CalculateCoilWeightFromItemNo(PackageNoInformation."Item No."));
                PackageNoInformation.Modify(true);
                if PackageNoFilter <> '' then
                    PackageNoFilter += '|';
                PackageNoFilter += PackageNoInformation."Package No.";
                //PackageNoInformation.Mark(true);

                // PackageNoInformation.SetRecFilter();
                // Report.Run(Report::"QR Label FLX", false, true, PackageNoInformation);

                CreatedPackageCount += 1;
            until CreatedPackageCount = FullHeadLabelCount;

        if LastHeadLabelLenght <> 0 then begin
            Clear(PackageNoInformation);
            PackageNoInformation.Init();
            PackageNoInformation."Item No." := LotNoInformation."Item No.";
            PackageNoInformation."Variant Code" := LotNoInformation."Variant Code";
            PackageNoInformation."Package No." := NoSeries.GetNextNo(FlexatiSetup."Head Label No. Series", WorkDate(), true);
            PackageNoInformation.Insert(true);
            PackageNoInformation.Validate(Description, Item.Description);
            PackageNoInformation.Validate("Package Type FLX", PackageNoInformation."Package Type FLX"::Head);
            PackageNoInformation.Validate("Lot No. FLX", LotNoInformation."Lot No.");
            PackageNoInformation.Validate("Your Reference FLX", LotNoInformation."Your Reference FLX");
            PackageNoInformation.Validate("Sell-to Customer No. FLX", LotNoInformation."Sell-to Customer No. FLX");
            PackageNoInformation.Validate("Sell-to Customer Name FLX", LotNoInformation."Sell-to Customer Name FLX");
            PackageNoInformation.Validate("Hose Lenght FLX", LotNoInformation."Hose Lenght FLX");
            PackageNoInformation.Validate("Ship-to Code FLX", LotNoInformation."Ship-to Code FLX");
            PackageNoInformation.Validate("Sales Order No. FLX", LotNoInformation."Sales Order No. FLX");
            PackageNoInformation.Validate("Sales Order Line No. FLX", LotNoInformation."Sales Order Line No. FLX");
            PackageNoInformation.Validate("Sales Order - Line No. FLX", PackageNoInformation."Sales Order No. FLX" + '-' + Format(PackageNoInformation."Sales Order Line No. FLX" / 10000));
            PackageNoInformation.Validate("Production Order No. FLX", LotNoInformation."Production Order No. FLX");
            PackageNoInformation.Validate("Label Lenght FLX", LastHeadLabelLenght);
            PackageNoInformation.Validate("ID (mm) FLX", LotNoInformation."ID (mm) FLX");
            PackageNoInformation.Validate("OD (mm) FLX", LotNoInformation."OD (mm) FLX");
            PackageNoInformation.Validate("BP (bar) FLX", LotNoInformation."BP (bar) FLX");
            PackageNoInformation.Validate("WP (bar) FLX", LotNoInformation."WP (bar) FLX");
            PackageNoInformation.Validate("Work Center No. FLX", ProdOrderRoutingLine."Work Center No.");
            PackageNoInformation.Validate("Production Order Line No. FLX", ProdOrderRoutingLine."Routing Reference No.");
            //PackageNoInformation.Validate("Weight (KG) FLX", PackageNoInformation."Label Lenght FLX" * FlexatiSalesManagement.CalculateCoilWeightFromItemNo(PackageNoInformation."Item No."));
            PackageNoInformation.Modify(true);

            //PackageNoInformation.Mark(true);
            if PackageNoFilter <> '' then
                PackageNoFilter += '|';
            PackageNoFilter += PackageNoInformation."Package No.";

            // PackageNoInformation.SetRecFilter();
            // Report.Run(Report::"QR Label FLX", false, true, PackageNoInformation);

            CreatedPackageCount += 1;
        end;

        Message(CreatedPackageCountMsg, CreatedPackageCount);

        ProdOrderRoutingLine."Quantity to Plan FLX" := 0;
        ProdOrderRoutingLine.Modify(true);
        Commit();//

        //PackageNoInformation.MarkedOnly(true);
        PackageNoInformation.SetFilter("Package No.", PackageNoFilter);
        PackageNoInformation.FindFirst();
        //Report.Runmodal(Report::"QR Label FLX", true, false, PackageNoInformation);
        LocalPrinterMgtBPS.SendPdfDocumentToLocalPrinter(PackageNoInformation, Report::"QR Label FLX");
        //PackageNoInformation.ClearMarks();
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnAfterInsertItemLedgEntry, '', false, false)]
    local procedure OnAfterInsertItemLedgEntry(var ItemLedgerEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; var ItemLedgEntryNo: Integer; var ValueEntryNo: Integer; var ItemApplnEntryNo: Integer; GlobalValueEntry: Record "Value Entry"; TransferItem: Boolean; var InventoryPostingToGL: Codeunit "Inventory Posting To G/L"; var OldItemLedgerEntry: Record "Item Ledger Entry")
    begin
        PopulateProducedAtOnPackageNoInformation(ItemLedgerEntry);
    end;

    local procedure PopulateProducedAtOnPackageNoInformation(ItemLedgerEntry: Record "Item Ledger Entry")
    var
        ItemLedgerEntry2: Record "Item Ledger Entry";
        PackageNoInformation: Record "Package No. Information";
    begin
        if not PackageNoInformation.Get(ItemLedgerEntry."Item No.", ItemLedgerEntry."Variant Code", ItemLedgerEntry."Package No.") then
            exit;

        ItemLedgerEntry2.SetRange("Package No.", PackageNoInformation."Package No.");
        //if ItemLedgerEntry2.Count() = 1 then begin
        if ItemLedgerEntry2.Find('-') and (ItemLedgerEntry2.Next() = 0) then begin
            PackageNoInformation."Produced At FLX" := CurrentDateTime();
            PackageNoInformation.Modify(true);
        end;
    end;

    procedure StartProductionProcess(var ProductionOperation: Record "Production Operation FLX")
    var
        ItemJournalLine: Record "Item Journal Line";
        PackageNoInformation: Record "Package No. Information";
        ProdOrderLine: Record "Prod. Order Line";
        ProdOrderRoutingLine: Record "Prod. Order Routing Line";
        Item: Record Item;
        JournalBatchName: Code[10];
        AlreadyStartedQst: Label 'This packages production has been started by User: %1. Do you want to delete those lines and start again?', Comment = '%1="Item Journal Line"."Journal Batch Name"';
        ClearOldLinesQst: Label 'Do you want to clear the lines for Package No.: %1 that have already been started in production and continue processing?', Comment = '%1="Package No."';
        HasBeenProducedErr: Label 'This package has been produced.';
        ProcessAbortedErr: Label 'Process aborted.';
        RemaningQtyNotEnoughErr: Label 'You can not start production for this package. Remaining quantity is not enough.';
        WorkCenterErr: Label 'You can only produce this label on Work Center No.: %1', Comment = '%1="Package No. Information"."Work Center No. FLX"';
        AlreadyStartedTxt: Text;
        ClearOldLinesTxt: Text;
    begin
        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Work Center Group for Cost");

        PackageNoInformation.SetRange("Package No.", ProductionOperation."Package No.");
        PackageNoInformation.FindFirst();

        Item.Get(PackageNoInformation."Item No.");

        ProdOrderRoutingLine.SetRange("Prod. Order No.", PackageNoInformation."Production Order No. FLX");
        ProdOrderRoutingLine.SetRange("Routing Reference No.", PackageNoInformation."Production Order Line No. FLX");
        ProdOrderRoutingLine.SetRange("Routing No.", Item."Routing No.");
        ProdOrderRoutingLine.SetRange("No.", PackageNoInformation."Work Center No. FLX");
#pragma warning disable AA0210
        ProdOrderRoutingLine.SetFilter("Work Center Group Code", '<>%1', FlexatiSetup."Work Center Group for Cost");
#pragma warning restore AA0210
        ProdOrderRoutingLine.FindFirst();

        if ProdOrderRoutingLine."Input Quantity" - CalculatePositiveFinishedQuantityFromProdOrderRoutingLine(ProdOrderRoutingLine) < PackageNoInformation."Label Lenght FLX" then
            Error(RemaningQtyNotEnoughErr);

        if PackageNoInformation."Produced At FLX" <> 0DT then
            Error(HasBeenProducedErr);

        if PackageNoInformation."Work Center No. FLX" <> ProductionOperation."Work Center No." then
            Error(WorkCenterErr, PackageNoInformation."Work Center No. FLX");

        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Consumption Jnl. Template Name");
        FlexatiSetup.TestField("Output Jnl. Template Name");

        ItemJournalLine.SetFilter("Journal Template Name", '%1|%2', FlexatiSetup."Consumption Jnl. Template Name", FlexatiSetup."Output Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ProductionOperation."User ID");
        ItemJournalLine.SetFilter("Package No.", '<>%1', '');
        if ItemJournalLine.FindSet(true) then begin
            ClearOldLinesTxt := StrSubstNo(ClearOldLinesQst, ItemJournalLine."Package No.");
            if not ConfirmManagement.GetResponseOrDefault(ClearOldLinesTxt, false) then
                Error(ProcessAbortedErr)
            else begin
                ItemJournalLine.SetRange("Package No.");
                ItemJournalLine.DeleteAll(true);
            end;
        end;

        ItemJournalLine.Reset();
        ItemJournalLine.SetRange("Package No.", ProductionOperation."Package No.");
        ItemJournalLine.SetRange("Order Type", ItemJournalLine."Order Type"::Production);
        if ItemJournalLine.FindFirst() then begin
            AlreadyStartedTxt := StrSubstNo(AlreadyStartedQst, ItemJournalLine."Journal Batch Name");
            if not ConfirmManagement.GetResponseOrDefault(AlreadyStartedTxt, false) then
                Error(ProcessAbortedErr)
            else begin
                JournalBatchName := ItemJournalLine."Journal Batch Name";

                ItemJournalLine.Reset();
                ItemJournalLine.SetFilter("Journal Template Name", '%1|%2', FlexatiSetup."Consumption Jnl. Template Name", FlexatiSetup."Output Jnl. Template Name");
                ItemJournalLine.SetRange("Journal Batch Name", JournalBatchName);
                ItemJournalLine.DeleteAll(true);
            end;
        end;
        ItemJournalLine.Reset();
        ItemJournalLine.SetFilter("Journal Template Name", '%1|%2', FlexatiSetup."Consumption Jnl. Template Name", FlexatiSetup."Output Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ProductionOperation."User ID");
        if ItemJournalLine.FindFirst() then
            ItemJournalLine.DeleteAll(true);

        ProdOrderLine.Get(ProdOrderLine.Status::Released, PackageNoInformation."Production Order No. FLX", PackageNoInformation."Production Order Line No. FLX");

        //Create Consumption Journal Lines
        CreateConsumptionJournalLines(ProductionOperation, PackageNoInformation, ProdOrderLine);

        //Create Output Journal Lines
        CreateOutputJournalLines(ProductionOperation, PackageNoInformation);
    end;

    local procedure CreateConsumptionJournalLines(var ProductionOperation: Record "Production Operation FLX"; var PackageNoInformation: Record "Package No. Information"; var ProdOrderLine: Record "Prod. Order Line")
    var
        Item: Record Item;
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ProdOrderComponent: Record "Prod. Order Component";
        UnitofMeasureManagement: Codeunit "Unit of Measure Management";
        QtyToConsume: Decimal;
    begin
        FlexatiSetup.GetRecordOnce();

        ProdOrderComponent.SetRange(Status, ProdOrderComponent.Status::Released);
        ProdOrderComponent.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderComponent.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");
        ProdOrderComponent.FindSet();
        repeat
            Item.Get(ProdOrderComponent."Item No.");
            QtyToConsume := ProdOrderComponent."Quantity per" * PackageNoInformation."Label Lenght FLX" * (1 + (ProdOrderComponent."Scrap %" / 100));

            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := FlexatiSetup."Consumption Jnl. Template Name";
            ItemJournalLine."Journal Batch Name" := ProductionOperation."User ID";
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine."Line No." := LastItemJournalLine."Line No." + 10000;

            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Consumption);
            ItemJournalLine.Validate("Order Type", ItemJournalLine."Order Type"::Production);
            ItemJournalLine.Validate("Order No.", ProdOrderComponent."Prod. Order No.");
            ItemJournalLine.Validate("Source No.", ProdOrderLine."Item No.");
            ItemJournalLine.Validate("Posting Date", WorkDate());
            ItemJournalLine.Validate("Item No.", ProdOrderComponent."Item No.");
            ItemJournalLine.Validate("Unit of Measure Code", ProdOrderComponent."Unit of Measure Code");
            ItemJournalLine.Description := ProdOrderComponent.Description;
            ItemJournalLine.Validate(Quantity, UnitofMeasureManagement.RoundToItemRndPrecision(QtyToConsume, Item."Rounding Precision"));
            ItemJournalLine."Variant Code" := ProdOrderComponent."Variant Code";
            ItemJournalLine.Validate("Location Code", ProdOrderComponent."Location Code");
            ItemJournalLine.Validate("Order Line No.", ProdOrderComponent."Prod. Order Line No.");
            ItemJournalLine.Validate("Prod. Order Comp. Line No.", ProdOrderComponent."Line No.");
            ItemJournalLine.Insert(false);

            LastItemJournalLine := ItemJournalLine;
        until ProdOrderComponent.Next() = 0;
    end;

    local procedure CreateOutputJournalLines(ProductionOperation: Record "Production Operation FLX"; PackageNoInformation: Record "Package No. Information")
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        OutputJnlExplRoute: Codeunit "Output Jnl.-Expl. Route";
    begin
        FlexatiSetup.GetRecordOnce();

        ItemJournalLine.Init();
        ItemJournalLine."Journal Template Name" := FlexatiSetup."Output Jnl. Template Name";
        ItemJournalLine."Journal Batch Name" := ProductionOperation."User ID";
        ItemJournalLine.SetUpNewLine(LastItemJournalLine);
        ItemJournalLine."Line No." := LastItemJournalLine."Line No." + 10000;
        ItemJournalLine.Validate("Order Type", ItemJournalLine."Order Type"::Production);
        ItemJournalLine.Validate("Order No.", PackageNoInformation."Production Order No. FLX");
        ItemJournalLine.Validate("Order Line No.", PackageNoInformation."Production Order Line No. FLX");
        ItemJournalLine.Insert(true);

        GlobalPackageNoInformation := PackageNoInformation;
        GlobalProductionOperation := ProductionOperation;
        OutputJnlExplRoute.Run(ItemJournalLine);
        Clear(GlobalPackageNoInformation);
        Clear(GlobalProductionOperation);
    end;

    procedure CalculateShippedQuantity(ProdOrderRoutingLine: Record "Prod. Order Routing Line"): Decimal
    var
        ShippedItemLedgerEntry: Record "Item Ledger Entry";
    begin
        ProdOrderRoutingLine.CalcFields("Item No. FLX");

        ShippedItemLedgerEntry.SetLoadFields(Quantity);
        ShippedItemLedgerEntry.SetRange("Entry Type", ShippedItemLedgerEntry."Entry Type"::Sale);
        //ShippedItemLedgerEntry.SetRange("Source No.", GetSelltoCustomerNoFromProdOrderRoutingLine(ProdOrderRoutingLine));
        ShippedItemLedgerEntry.SetRange("Item No.", ProdOrderRoutingLine."Item No. FLX");
        ShippedItemLedgerEntry.SetRange("Lot No.", GetLotNoFromProdOrderRoutingLine(ProdOrderRoutingLine));
        ShippedItemLedgerEntry.CalcSums(Quantity);

        exit(ShippedItemLedgerEntry.Quantity);
    end;

    local procedure GetLotNoFromProdOrderRoutingLine(ProdOrderRoutingLine: Record "Prod. Order Routing Line"): Code[50]
    var
        ProdOrderLine: Record "Prod. Order Line";
    begin
        ProdOrderLine.Get(ProdOrderRoutingLine.Status, ProdOrderRoutingLine."Prod. Order No.", ProdOrderRoutingLine."Routing Reference No.");
        exit(ProdOrderLine."Lot No. FLX");
    end;



    // procedure GetSelltoCustomerNoFromProdOrderRoutingLine(ProdOrderRoutingLine: Record "Prod. Order Routing Line"): Code[20]
    // var
    //     ProdOrderLine: Record "Prod. Order Line";
    //     SalesLine: Record "Sales Line";
    // begin
    //     ProdOrderLine.Get(ProdOrderRoutingLine.Status, ProdOrderRoutingLine."Prod. Order No.", ProdOrderRoutingLine."Routing Reference No.");
    //     SalesLine.Get(SalesLine."Document Type"::Order, ProdOrderLine."Prod. Order No.", ProdOrderLine."Source Line No. FLX");
    //     exit(SalesLine."Sell-to Customer No.");
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnAfterCalcBaseQtyToPost, '', false, false)]
    local procedure OnAfterCalcBaseQtyToPost(var ProdOrderRoutingLine: Record "Prod. Order Routing Line"; var BaseQty: Decimal)
    begin
        //In order to make more production than planned, we need to set BaseQty >= 1. 
        //We already updating the quantity to Hose Lenght but if BaseQty <= 0 it skips whole line creation function.
        if BaseQty <= 0 then
            BaseQty := 1;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeInsertOutputJnlLineWithRtngLine, '', false, false)]
    local procedure OnBeforeInsertOutputJnlLineWithRtngLine(ItemJournalLine: Record "Item Journal Line"; ProdOrderLine: Record "Prod. Order Line"; var SkipRecord: Boolean; var IsLastOperation: Boolean; ProdOrderRoutingLine: Record "Prod. Order Routing Line")
    begin
        if (not IsLastOperation) and (ProdOrderRoutingLine."Work Center No." <> GlobalPackageNoInformation."Work Center No. FLX") then
            SkipRecord := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeOutputItemJnlLineInsert, '', false, false)]
    local procedure OnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    begin
        ItemJournalLine.Validate("Starting DateTime FLX", CurrentDateTime());

        if GlobalPackageNoInformation."Label Lenght FLX" <> 0 then begin
            ItemJournalLine.Validate("Output Quantity", GlobalPackageNoInformation."Label Lenght FLX");
            ItemJournalLine.Validate("Production Line No. FLX", GlobalProductionOperation."Production Line No.");

            if LastOperation then begin
                ItemJournalLine.Validate("Lot No.", GlobalPackageNoInformation."Lot No. FLX");
                ItemJournalLine.Validate("Package No.", GlobalPackageNoInformation."Package No.");
            end;
        end
        else
            if GlobalBanburyProduction.Quantity <> 0 then begin
                ItemJournalLine.Validate("Output Quantity", GlobalBanburyProduction.Quantity);
                ItemJournalLine.Validate("Ending DateTime FLX", CurrentDateTime());
            end;
    end;

    procedure CalculateRunTime(var ItemJournalLine: Record "Item Journal Line"): Integer
    begin
        if (ItemJournalLine."Starting DateTime FLX" = 0DT) or (ItemJournalLine."Ending DateTime FLX" = 0DT) then
            exit;
        ItemJournalLine.Validate("Run Time", (ItemJournalLine."Ending DateTime FLX" - ItemJournalLine."Starting DateTime FLX") / 3600000);
    end;

    procedure FinishProductionProcess(ProductionOperation: Record "Production Operation FLX")
    var
        ItemJournalLine: Record "Item Journal Line";
        NoStartedProductionErr: Label 'There is no started production for Package No.: %1', Comment = '%1="Production Operation FLX"."Package No."';
    begin
        FlexatiSetup.GetRecordOnce();

        ItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Output Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ProductionOperation."User ID");
#pragma warning disable AA0210
        ItemJournalLine.SetRange("Package No.", ProductionOperation."Package No.");
#pragma warning restore AA0210
        if not ItemJournalLine.FindSet(true) then
            Error(NoStartedProductionErr, ProductionOperation."Package No.");
        repeat
            ItemJournalLine.Validate("Ending DateTime FLX", CurrentDateTime());
            ItemJournalLine.Modify(true);
        until ItemJournalLine.Next() = 0;

        //Post Consumption Journals
        Clear(ItemJournalLine);
        ItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Consumption Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ProductionOperation."User ID");
        ItemJournalLine.FindFirst();
        GlobalProductionOperation := ProductionOperation;
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        Clear(ItemJournalLine);

        //Post Output Journals
        ItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Output Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ProductionOperation."User ID");
        ItemJournalLine.FindFirst();
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        Clear(GlobalProductionOperation);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post", OnBeforeCode, '', false, false)]
    local procedure OnBeforeCode(var ItemJournalLine: Record "Item Journal Line"; var HideDialog: Boolean; var SuppressCommit: Boolean; var IsHandled: Boolean)
    begin
        HideDialog := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnBeforeInsertCapLedgEntry, '', false, false)]
    local procedure OnBeforeInsertCapLedgEntry(var CapLedgEntry: Record "Capacity Ledger Entry"; ItemJournalLine: Record "Item Journal Line")
    begin
        CapLedgEntry."Ending DateTime FLX" := ItemJournalLine."Ending DateTime FLX";
        CapLedgEntry."Starting DateTime FLX" := ItemJournalLine."Starting DateTime FLX";
        CapLedgEntry."Production Line No. FLX" := ItemJournalLine."Production Line No. FLX";

        if GlobalProductionOperation."Package No." <> '' then begin
            CapLedgEntry."Package No. FLX" := GlobalProductionOperation."Package No.";
            CapLedgEntry."Item Journal Batch Name FLX" := GlobalProductionOperation."User ID";
        end;

        if GlobalBanburyProduction.Quantity <> 0 then
            CapLedgEntry."Item Journal Batch Name FLX" := GlobalBanburyProduction."User ID";
    end;

    procedure GetBinCodeFromPackageNo(PackageNo: Code[50]): Code[20]
    var
        WarehouseEntry: Record "Warehouse Entry";
    begin
        WarehouseEntry.SetRange("Package No.", PackageNo);
        WarehouseEntry.CalcSums(Quantity);
        if WarehouseEntry.Quantity > 0 then begin
            WarehouseEntry.FindLast();
            exit(WarehouseEntry."Bin Code");
        end;

        exit('');
    end;

    procedure UpdateStandardCostForReplenishmentSystemProduction()
    var
        Item: Record Item;
        CalculateStandardCost: Codeunit "Calculate Standard Cost";
        NoItemToUpdateErr: Label 'There is no item to update standard cost.';
        UpdateSuccessfulMsg: Label 'Standard Cost has been updated for relevant items.';

    begin
        Item.SetRange("Replenishment System", Item."Replenishment System"::"Prod. Order");
        Item.SetFilter("Production BOM No.", '<>%1', '');
        Item.SetRange("Production BOM Status FLX", Item."Production BOM Status FLX"::Certified);
        Item.SetRange("Routing Status FLX", Item."Routing Status FLX"::Certified);
        if not Item.FindSet(true) then
            Error(NoItemToUpdateErr);

        repeat
            // Item."Hide Dlg for Calc Std Cost FLX" := true;
            // Item.Modify(false);

            Clear(CalculateStandardCost);
            CalculateStandardCost.CalcItem(Item."No.", false);
        //CalculateStdCost.CalcItem(Item."No.", false);

        // Item."Hide Dlg for Calc Std Cost FLX" := false;
        // Item.Modify(false);
        until Item.Next() = 0;

        Message(UpdateSuccessfulMsg);
    end;

    procedure GetBomItemNoList_ByItemCategoryCode(var ProdOrderLine: Record "Prod. Order Line"; ItemCategoryCode: Code[20]): Text[250]
    var
        Item: Record Item;
        //ProductionBOMLine: Record "Production BOM Line";
        //VersionManagement: Codeunit VersionManagement;
        ProdOrderComp: Record "Prod. Order Component";
        ItemNoList: Text[250];
    begin
        //if not Item.Get(ItemNo) then
        //    exit;

        //if Item."Production BOM No." = '' then
        //    exit;

        /*ProductionBOMLine.SetRange("Production BOM No.", Item."Production BOM No.");
        ProductionBOMLine.SetRange("Version Code", VersionManagement.GetBOMVersion(Item."Production BOM No.", WorkDate(), true));
        ProductionBOMLine.SetRange(Type, ProductionBOMLine.Type::Item);
        //ProductionBOMLine.SetFilter("No.", '<>H*');
        if ProductionBOMLine.FindSet() then
            repeat
                Item.Get(ProductionBOMLine."No.");
                if CopyStr(Item."Item Category Code", 1, 2) = ItemCategoryCode then begin
                    if ItemNoList <> '' then
                        ItemNoList += ',';
                    ItemNoList += ProductionBOMLine."No.";
                end;
            until ProductionBOMLine.Next() = 0;
            */
        ProdOrderComp.SetRange(Status, ProdOrderLine.Status);
        ProdOrderComp.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderComp.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");
        //ProdOrderComp.SetFilter("No.", '<>H*');
        if ProdOrderComp.FindSet() then
            repeat
                Item.Get(ProdOrderComp."Item No.");
                if CopyStr(Item."Item Category Code", 1, 2) = ItemCategoryCode then begin
                    if ItemNoList <> '' then
                        ItemNoList += ',';
                    ItemNoList += ProdOrderComp."Item No.";
                end;
            until ProdOrderComp.Next() = 0;

        exit(ItemNoList);
    end;

    procedure GetBomItemNoDesc_ByItemCategoryCode(var ProdOrderLine: Record "Prod. Order Line"; ItemCategoryCode: Code[20]): Text[250]
    var
        Item: Record Item;
        //ProductionBOMLine: Record "Production BOM Line";
        //VersionManagement: Codeunit VersionManagement;
        ProdOrderComp: Record "Prod. Order Component";
        ItemNoList: Text[250];
    begin
        //if not Item.Get(ItemNo) then
        //    exit;

        //if Item."Production BOM No." = '' then
        //    exit;

        /*ProductionBOMLine.SetRange("Production BOM No.", Item."Production BOM No.");
        ProductionBOMLine.SetRange("Version Code", VersionManagement.GetBOMVersion(Item."Production BOM No.", WorkDate(), true));
        ProductionBOMLine.SetRange(Type, ProductionBOMLine.Type::Item);
        //ProductionBOMLine.SetFilter("No.", '<>H*');
        if ProductionBOMLine.FindSet() then
            repeat
                Item.Get(ProductionBOMLine."No.");
                if CopyStr(Item."Item Category Code", 1, 2) = ItemCategoryCode then begin
                    if ItemNoList <> '' then
                        ItemNoList += ',';
                    ItemNoList += Item.Description;
                end;
            until ProductionBOMLine.Next() = 0;
        */
        ProdOrderComp.SetRange(Status, ProdOrderLine.Status);
        ProdOrderComp.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderComp.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");
        //ProdOrderComp.SetFilter("No.", '<>H*');
        if ProdOrderComp.FindSet() then
            repeat
                Item.Get(ProdOrderComp."Item No.");
                if CopyStr(Item."Item Category Code", 1, 2) = ItemCategoryCode then begin
                    if ItemNoList <> '' then
                        ItemNoList += ',';
                    ItemNoList += Item.Description;
                end;
            until ProdOrderComp.Next() = 0;

        exit(ItemNoList);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Calculate Standard Cost", OnCalcItemOnBeforeShowStrMenu, '', false, false)]
    local procedure "Calculate Standard Cost_OnCalcItemOnBeforeShowStrMenu"(var Item: Record Item; var ShowStrMenu: Boolean; var NewCalcMultiLevel: Boolean)
    begin
        //if Item."Hide Dlg for Calc Std Cost FLX" then begin
        ShowStrMenu := false;
        NewCalcMultiLevel := true;
        //end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnAfterGetSalesHeader, '', false, false)]
    local procedure "Sales Line_OnAfterGetSalesHeader"(var SalesLine: Record "Sales Line"; var SalesHeader: Record "Sales Header"; var Currency: Record Currency)
    begin
        if SalesLine."Your Reference FLX" = '' then
            SalesLine."Your Reference FLX" := SalesHeader."Your Reference";
    end;

    [EventSubscriber(ObjectType::Page, Page::"Sales Order Planning", OnBeforeCreateProdOrder, '', false, false)]
    local procedure "Sales Order Planning_OnBeforeCreateProdOrder"(var SalesPlanningLine: Record "Sales Planning Line"; var NewStatus: Enum "Production Order Status"; var NewOrderType: Option; var ShowCreateOrderForm: Boolean; var IsHandled: Boolean)
    begin
        ShowCreateOrderForm := false;
        NewOrderType := 1; //Project Order
        NewStatus := NewStatus::"Firm Planned";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnBeforeInsertItemLedgEntry, '', false, false)]
    local procedure "Item Jnl.-Post Line_OnBeforeInsertItemLedgEntry"(var ItemLedgerEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line"; TransferItem: Boolean; OldItemLedgEntry: Record "Item Ledger Entry"; ItemJournalLineOrigin: Record "Item Journal Line")
    begin
        ItemLedgerEntry."Source Package No. FLX" := GlobalProductionOperation."Package No.";
    end;

    [EventSubscriber(ObjectType::Table, Database::"Item Journal Batch", OnBeforeValidateItemTrackingOnLines, '', false, false)]
    local procedure "Item Journal Batch_OnBeforeValidateItemTrackingOnLines"(var ItemJournalBatch: Record "Item Journal Batch"; var IsHandled: Boolean)
    begin
        IsHandled := true;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnBeforeCheckItemTrackingIsEmpty, '', false, false)]
    local procedure "Item Jnl.-Post Line_OnBeforeCheckItemTrackingIsEmpty"(ItemJournalLine: Record "Item Journal Line"; var IsHandled: Boolean)
    begin
        IsHandled := true;
    end;



    #endregion Coil Production

    #region Banbury Production

    procedure PostProduction(var BanburyProduction: Record "Banbury Production FLX")
    begin
        BanburyProduction.TestField("Production Order No.");
        BanburyProduction.TestField("User ID");
        BanburyProduction.TestField(Quantity);

        CreateConsumpitonJournalForBanburyProduction(BanburyProduction);

        CreateOutputJournalForBanburyProduction(BanburyProduction);

        PostItemJournalLinesForBanburyProduction(BanburyProduction);
    end;

    procedure CreateConsumpitonJournalForBanburyProduction(var BanburyProduction: Record "Banbury Production FLX")
    var
        ProductionOrder: Record "Production Order";
        ProdOrderLine: Record "Prod. Order Line";
        CalcConsumption: Report "Calc. Consumption";

    begin
        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Consumption Jnl. Template Name");

        ProductionOrder.Get(ProductionOrder.Status::Released, BanburyProduction."Production Order No.");
        ProdOrderLine.SetRange(Status, ProdOrderLine.Status::Released);
        ProdOrderLine.SetRange("Prod. Order No.", ProductionOrder."No.");
        ProdOrderLine.FindFirst();
        ProdOrderLine.TestField("Remaining Quantity");

        ProductionOrder.SetRecFilter();


        CalcConsumption.SetTemplateAndBatchName(FlexatiSetup."Consumption Jnl. Template Name", BanburyProduction."User ID");
        CalcConsumption.InitializeRequest(Today(), 1);
        CalcConsumption.SetTableView(ProductionOrder);
        GlobalBanburyProduction := BanburyProduction;
        CalcConsumption.Execute('');

    end;

    [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnBeforeGetNeededQty, '', false, false)]
    local procedure "Calc. Consumption_OnBeforeGetNeededQty"(var NeededQty: Decimal; CalcBasedOn: Option; ProdOrderComponent: Record "Prod. Order Component"; ProductionOrder: Record "Production Order"; PostingDate: Date; var IsHandled: Boolean)
    begin
        NeededQty := GlobalBanburyProduction.Quantity * ProdOrderComponent."Quantity per";
        NeededQty += NeededQty * ProdOrderComponent."Scrap %" / 100;
        IsHandled := true;
    end;

    procedure CreateOutputJournalForBanburyProduction(var BanburyProduction: Record "Banbury Production FLX")
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        OutputJnlExplRoute: Codeunit "Output Jnl.-Expl. Route";
    begin
        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Output Jnl. Template Name");

        LastItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Output Jnl. Template Name");
        LastItemJournalLine.SetRange("Journal Batch Name", BanburyProduction."User ID");
        if LastItemJournalLine.FindLast() then; //

        ItemJournalLine.Init();
        ItemJournalLine."Journal Template Name" := FlexatiSetup."Output Jnl. Template Name";
        ItemJournalLine."Journal Batch Name" := BanburyProduction."User ID";
        ItemJournalLine.SetUpNewLine(LastItemJournalLine);
        ItemJournalLine.Validate("Order Type", ItemJournalLine."Order Type"::Production);
        ItemJournalLine.Validate("Order No.", BanburyProduction."Production Order No.");
        ItemJournalLine.Insert(true);

        OutputJnlExplRoute.Run(ItemJournalLine);
        //GlobalBanburyQty := 0;

    end;

    local procedure PostItemJournalLinesForBanburyProduction(var BanburyProduction: Record "Banbury Production FLX")
    var
        ItemJournalLine: Record "Item Journal Line";
    begin
        FlexatiSetup.GetRecordOnce();

        //Post Consumption Journals
        ItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Consumption Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", BanburyProduction."User ID");
        ItemJournalLine.FindFirst();
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        Clear(ItemJournalLine);

        //Post Output Journals
        ItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Output Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", BanburyProduction."User ID");
        ItemJournalLine.FindFirst();
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        Clear(GlobalBanburyProduction);
    end;
    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnAfterInsertItemJnlLine, '', false, false)]
    // local procedure "Output Jnl.-Expl. Route_OnAfterInsertItemJnlLine"(var ItemJournalLine: Record "Item Journal Line")
    // begin
    //     ItemJournalLine.Validate("Output Quantity", GlobalBanburyQty);
    // end;

    [EventSubscriber(ObjectType::Page, Page::"Released Production Order", OnBeforeActionEvent, RefreshProductionOrder, false, false)]
    local procedure OnBeforeActionEvent_RefreshProductionOrder(var Rec: Record "Production Order")
    // var
    //     Item: Record Item;
    begin

        // if Rec."Location Code" = '' then begin
        //     Item.Get(Rec."Source No.");

        //     Rec.Validate("Location Code", Item."Production Output Location FLX");
        //     Rec.Validate("Bin Code", Item."Production Output Bin Code FLX");
        //     Rec.Modify(false);
        // end;

        Rec.TestField("Location Code");
        Rec.TestField("Bin Code");
    end;
    #endregion Banbury Production

    procedure ConsumptionProductionProcess(var ProductionOperation: Record "Production Operation FLX")
    var
        ItemJournalLine: Record "Item Journal Line";
        PackageNoInformation: Record "Package No. Information";
        ConsumptionPackageNoInformation: Record "Package No. Information";
        ProdOrderLine: Record "Prod. Order Line";
        // Item: Record Item;
        // JournalBatchName: Code[10];
        // AlreadyStartedQst: Label 'This packages production has been started by User: %1. Do you want to delete those lines and start again?', Comment = '%1="Item Journal Line"."Journal Batch Name"';
        ClearOldLinesQst: Label 'Do you want to clear the lines for Package No.: %1 that have already been started in production and continue processing?', Comment = '%1="Package No."';
        ClearOldLinesTxt: Text;
        ProcessAbortedErr: Label 'Process aborted by user.';
        PackageNotFoundErr: Label 'Package No. %1 could not be found.', Comment = '%1="Package No."';
        ConsumptionPackageNotFoundErr: Label 'Consumption Package No. %1 not found.', Comment = '%1="Consumption Package No."';
        ProdOrderLineNotFoundErr: Label 'Production Order Line not found for Order No. %1, Line No. %2.', Comment = '%1="Production Order No.", %2="Production Order Line No."';
    begin
        FlexatiSetup.GetRecordOnce();
        FlexatiSetup.TestField("Consumption Jnl. Template Name");
        FlexatiSetup.TestField("Output Jnl. Template Name");

        // Check for existing journal lines and clear if needed
        ItemJournalLine.SetFilter("Journal Template Name", '%1|%2', FlexatiSetup."Consumption Jnl. Template Name", FlexatiSetup."Output Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ProductionOperation."User ID");
        ItemJournalLine.SetFilter("Package No.", '<>%1', '');
        if ItemJournalLine.FindSet(true) then begin
            ClearOldLinesTxt := StrSubstNo(ClearOldLinesQst, ItemJournalLine."Package No.");
            if not ConfirmManagement.GetResponseOrDefault(ClearOldLinesTxt, false) then
                Error(ProcessAbortedErr)
            else begin
                ItemJournalLine.SetRange("Package No.");
                ItemJournalLine.DeleteAll(true);
            end;
        end;

        // Get Package No. Information for the main package
        PackageNoInformation.SetRange("Package No.", ProductionOperation."Package No.");
        if not PackageNoInformation.FindFirst() then
            Error(PackageNotFoundErr, ProductionOperation."Package No.");
        PackageNoInformation.TestField("Production Order No. FLX");
        PackageNoInformation.TestField("Production Order Line No. FLX");

        // Get Consumption Package No. Information for lot assignment
        ConsumptionPackageNoInformation.SetRange("Package No.", ProductionOperation."Consumption Package No.");
        if not ConsumptionPackageNoInformation.FindFirst() then
            Error(ConsumptionPackageNotFoundErr, ProductionOperation."Consumption Package No.");

        // Get Production Order Line
        ProdOrderLine.SetRange(Status, ProdOrderLine.Status::Released);
        ProdOrderLine.SetRange("Prod. Order No.", PackageNoInformation."Production Order No. FLX");
        ProdOrderLine.SetRange("Line No.", PackageNoInformation."Production Order Line No. FLX");
        if not ProdOrderLine.FindFirst() then
            Error(ProdOrderLineNotFoundErr, PackageNoInformation."Production Order No. FLX", PackageNoInformation."Production Order Line No. FLX");

        // Create Consumption Journal Lines with lot assignment for hose items
        CreateConsumptionJournalLinesWithLotAssignment(ProductionOperation, PackageNoInformation, ConsumptionPackageNoInformation, ProdOrderLine);

        // Create Output Journal Lines (reuse existing logic)
        CreateOutputJournalLines(ProductionOperation, PackageNoInformation);
    end;

    local procedure CreateConsumptionJournalLinesWithLotAssignment(var ProductionOperation: Record "Production Operation FLX"; var PackageNoInformation: Record "Package No. Information"; var ConsumptionPackageNoInformation: Record "Package No. Information"; var ProdOrderLine: Record "Prod. Order Line")
    var
        Item: Record Item;
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ProdOrderComponent: Record "Prod. Order Component";
        UnitofMeasureManagement: Codeunit "Unit of Measure Management";
        QtyToConsume: Decimal;
        ItemNotFoundErr: Label 'Item %1 not found.', Comment = '%1="Item No."';
    begin
        FlexatiSetup.GetRecordOnce();

        ProdOrderComponent.SetRange(Status, ProdOrderComponent.Status::Released);
        ProdOrderComponent.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderComponent.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");
        ProdOrderComponent.FindSet();
        repeat
            Item.SetRange("No.", ProdOrderComponent."Item No.");
            if not Item.FindFirst() then
                Error(ItemNotFoundErr, ProdOrderComponent."Item No.");
            QtyToConsume := ProdOrderComponent."Quantity per" * PackageNoInformation."Label Lenght FLX" * (1 + (ProdOrderComponent."Scrap %" / 100));

            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := FlexatiSetup."Consumption Jnl. Template Name";
            ItemJournalLine."Journal Batch Name" := ProductionOperation."User ID";
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine."Line No." := LastItemJournalLine."Line No." + 10000;

            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Consumption);
            ItemJournalLine.Validate("Order Type", ItemJournalLine."Order Type"::Production);
            ItemJournalLine.Validate("Order No.", ProdOrderComponent."Prod. Order No.");
            ItemJournalLine.Validate("Source No.", ProdOrderLine."Item No.");
            ItemJournalLine.Validate("Posting Date", WorkDate());
            ItemJournalLine.Validate("Item No.", ProdOrderComponent."Item No.");
            ItemJournalLine.Validate("Unit of Measure Code", ProdOrderComponent."Unit of Measure Code");
            ItemJournalLine.Description := ProdOrderComponent.Description;
            ItemJournalLine.Validate(Quantity, UnitofMeasureManagement.RoundToItemRndPrecision(QtyToConsume, Item."Rounding Precision"));
            ItemJournalLine."Variant Code" := ProdOrderComponent."Variant Code";
            ItemJournalLine.Validate("Location Code", ProdOrderComponent."Location Code");
            ItemJournalLine.Validate("Order Line No.", ProdOrderComponent."Prod. Order Line No.");
            ItemJournalLine.Validate("Prod. Order Comp. Line No.", ProdOrderComponent."Line No.");

            // Check if this is a hose item (has Production BOM No.) and assign lot/package info
            if Item."Production BOM No." <> '' then begin
                ItemJournalLine.Validate("Lot No.", ConsumptionPackageNoInformation."Lot No. FLX");
                ItemJournalLine.Validate("Package No.", ProductionOperation."Consumption Package No.");
            end;

            ItemJournalLine.Insert(false);

            LastItemJournalLine := ItemJournalLine;
        until ProdOrderComponent.Next() = 0;
    end;

    var
        FlexatiSetup: Record "Flexati Setup FLX";
        GlobalPackageNoInformation: Record "Package No. Information";
        GlobalProductionOperation: Record "Production Operation FLX";
        GlobalBanburyProduction: Record "Banbury Production FLX";
        ConfirmManagement: Codeunit "Confirm Management";
        //GlobalBanburyQty: Decimal;
        QuantityErr: Label 'Something is wrong with entered quantity.';
}
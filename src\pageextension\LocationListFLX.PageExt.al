pageextension 60018 "Location List FLX" extends "Location List"
{
    layout
    {
        addlast(Control1)
        {
            field("Include In Calculation FLX"; Rec."Include In Calculation FLX")
            {
                ApplicationArea = All;
            }
            field("Q. C. Accept Required FLX"; Rec."Q. C. Accept Required FLX")
            {
                ApplicationArea = All;
            }
            field("Quality Control Location FLX"; Rec."Quality Control Location FLX")
            {
                ApplicationArea = All;
            }
        }
    }
}
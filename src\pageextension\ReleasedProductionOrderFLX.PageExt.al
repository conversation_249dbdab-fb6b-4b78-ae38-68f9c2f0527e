pageextension 60024 "Released Production Order FLX" extends "Released Production Order"
{
    layout
    {
        addafter("Last Date Modified")
        {
            field("Note FLX"; Rec."Note FLX")
            {
                ApplicationArea = All;
            }
            field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        modify(RefreshProductionOrder)
        {
            Visible = false;
        }
        addlast("&Print")
        {
            action("PrintBanburyProductionOrder FLX")
            {
                ApplicationArea = All;
                Caption = 'Print Banbury Production Order';
                Image = Print;
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Print action.';
                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Banbury Production Order FLX", true, true, Rec);
                end;
            }
        }
    }
}
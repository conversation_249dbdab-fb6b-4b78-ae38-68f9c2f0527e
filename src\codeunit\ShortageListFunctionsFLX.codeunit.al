codeunit 60005 "Shortage List Functions FLX"
{
    procedure ProcessFilters(StartDate: Date; EndDate: Date; SourceItemNo: Text; ItemNo: Text; ExcludeSupplyQuantities: Boolean; IncludePlannedProdOrders: <PERSON>olean; IncludeFirmPlannedProdOrders: <PERSON>olean; IncludeReleasedProdOrders: <PERSON>olean; ShowOnlyNegativeQty: Boolean; LoopSubComp: Boolean)
    var
        Item: Record Item;
        ProdOrderComponent: Record "Prod. Order Component";
        //ProductionBOMLine: Record "Production BOM Line";
        //ProductionOrder: Record "Production Order";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
        ShortageList: Record "Shortage List Detail FLX";
        LoopOnce: Boolean;
        TempItemNo: Code[20];
        Window: Dialog;
        Counter: Integer;
        FilterLenght: Integer;
        TotalCount: Integer;
        EndDateCanNotBeZeroErr: Label 'End Date Can Not Be Empty';
        ProdOrderStatusFilter: Text[250];
    begin
        if EndDate = 0D then //BRST.006.05 - Added Line
            Error(EndDateCanNotBeZeroErr); //BRST.006.05 - Added Line

        GetRequiredFilters();
        ShortageList.Reset();                     //BRST.006.07 - Added Line
        ShortageList.SetFilter("User Id", '%1|%2', CopyStr(UserId(), 1, 50), '');//BRST.006.07 - Added Line
        if ShortageList.FindFirst() then          //BRST.006.07 - Added Line
            ShortageList.DeleteAll(true);

        ShortageListCumulativeQty.Reset();                      //BRST.006.07 - Added Line
        ShortageListCumulativeQty.SetFilter("User Id", '%1|%2', CopyStr(UserId(), 1, 50), ''); //BRST.006.07 - Added Line
        if ShortageListCumulativeQty.FindFirst() then           //BRST.006.07 - Added Line
            ShortageListCumulativeQty.DeleteAll(true);

        ShortageItemList.Reset();                           //BRST.006.07 - Added Line
        ShortageItemList.SetFilter("User Id", '%1|%2', CopyStr(UserId(), 1, 50), '');      //BRST.006.07 - Added Line
        if ShortageItemList.FindFirst() then               //BRST.006.07 - Added Line
            ShortageItemList.DeleteAll(true);

        //BRST.006.09 - Begin
        ProdOrderStatusFilter := '';

        /* compiler hata veriyor temp tablo trigger olmaz diyor
        TempItemConsump.DeleteAll(true);
        TempItemOnHand.DeleteAll(true);
        TempItemOutput.DeleteAll(true);
        TempItemPurchase.DeleteAll(true);
        TempItemReal.DeleteAll(true);
        TempItemSales.DeleteAll(true);
        */
        TempItemConsump.DeleteAll(false);
        TempItemOnHand.DeleteAll(false);
        TempItemOutput.DeleteAll(false);
        TempItemPurchase.DeleteAll(false);
        TempItemReal.DeleteAll(false);
        TempItemSales.DeleteAll(false);

        /*
        Simulated = 0
        Planned = 1
        Firm Planned = 2
        Released = 3
        Finished = 4
        */

        if IncludePlannedProdOrders then
            ProdOrderStatusFilter += '|1';

        if IncludeFirmPlannedProdOrders then
            ProdOrderStatusFilter += '|2';

        if IncludeReleasedProdOrders then
            ProdOrderStatusFilter += '|3';

        FilterLenght := StrLen(ProdOrderStatusFilter);
        ProdOrderStatusFilter := CopyStr(CopyStr(ProdOrderStatusFilter, 2, FilterLenght), 1, 250);
        //BRST.006.09 - End

        Window.Open('Processing: @1@@@@@@@@@@@@@@@');

        if SourceItemNo <> '' then begin
            ProdOrderComponent.Reset();
            ProdOrderComponent.SetFilter("Source Item No. FLX", SourceItemNo); //BRST.006.07 - Modified Line
            ProdOrderComponent.SetRange("Prod. Order Start Date FLX", StartDate, EndDate);
            ProdOrderComponent.SetFilter(Status, '<>%1', ProdOrderComponent.Status::Finished); //BRST.006.06 - Added Line
            ProdOrderComponent.SetCurrentKey("Item No.");
            if ProdOrderComponent.FindSet() then begin
                TotalCount := ProdOrderComponent.Count();
                TempItemNo := '';
                Counter := 0;
                repeat
                    if not (TempItemNo = ProdOrderComponent."Item No.") then begin
                        Counter += 1;
                        Window.Update(1, Round(Counter / TotalCount * 10000, 1));
                        TempItemNo := ProdOrderComponent."Item No.";
                        InsertOnHandSubcontractorRejectedQuantityLine(TempItemNo, StartDate);
                        InsertConsuptionLines(TempItemNo, StartDate, EndDate, ProdOrderStatusFilter); //BRST.006.09 - Modified Line
                        InsertSalesLines(TempItemNo, StartDate, EndDate);
                        if not ExcludeSupplyQuantities then begin
                            InsertPurchaseLines(TempItemNo, StartDate, EndDate);
                            InsertOutputLines(TempItemNo, StartDate, EndDate);
                        end;
                        Update_ShortItemList(TempItemNo); //BRST.006.11 - Added Line
                        CalcSubComp(TempItemNo, StartDate, EndDate, ProdOrderStatusFilter, ExcludeSupplyQuantities); //BRST.006.16
                    end;
                until ProdOrderComponent.Next() = 0;
            end;
        end else begin
            if ItemNo <> '' then begin
                Item.SetFilter("No.", ItemNo); //BRST.006.07 - Modified Line
                LoopOnce := true;
            end;

            if Item.FindSet() then begin
                TotalCount := Item.Count();
                repeat
                    if not (TempItemNo = Item."No.") or (LoopOnce) then begin
                        Counter := Counter + 1;
                        Window.Update(1, Round(Counter / TotalCount * 10000, 1));
                        TempItemNo := Item."No.";
                        LoopOnce := false;
                        InsertOnHandSubcontractorRejectedQuantityLine(TempItemNo, StartDate);
                        InsertConsuptionLines(TempItemNo, StartDate, EndDate, ProdOrderStatusFilter); //BRST.006.09 - Modified Line
                        InsertSalesLines(TempItemNo, StartDate, EndDate);
                        if not ExcludeSupplyQuantities then begin
                            InsertPurchaseLines(TempItemNo, StartDate, EndDate);
                            InsertOutputLines(TempItemNo, StartDate, EndDate);
                        end;
                        InsertRealConsump(TempItemNo, StartDate, EndDate);
                        if LoopSubComp then
                            CalcSubComp(TempItemNo, StartDate, EndDate, ProdOrderStatusFilter, ExcludeSupplyQuantities); //BRST.006.16
                        Update_ShortItemList(TempItemNo); //BRST.006.11 - Added Line
                    end;
                until Item.Next() = 0;
            end;
        end;

        ShortageListLoop();
        Message(ProcessCompletedMsg);
    end;

    local procedure GetRequiredFilters()
    var
        ItemShortageListSetup: Record "Item Shortage List Setup FLX";
    //Location: Record Location;
    begin
        ItemShortageListSetup.Get();
        ItemShortageListSetup.TestField("Reject Location");
        ItemShortageListSetup.TestField("UHD Location");
        ItemShortageListSetup.TestField("WHSE Location"); //FLEX-240
        RejectLocationCode := ItemShortageListSetup."Reject Location";
        UHDLocationCode := ItemShortageListSetup."UHD Location";
        WHSELocationCode := ItemShortageListSetup."WHSE Location"; //FLEX-240
    end;

    local procedure InsertOnHandSubcontractorRejectedQuantityLine(ItemNo: Code[20]; StartDate: Date)
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageList: Record "Shortage List Detail FLX";
        StockKeepUnit: Record "Stockkeeping Unit";
        Vendor: Record Vendor;
        VendorNo: Code[20];
        EntryNo: Integer;
    begin
        if ItemNo = '' then
            exit;
        //ShortageList.Reset();
        //ShortageList.SetRange("Register Type", ShortageList."Register Type"::"On Hand");
        //ShortageList.SetRange("Item No.", ItemNo);
        //ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        //if ShortageList.FindFirst() then
        //    exit;
        if TempItemOnHand.Get(ItemNo) then
            exit;
        TempItemOnHand."No." := ItemNo;
        //TempItemOnHand.Insert(true); //compiler hata veriyor temp tabloda trigger olmaz diyor
        TempItemOnHand.Insert(false);

        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetCurrentKey("Item No.");
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetFilter("Remaining Quantity", '>0');
        ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
        ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
        ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
        ItemLedgerEntry.CalcSums("Remaining Quantity");

        Item.Get(ItemNo);

        if not ShortageItemList.Get(ItemNo, UserId()) then begin//BRST.006.07 - Modified Line
            VendorNo := '';
            StockKeepUnit.Reset();
            StockKeepUnit.SetRange("Item No.", ItemNo);
            StockKeepUnit.SetFilter("Vendor No.", '<>%1', '');
            if StockKeepUnit.FindFirst() then
                VendorNo := StockKeepUnit."Vendor No."
            else
                VendorNo := Item."Vendor No.";
            ShortageItemList."Item No." := ItemNo;
            ShortageItemList."Vendor No." := VendorNo;
            if Vendor.Get(VendorNo) then
                ShortageItemList."Vendor Name" := Vendor.Name;
            ShortageItemList."Item Description" := Item.Description;
            ShortageItemList."Inventory Posting Group" := Item."Inventory Posting Group";
            ShortageItemList."Item Description 2" := Item."Description 2";
            ShortageItemList."Item Category Code" := Item."Item Category Code";
            ShortageItemList."Minimum Order Qty." := Item."Minimum Order Quantity";
            ShortageItemList."Safety Stock Quantity" := Item."Safety Stock Quantity";
            ShortageItemList.Inventory := ItemLedgerEntry."Remaining Quantity";
            ShortageItemList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
            ShortageItemList.Insert(true);
            CalculateLeadTime(ShortageItemList); //BRST.006.12 - Added Line
        end;

        ShortageList.Reset();
        if not ShortageList.FindLast() then
            EntryNo := 0
        else
            EntryNo := ShortageList."Register No.";

        ShortageList.Init();
        ShortageList."Register No." := EntryNo + 1;
        ShortageList."Register Type" := ShortageList."Register Type"::"On Hand";
        ShortageList."Item No." := Item."No.";
        ShortageList."Item Description" := Item.Description;
        ShortageList.Quantity := ItemLedgerEntry."Remaining Quantity"; //On Hand Qty.
        ShortageList."Source No." := Item."Vendor No.";
        if Vendor.Get(Item."Vendor No.") then
            ShortageList."Source Name/Description" := Vendor.Name;
        ShortageList.Date := StartDate;
        ShortageList."Document No." := 'Already On Hand';
        ShortageList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
        ShortageList.Insert(true);
        EntryNo += 1;

        ItemLedgerEntry.SetRange("Location Code", RejectLocationCode);
        ItemLedgerEntry.CalcSums("Remaining Quantity");
        ShortageList."Quantity On Reject Location" := ItemLedgerEntry."Remaining Quantity";
        ShortageList.Modify(true);

        ItemLedgerEntry.SetRange("Location Code", UHDLocationCode);
        ItemLedgerEntry.CalcSums("Remaining Quantity");
        ShortageList."Quantity On UHD Location" := ItemLedgerEntry."Remaining Quantity";
        ShortageList.Modify(true);

        //FLEX-240 BEGIN
        ItemLedgerEntry.SetRange("Location Code", WHSELocationCode);
        ItemLedgerEntry.CalcSums("Remaining Quantity");
        ShortageList."Quantity On WHSE Location" := ItemLedgerEntry."Remaining Quantity";
        ShortageList.Modify(true);
        //FLEX-240 END

        ItemLedgerEntry.SetRange("Location Code");
        ItemLedgerEntry.SetRange("Subcontractor Location FLX", true);
        ItemLedgerEntry.CalcSums("Remaining Quantity");
        ShortageList."Quantity On Subcontractors" := ItemLedgerEntry."Remaining Quantity"; //Subcontractor Location Qty.
        ShortageList.Quantity += ShortageList."Quantity On Subcontractors"; //BRST.006.01 - Added Line
        ShortageList.Modify(true);
    end;

    local procedure InsertPurchaseLines(ItemNo: Code[20]; StartDate: Date; EndDate: Date)
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageList: Record "Shortage List Detail FLX";
        StockKeepUnit: Record "Stockkeeping Unit";
        Vendor: Record Vendor;
        VendorNo: Code[20];
        EntryNo: Integer;
    begin
        if ItemNo = '' then
            exit;
        //if ItemNo = '000-02' then
        //    Message('000-02 pl insert');

        //ShortageList.Reset();
        //ShortageList.SetRange("Register Type", ShortageList."Register Type"::Purchase);
        //ShortageList.SetRange("Item No.", ItemNo);
        //ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        //if ShortageList.FindFirst() then
        //    exit;
        if TempItemPurchase.Get(ItemNo) then
            exit;
        TempItemPurchase."No." := ItemNo;
        //TempItemPurchase.Insert(true);//compiler hata veriyor temp tabloda trigger olmaz diyor
        TempItemPurchase.Insert(false);

        PurchaseLine.Reset();
        PurchaseLine.SetRange("No.", ItemNo);
        PurchaseLine.SetRange("Expected Receipt Date", StartDate, EndDate);
        PurchaseLine.SetFilter("Outstanding Quantity", '>0');
        PurchaseLine.SetRange("Document Type", PurchaseLine."Document Type"::Order);// BRST.006.01 - Added Line
        PurchaseLine.SetRange("Prod. Order No.", ''); //BRST.006.02 - Added Line

        Item.Get(ItemNo);

        if not ShortageItemList.Get(ItemNo, UserId()) then begin//BRST.006.07 - Modified Line
            //BRST.006.11 BEGIN
            ItemLedgerEntry.Reset();
            ItemLedgerEntry.SetCurrentKey("Item No.");
            ItemLedgerEntry.SetRange("Item No.", ItemNo);
            ItemLedgerEntry.SetFilter("Remaining Quantity", '>0');
            ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
            ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
            ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
            ItemLedgerEntry.CalcSums("Remaining Quantity");
            //BRST.006.11 END
            VendorNo := '';
            StockKeepUnit.Reset();
            StockKeepUnit.SetRange("Item No.", ItemNo);
            StockKeepUnit.SetFilter("Vendor No.", '<>%1', '');
            if StockKeepUnit.FindFirst() then
                VendorNo := StockKeepUnit."Vendor No."
            else
                VendorNo := Item."Vendor No.";
            ShortageItemList."Item No." := ItemNo;
            ShortageItemList."Vendor No." := VendorNo;
            if Vendor.Get(VendorNo) then
                ShortageItemList."Vendor Name" := Vendor.Name;
            ShortageItemList."Item Description" := Item.Description;
            ShortageItemList."Inventory Posting Group" := Item."Inventory Posting Group";
            ShortageItemList."Item Description 2" := Item."Description 2";
            ShortageItemList."Item Category Code" := Item."Item Category Code";
            ShortageItemList."Minimum Order Qty." := Item."Minimum Order Quantity";
            ShortageItemList."Safety Stock Quantity" := Item."Safety Stock Quantity";
            ShortageItemList.Inventory := ItemLedgerEntry."Remaining Quantity";
            ShortageItemList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
            ShortageItemList.Insert(true);
            CalculateLeadTime(ShortageItemList); //BRST.006.12 - Added Line
        end;

        ShortageList.Reset();
        if not ShortageList.FindLast() then
            EntryNo := 0
        else
            EntryNo := ShortageList."Register No.";

        if PurchaseLine.FindSet() then
            repeat
                ShortageList.Init();
                ShortageList."Register No." := EntryNo + 1;
                ShortageList."Register Type" := ShortageList."Register Type"::Purchase;
                ShortageList."Item No." := ItemNo;
                ShortageList."Item Description" := PurchaseLine.Description;
                ShortageList."Source No." := PurchaseLine."Buy-from Vendor No.";
                PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");
                ShortageList."Source Name/Description" := PurchaseHeader."Buy-from Vendor Name";
                ShortageList.Quantity := PurchaseLine."Outstanding Quantity";
                ShortageList."Document No." := PurchaseLine."Document No.";
                ShortageList.Date := PurchaseLine."Expected Receipt Date";
                ShortageList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
                ShortageList.Insert(true);
                EntryNo += 1;
            until PurchaseLine.Next() = 0;
    end;

    local procedure InsertOutputLines(ItemNo: Code[20]; StartDate: Date; EndDate: Date)
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProdOrderLine: Record "Prod. Order Line";
        //PurchaseHeader: Record "Purchase Header";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageList: Record "Shortage List Detail FLX";
        StockKeepUnit: Record "Stockkeeping Unit";
        Vendor: Record Vendor;
        VendorNo: Code[20];
        EntryNo: Integer;
    begin
        if ItemNo = '' then
            exit;

        //ShortageList.Reset();
        //ShortageList.SetRange("Register Type", ShortageList."Register Type"::Output);
        //ShortageList.SetRange("Item No.", ItemNo);
        //ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        //if ShortageList.FindFirst() then
        //    exit;
        if TempItemOutput.Get(ItemNo) then
            exit;
        TempItemOutput."No." := ItemNo;
        //TempItemOutput.Insert(true); //compiler hata veriyor temp tabloda trigger olmaz diyor
        TempItemOutput.Insert(false);

        ProdOrderLine.Reset();
        ProdOrderLine.SetRange("Item No.", ItemNo);
        ProdOrderLine.SetRange("Ending Date", StartDate, EndDate);
        ProdOrderLine.SetFilter("Remaining Quantity", '>0');
        //ProdOrderLine.SETFILTER("Location Code",'<>FASON'); //BRST.006.02 - Removed Line
        ProdOrderLine.SetFilter(Status, '<>%1', ProdOrderLine.Status::Finished); //BRST.006.03 - Added Line
        Item.Get(ItemNo);

        if not ShortageItemList.Get(ItemNo, UserId()) then begin //BRST.006.07 - Modified Line
            VendorNo := '';
            //BRST.006.11 BEGIN
            ItemLedgerEntry.Reset();
            ItemLedgerEntry.SetCurrentKey("Item No.");
            ItemLedgerEntry.SetRange("Item No.", ItemNo);
            ItemLedgerEntry.SetFilter("Remaining Quantity", '>0');
            ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
            ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
            ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
            ItemLedgerEntry.CalcSums("Remaining Quantity");
            //BRST.006.11 END
            StockKeepUnit.Reset();
            StockKeepUnit.SetRange("Item No.", ItemNo);
            StockKeepUnit.SetFilter("Vendor No.", '<>%1', '');
            if StockKeepUnit.FindFirst() then
                VendorNo := StockKeepUnit."Vendor No."
            else
                VendorNo := Item."Vendor No.";
            ShortageItemList."Item No." := ItemNo;
            ShortageItemList."Vendor No." := VendorNo;
            if Vendor.Get(VendorNo) then
                ShortageItemList."Vendor Name" := Vendor.Name;
            ShortageItemList."Item Description" := Item.Description;
            ShortageItemList."Inventory Posting Group" := Item."Inventory Posting Group";
            ShortageItemList."Item Description 2" := Item."Description 2";
            ShortageItemList."Item Category Code" := Item."Item Category Code";
            ShortageItemList."Minimum Order Qty." := Item."Minimum Order Quantity";
            ShortageItemList."Safety Stock Quantity" := Item."Safety Stock Quantity";
            ShortageItemList.Inventory := ItemLedgerEntry."Remaining Quantity";
            ShortageItemList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
            ShortageItemList.Insert(true);
            CalculateLeadTime(ShortageItemList); //BRST.006.12 - Added Line
        end;

        ShortageList.Reset();
        if not ShortageList.FindLast() then
            EntryNo := 0
        else
            EntryNo := ShortageList."Register No.";

        if ProdOrderLine.FindSet() then
            repeat
                EntryNo += 1;
                ShortageList.Init();
                ShortageList."Register No." := EntryNo;
                ShortageList."Register Type" := ShortageList."Register Type"::Output;
                ShortageList."Item No." := ItemNo;
                ShortageList."Item Description" := ProdOrderLine.Description;
                //ShortageList."Source No." :=
                //ShortageList."Source Name/Description" :=
                ShortageList.Quantity := ProdOrderLine."Remaining Quantity";
                ShortageList."Document No." := ProdOrderLine."Prod. Order No.";
                ShortageList.Date := ProdOrderLine."Ending Date";
                ShortageList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
                ShortageList.Insert(true);
            until ProdOrderLine.Next() = 0;
    end;

    local procedure InsertConsuptionLines(ItemNo: Code[20]; StartDate: Date; EndDate: Date; ProdOrderStatusFilter: Text)
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProdOrderComponent: Record "Prod. Order Component";
        ProductionOrder: Record "Production Order";
        //SalesHd: Record "Sales Header";
        //SalesLine: Record "Sales Line";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageList: Record "Shortage List Detail FLX";
        StockKeepUnit: Record "Stockkeeping Unit";
        Vendor: Record Vendor;
        VendorNo: Code[20];
        //toplam: Decimal;
        EntryNo: Integer;
    begin
        if ItemNo = '' then
            exit;

        //ShortageList.Reset();
        //ShortageList.SetRange("Register Type", ShortageList."Register Type"::Consuption);
        //ShortageList.SetRange("Item No.", ItemNo);
        //ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        //if ShortageList.FindFirst() then
        //    exit;
        if TempItemConsump.Get(ItemNo) then
            exit;
        TempItemConsump."No." := ItemNo;
        //TempItemConsump.Insert(true); //compiler hata veriyor temp tabloda trigger olmaz diyor
        TempItemConsump.Insert(false);

        ProdOrderComponent.Reset();
        if ProdOrderStatusFilter = '' then
            //ProdOrderComponent.SETRANGE(Status,ProdOrderComponent.Status::Released) //FLEX-240 cancelled
            ProdOrderComponent.SetFilter(Status, '%1|%2', ProdOrderComponent.Status::Released, ProdOrderComponent.Status::"Firm Planned") //FLEX-240 added
        else
            ProdOrderComponent.SetFilter(Status, ProdOrderStatusFilter); //BRST.006.09 - Added Line
        ProdOrderComponent.SetRange("Item No.", ItemNo);
        ProdOrderComponent.SetRange("Due Date", StartDate, EndDate);
        ProdOrderComponent.SetFilter("Remaining Quantity", '>0');

        Item.Get(ItemNo);

        if not ShortageItemList.Get(ItemNo, UserId()) then begin //BRST.006.07 - Modified Line
            VendorNo := '';
            //BRST.006.11 BEGIN
            ItemLedgerEntry.Reset();
            ItemLedgerEntry.SetCurrentKey("Item No.");
            ItemLedgerEntry.SetRange("Item No.", ItemNo);
            ItemLedgerEntry.SetFilter("Remaining Quantity", '>0');
            ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
            ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
            ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
            ItemLedgerEntry.CalcSums("Remaining Quantity");
            //BRST.006.11 END
            StockKeepUnit.Reset();
            StockKeepUnit.SetRange("Item No.", ItemNo);
            StockKeepUnit.SetFilter("Vendor No.", '<>%1', '');
            if StockKeepUnit.FindFirst() then
                VendorNo := StockKeepUnit."Vendor No."
            else
                VendorNo := Item."Vendor No.";
            ShortageItemList."Item No." := ItemNo;
            ShortageItemList."Vendor No." := VendorNo;
            if Vendor.Get(VendorNo) then
                ShortageItemList."Vendor Name" := Vendor.Name;
            ShortageItemList."Item Description" := Item.Description;
            ShortageItemList."Inventory Posting Group" := Item."Inventory Posting Group";
            ShortageItemList."Item Description 2" := Item."Description 2";
            ShortageItemList."Item Category Code" := Item."Item Category Code";
            ShortageItemList."Minimum Order Qty." := Item."Minimum Order Quantity";
            ShortageItemList."Safety Stock Quantity" := Item."Safety Stock Quantity";
            ShortageItemList.Inventory := ItemLedgerEntry."Remaining Quantity";
            ShortageItemList."User Id" := CopyStr(UserId(), 1, 50);   //BRST.006.07 - Added Line
            ShortageItemList.Insert(true);
            CalculateLeadTime(ShortageItemList); //BRST.006.12 - Added Line
        end;

        ShortageList.Reset();
        if not ShortageList.FindLast() then
            EntryNo := 0
        else
            EntryNo := ShortageList."Register No.";
        //toplam := 0;
        if ProdOrderComponent.FindSet() then
            repeat
                EntryNo += 1;
                ProductionOrder.Get(ProdOrderComponent.Status, ProdOrderComponent."Prod. Order No.");
                ShortageList.Init();
                ShortageList."Register No." := EntryNo;
                ShortageList."Register Type" := ShortageList."Register Type"::Consuption;
                ShortageList."Item No." := ItemNo;
                ShortageList."Item Description" := ProdOrderComponent.Description;
                ShortageList."Source No." := ProductionOrder."Source No.";
                ShortageList."Source Name/Description" := ProductionOrder.Description;
                ShortageList.Quantity := ProdOrderComponent."Remaining Quantity" * -1;
                ShortageList."Document No." := ProdOrderComponent."Prod. Order No.";
                ShortageList.Date := ProdOrderComponent."Due Date";
                ShortageList."User Id" := CopyStr(UserId(), 1, 50);  //BRST.006.07 - Added Line
                ShortageList.Insert(true);
            //toplam += ProdOrderComponent."Remaining Quantity";
            until ProdOrderComponent.Next() = 0;
        //IF ItemNo = '100-40' THEN
        //  MESSAGE('toplam bileşen : '+FORMAT(toplam));
    end;

    local procedure InsertSalesLines(ItemNo: Code[20]; StartDate: Date; EndDate: Date)
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageList: Record "Shortage List Detail FLX";
        StockKeepUnit: Record "Stockkeeping Unit";
        Vendor: Record Vendor;
        //CustomerNo: Code[20];
        VendorNo: Code[20];
        EntryNo: Integer;
    begin
        if ItemNo = '' then
            exit;

        //ShortageList.Reset();
        //ShortageList.SetRange("Register Type", ShortageList."Register Type"::Sale);
        //ShortageList.SetRange("Item No.", ItemNo);
        //ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        //if ShortageList.FindFirst() then
        //    exit;
        if TempItemSales.Get(ItemNo) then
            exit;
        TempItemSales."No." := ItemNo;
        //TempItemSales.Insert(true); //compiler hata veriyor temp tabloda trigger olmaz diyor
        TempItemSales.Insert(false);

        SalesLine.Reset();
        SalesLine.SetRange("No.", ItemNo);
        //SalesLine.SETRANGE("Planned Delivery Date",StartDate,EndDate);
        SalesLine.SetRange("Flexati Shipment Date FLX", StartDate, EndDate);
        SalesLine.SetFilter("Outstanding Quantity", '>0');
        SalesLine.SetRange("Document Type", SalesLine."Document Type"::Order); //BRST.006.10 - Added line
        Item.Get(ItemNo);

        if not ShortageItemList.Get(ItemNo, UserId()) then begin //BRST.006.07 - Modified Line
            VendorNo := '';
            //BRST.006.11 BEGIN
            ItemLedgerEntry.Reset();
            ItemLedgerEntry.SetCurrentKey("Item No.");
            ItemLedgerEntry.SetRange("Item No.", ItemNo);
            ItemLedgerEntry.SetFilter("Remaining Quantity", '>0');
            ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
            ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
            ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
            ItemLedgerEntry.CalcSums("Remaining Quantity");
            //BRST.006.11 END
            StockKeepUnit.Reset();
            StockKeepUnit.SetRange("Item No.", ItemNo);
            StockKeepUnit.SetFilter("Vendor No.", '<>%1', '');
            if StockKeepUnit.FindFirst() then
                VendorNo := StockKeepUnit."Vendor No."
            else
                VendorNo := Item."Vendor No.";
            ShortageItemList."Item No." := ItemNo;
            ShortageItemList."Vendor No." := VendorNo;
            if Vendor.Get(VendorNo) then
                ShortageItemList."Vendor Name" := Vendor.Name;
            ShortageItemList."Item Description" := Item.Description;
            ShortageItemList."Inventory Posting Group" := Item."Inventory Posting Group";
            ShortageItemList."Item Description 2" := Item."Description 2";
            ShortageItemList."Item Category Code" := Item."Item Category Code";
            ShortageItemList."Minimum Order Qty." := Item."Minimum Order Quantity";
            ShortageItemList."Safety Stock Quantity" := Item."Safety Stock Quantity";
            ShortageItemList.Inventory := ItemLedgerEntry."Remaining Quantity";
            ShortageItemList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
            ShortageItemList.Insert(true);
            CalculateLeadTime(ShortageItemList); //BRST.006.12 - Added Line
        end;

        ShortageList.Reset();
        if not ShortageList.FindLast() then
            EntryNo := 0
        else
            EntryNo := ShortageList."Register No.";

        if SalesLine.FindSet() then
            repeat
                ShortageList.Init();
                ShortageList."Register No." := EntryNo + 1;
                ShortageList."Register Type" := ShortageList."Register Type"::Sale;
                ShortageList."Item No." := ItemNo;
                ShortageList."Item Description" := SalesLine.Description;
                ShortageList."Source No." := SalesLine."Sell-to Customer No.";
                SalesHeader.Get(SalesLine."Document Type", SalesLine."Document No.");
                ShortageList."Source Name/Description" := SalesHeader."Sell-to Customer Name";
                ShortageList.Quantity := SalesLine."Outstanding Quantity" * -1;
                ShortageList."Document No." := SalesLine."Document No.";
                //ShortageList.Date := SalesLine."Planned Delivery Date";
                ShortageList.Date := SalesLine."Flexati Shipment Date FLX";
                ShortageList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
                ShortageList.Insert(true);
                EntryNo += 1;
            until SalesLine.Next() = 0;
    end;

    local procedure InsertRealConsump(ItemNo: Code[20]; StartDate: Date; EndDate: Date)
    var
        Customer: Record Customer;
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        //SalesHeader: Record "Sales Header";
        //SalesLine: Record "Sales Line";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageList: Record "Shortage List Detail FLX";
        StockKeepUnit: Record "Stockkeeping Unit";
        Vendor: Record Vendor;
        //CustomerNo: Code[20];
        VendorNo: Code[20];
        EntryNo: Integer;
    begin
        if ItemNo = '' then
            exit;

        //ShortageList.Reset();
        //ShortageList.SetRange("Register Type", ShortageList."Register Type"::RealConsump);
        //ShortageList.SetRange("Item No.", ItemNo);
        //ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        //if ShortageList.FindFirst() then
        //    exit;
        if TempItemReal.Get(ItemNo) then
            exit;
        TempItemReal."No." := ItemNo;
        //TempItemReal.Insert(true); //compiler hata veriyor temp tabloda trigger olmaz diyor
        TempItemReal.Insert(false);

        Item.Get(ItemNo);
        if not ShortageItemList.Get(ItemNo, UserId()) then begin  //BRST.006.07 - Modified Line
            VendorNo := '';
            //BRST.006.11 BEGIN
            ItemLedgerEntry.Reset();
            ItemLedgerEntry.SetCurrentKey("Item No.");
            ItemLedgerEntry.SetRange("Item No.", ItemNo);
            ItemLedgerEntry.SetFilter("Remaining Quantity", '>0');
            ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
            ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
            ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
            ItemLedgerEntry.CalcSums("Remaining Quantity");
            //BRST.006.11 END
            StockKeepUnit.Reset();
            StockKeepUnit.SetRange("Item No.", ItemNo);
            StockKeepUnit.SetFilter("Vendor No.", '<>%1', '');
            if StockKeepUnit.FindFirst() then
                VendorNo := StockKeepUnit."Vendor No."
            else
                VendorNo := Item."Vendor No.";
            ShortageItemList."Item No." := ItemNo;
            ShortageItemList."Vendor No." := VendorNo;
            if Vendor.Get(VendorNo) then
                ShortageItemList."Vendor Name" := Vendor.Name;
            ShortageItemList."Item Description" := Item.Description;
            ShortageItemList."Inventory Posting Group" := Item."Inventory Posting Group";
            ShortageItemList."Item Description 2" := Item."Description 2";
            ShortageItemList."Item Category Code" := Item."Item Category Code";
            ShortageItemList."Minimum Order Qty." := Item."Minimum Order Quantity";
            ShortageItemList."Safety Stock Quantity" := Item."Safety Stock Quantity";
            ShortageItemList.Inventory := ItemLedgerEntry."Remaining Quantity";
            ShortageItemList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
            ShortageItemList.Insert(true);
            CalculateLeadTime(ShortageItemList); //BRST.006.12 - Added Line
        end;

        ShortageList.Reset();
        if not ShortageList.FindLast() then
            EntryNo := 0
        else
            EntryNo := ShortageList."Register No.";

        ItemLedgerEntry.Reset();
        ItemLedgerEntry.SetCurrentKey("Item No.");
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetRange("Posting Date", StartDate, EndDate);
        ItemLedgerEntry.SetFilter("Entry Type", '%1|%2', ItemLedgerEntry."Entry Type"::Consumption, ItemLedgerEntry."Entry Type"::Sale);
        ItemLedgerEntry.SetFilter("Location Code", '<>%1', RejectLocationCode);
        ItemLedgerEntry.SetRange("Subcontractor Location FLX", false);
        ItemLedgerEntry.SetRange("Exclude From Shortage List FLX", false);
        if ItemLedgerEntry.FindSet() then
            repeat
                ShortageList.Init();
                ShortageList."Register No." := EntryNo + 1;
                ShortageList."Register Type" := ShortageList."Register Type"::RealConsump;
                ShortageList."Item No." := ItemNo;
                if ItemLedgerEntry.Description = '' then
                    ShortageList."Item Description" := Item.Description
                else
                    ShortageList."Item Description" := ItemLedgerEntry.Description;
                ShortageList."Source No." := ItemLedgerEntry."Source No.";
                if Customer.Get(ShortageList."Source No.") then
                    ShortageList."Source Name/Description" := Customer.Name;
                ShortageList.Quantity := ItemLedgerEntry.Quantity * -1;
                ShortageList."Document No." := ItemLedgerEntry."Document No.";
                ShortageList.Date := ItemLedgerEntry."Posting Date";
                ShortageList."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
                ShortageList.Insert(true);
                EntryNo += 1;
            until ItemLedgerEntry.Next() = 0;
    end;

    local procedure CalculateCumulativeQuantity(ItemNo: Code[20]; DocumentDate: Date): Decimal
    var
        ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
    begin

        ShortageListCumulativeQty.Reset();
        ShortageListCumulativeQty.SetRange("Item No.", ItemNo);
        ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        ShortageListCumulativeQty.SetFilter("Document Date", '<=%1', DocumentDate);
        ShortageListCumulativeQty.CalcSums("Net Qty.");

        exit(ShortageListCumulativeQty."Net Qty.");
    end;

    local procedure ShortageListLoop()
    var
        //ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
        ShortageList: Record "Shortage List Detail FLX";
    begin
        ShortageList.Reset();
        ShortageList.SetCurrentKey(Date, "Item No.", "User Id"); //BRST.006.07 - Modified Line
        ShortageList.SetRange("User Id", UserId()); //BRST.006.10 - Added Line
        if ShortageList.FindSet() then
            repeat
                InsertToShortageListCumQty(ShortageList);
            until ShortageList.Next() = 0;
    end;

    local procedure InsertToShortageListCumQty(var ShortageList: Record "Shortage List Detail FLX")
    var
        ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
    begin
        if ShortageListCumulativeQty.Get(ShortageList.Date, ShortageList."Item No.", UserId()) then begin
            if (ShortageList."Register Type" = ShortageList."Register Type"::Consuption) or (ShortageList."Register Type" = ShortageList."Register Type"::Sale) then
                ShortageListCumulativeQty."Negative Qty." += ShortageList.Quantity
            else
                if (ShortageList."Register Type" = ShortageList."Register Type"::"On Hand") or (ShortageList."Register Type" = ShortageList."Register Type"::Output) or
                    (ShortageList."Register Type" = ShortageList."Register Type"::Purchase) then //BRST.006.04 - Modified Line
                    ShortageListCumulativeQty."Positive Qty." += ShortageList.Quantity
                else
                    if ShortageList."Register Type" = ShortageList."Register Type"::RealConsump then
                        ShortageListCumulativeQty."Real Consump Qty" += ShortageList.Quantity;

            ShortageListCumulativeQty."Net Qty." := ShortageListCumulativeQty."Positive Qty." + ShortageListCumulativeQty."Negative Qty.";
            ShortageListCumulativeQty.Modify(true);
            ShortageListCumulativeQty."Cumulative Net Qty." := CalculateCumulativeQuantity(ShortageListCumulativeQty."Item No.", ShortageListCumulativeQty."Document Date");
        end
        else begin
            ShortageListCumulativeQty.Init();
            ShortageListCumulativeQty."Document Date" := ShortageList.Date;
            ShortageListCumulativeQty."Item No." := ShortageList."Item No.";
            if (ShortageList."Register Type" = ShortageList."Register Type"::Consuption) or (ShortageList."Register Type" = ShortageList."Register Type"::Sale) then //BRST.006.04 - Modified Line
                ShortageListCumulativeQty."Negative Qty." := ShortageList.Quantity
            else
                if (ShortageList."Register Type" = ShortageList."Register Type"::"On Hand") or (ShortageList."Register Type" = ShortageList."Register Type"::Output) or
                    (ShortageList."Register Type" = ShortageList."Register Type"::Purchase) then
                    ShortageListCumulativeQty."Positive Qty." := ShortageList.Quantity
                else
                    if ShortageList."Register Type" = ShortageList."Register Type"::RealConsump then
                        ShortageListCumulativeQty."Real Consump Qty" := ShortageList.Quantity;

            ShortageListCumulativeQty."Net Qty." := ShortageListCumulativeQty."Positive Qty." + ShortageListCumulativeQty."Negative Qty.";
            ShortageListCumulativeQty."User Id" := CopyStr(UserId(), 1, 50); //BRST.006.07 - Added Line
            ShortageListCumulativeQty.Insert(true);
            ShortageListCumulativeQty."Cumulative Net Qty." := CalculateCumulativeQuantity(ShortageListCumulativeQty."Item No.", ShortageListCumulativeQty."Document Date");
        end;

        ShortageListCumulativeQty.Modify(true);
    end;

    local procedure Update_ShortItemList(ItemNo: Code[20])
    var
        DefaultDim: Record "Default Dimension";
        ShortageItemList: Record "Shortage Item List FLX";
        ShortageListCumulativeQty: Record "Shortage List - Cumulative Qty";
        ShortageList: Record "Shortage List Detail FLX";
        ShortageListDetail: Record "Shortage List Detail FLX";
    begin
        //BRST.006.11 - Added Line
        if ItemNo = '' then
            exit;
        if not ShortageItemList.Get(ItemNo, UserId()) then
            exit;
        //PRODUCTHIERARCHY Dim Code
        DefaultDim.Reset();
        DefaultDim.SetRange(DefaultDim."Table ID", 27);
        DefaultDim.SetRange(DefaultDim."No.", ItemNo);
        DefaultDim.SetRange(DefaultDim."Dimension Code", 'PRODUCTHIERARCHY');
        if DefaultDim.FindFirst() then begin
            ShortageItemList."Product hyerachy dim. value" := DefaultDim."Dimension Value Code";
            ShortageItemList.Modify(true);
        end;
        ShortageListDetail.Reset();
        ShortageListDetail.SetRange("Item No.", ItemNo);
        ShortageListDetail.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        ShortageListDetail.SetRange("Register Type", ShortageListDetail."Register Type"::"On Hand");
        if ShortageListDetail.FindFirst() then begin
            ShortageItemList."Quantity On Reject Loc." := ShortageListDetail."Quantity On Reject Location";
            ShortageItemList."Quantity On Subcontractors" := ShortageListDetail."Quantity On Subcontractors";
            ShortageItemList."Quantity On UHD Location" := ShortageListDetail."Quantity On UHD Location";
            ShortageItemList."Quantity On WHSE Location" := ShortageListDetail."Quantity On WHSE Location";//FLEX-240
            ShortageItemList.Modify(true);
        end;
        ShortageListCumulativeQty.Reset();
        ShortageListCumulativeQty.SetRange("Item No.", ItemNo);
        ShortageListCumulativeQty.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        if ShortageListCumulativeQty.FindFirst() then begin
            ShortageItemList."Cumulative Net Qty." := ShortageListCumulativeQty."Cumulative Net Qty.";
            ShortageItemList.Modify(true);
        end;

        ShortageList.Reset();
        ShortageList.SetRange("Item No.", ItemNo);
        ShortageList.SetRange("User Id", UserId()); //BRST.006.07 - Modified Line
        ShortageList.SetFilter(Quantity, '<>%1', 0);
        if (ShortageList.FindFirst()) or (not ShortageList.IsEmpty()) then
            exit;
        if (ShortageItemList.Get(ItemNo, UserId())) and (ShortageItemList.Inventory = 0) and (ShortageItemList."Qty. on Purch. Order" = 0) and
        (ShortageItemList."Qty. on Sales Order" = 0) and (ShortageItemList."Qty. on Component Lines" = 0) then
            ShortageItemList.Delete(true);
    end;

    local procedure CalculateLeadTime(var ShortageItemList: Record "Shortage Item List FLX")
    var
        Item: Record Item;
        StockkeepingUnit: Record "Stockkeeping Unit";
    begin
        //BRST.006.12 - Added Function
        StockkeepingUnit.Reset();
        StockkeepingUnit.SetRange("Location Code", 'INBOUND');
        StockkeepingUnit.SetRange("Item No.", ShortageItemList."Item No.");
        if StockkeepingUnit.FindFirst() then
            ShortageItemList."Lead Time Calculation" := StockkeepingUnit."Lead Time Calculation"
        else begin
            Item.Get(ShortageItemList."Item No.");
            ShortageItemList."Lead Time Calculation" := Item."Lead Time Calculation";
            ShortageItemList."Lead Time as Date" := CalcDate(ShortageItemList."Lead Time Calculation", Today());
            if ShortageItemList."Lead Time as Date" = Today() then
                ShortageItemList."Lead Time as Date" := 0D;
        end;
        CalculateQtyOnConsuptionLinesAndPurchOrders(ShortageItemList);
        ShortageItemList.Modify(true);
    end;

    local procedure CalculateQtyOnConsuptionLinesAndPurchOrders(var ShortageItemList: Record "Shortage Item List FLX")
    var
        Item: Record Item;
    begin
        //BRST.006.12 - Added Function
        Item.Get(ShortageItemList."Item No.");
        Item.CalcFields(Item."Qty. on Component Lines", Item."Qty. on Purch. Order", Item."Qty. on Sales Order");
        ShortageItemList."Qty. on Component Lines" := Item."Qty. on Component Lines";
        ShortageItemList."Qty. on Purch. Order" := Item."Qty. on Purch. Order";
        ShortageItemList."Qty. on Sales Order" := Item."Qty. on Sales Order";
    end;

    local procedure CalcSubComp(UpItemNo: Code[20]; StartDate: Date; EndDate: Date; ProdOrderStatusFilter: Text; ExcludeSupplyQuantities: Boolean)
    var
        Item: Record Item;
        ProdBomLine: Record "Production BOM Line";
        TempItemNo: Code[20];
    begin
        //BRST.006.16
        if UpItemNo = '' then
            exit;
        Item.Get(UpItemNo);
        if Item."Production BOM No." = '' then
            exit;
        ProdBomLine.Reset();
        ProdBomLine.SetRange(ProdBomLine."Production BOM No.", Item."Production BOM No.");
        if ProdBomLine.FindSet() then
            repeat
                TempItemNo := ProdBomLine."No.";
                InsertOnHandSubcontractorRejectedQuantityLine(TempItemNo, StartDate);
                InsertConsuptionLines(TempItemNo, StartDate, EndDate, ProdOrderStatusFilter); //BRST.006.09 - Modified Line
                InsertSalesLines(TempItemNo, StartDate, EndDate);
                if not ExcludeSupplyQuantities then begin
                    InsertPurchaseLines(TempItemNo, StartDate, EndDate);
                    InsertOutputLines(TempItemNo, StartDate, EndDate);
                end;
                Update_ShortItemList(TempItemNo); //BRST.006.11 - Added Line
                CalcSubComp(TempItemNo, StartDate, EndDate, ProdOrderStatusFilter, ExcludeSupplyQuantities);
            until ProdBomLine.Next() = 0;
    end;

    var
        TempItemConsump: Record Item temporary;
        TempItemOnHand: Record Item temporary;
        TempItemOutput: Record Item temporary;
        TempItemPurchase: Record Item temporary;
        TempItemReal: Record Item temporary;
        TempItemSales: Record Item temporary;
        RejectLocationCode: Code[10];
        UHDLocationCode: Code[10];
        WHSELocationCode: Code[10];
        ProcessCompletedMsg: Label 'Process Completed';
    //SubcontractorLocationsFilter: Text[250];
}

tableextension 60011 "Location FLX" extends Location
{
    fields
    {
        field(60000; "Include In Calculation FLX"; Boolean)
        {
            Caption = 'Include In Calculation';
            ToolTip = 'Specifies the value of the Include In Calculation field.';
        }
        field(60001; "Q. C. Accept Required FLX"; Boolean)
        {
            Caption = 'Quality Control Accept Required';
            ToolTip = 'Specifies the value of the Quality Control Accept Required field.';
        }
        field(60002; "Subcontractor Location FLX"; Boolean)
        {
            AllowInCustomizations = Always;
            Caption = 'Subcontractor Location';
        }
        field(60003; "Quality Control Location FLX"; Boolean)
        {
            Caption = 'Quality Control Location';
            ToolTip = 'Specifies the value of the Quality Control Location field.';
        }
    }
}
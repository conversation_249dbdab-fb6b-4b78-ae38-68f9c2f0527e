report 60008 "Prod. Ord. Material Req. Total"
{
    DefaultLayout = RDLC;
    RDLCLayout = './src/ReportLayouts/ProdOrderMatRequisitionTotal.rdlc';
    ApplicationArea = Manufacturing;
    Caption = 'Total Prod. Order - Mat. Requisition';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem("Production Order"; "Production Order")
        {
            DataItemTableView = sorting(Status, "No.");
            PrintOnlyIfDetail = true;
            RequestFilterFields = Status, "No.", "Source Type", "Source No.";
            column(No_ProdOrder; "No.")
            {
            }
            column(Desc_ProdOrder; Description)
            {
            }
            column(SourceNo_ProdOrder; "Source No.")
            {
                IncludeCaption = true;
            }
            column(Status_ProdOrder; Status)
            {
            }
            column(Qty_ProdOrder; Quantity)
            {
                IncludeCaption = true;
            }

            trigger OnPreDataItem()
            begin
                ProdOrderFilter := GetFilters();
                //SetRange(Status, Status::Released);
            end;

            trigger OnAfterGetRecord()
            begin
                ProdOrderComp.Reset();
                ProdOrderComp.SetRange(Status, "Production Order".Status);
                ProdOrderComp.SetRange("Prod. Order No.", "Production Order"."No.");
                ProdOrderComp.SetFilter("Remaining Quantity", '<>0');
                if ProdOrderComp.FindSet() then
                    repeat
                        RemainingQtyReserved := 0;
                        ReservationEntry.Reset();
                        ReservationEntry.SetCurrentKey("Source ID", "Source Ref. No.", "Source Type", "Source Subtype");
                        ReservationEntry.SetRange("Source Type", Database::"Prod. Order Component");
                        ReservationEntry.SetRange("Source ID", "Production Order"."No.");
                        ReservationEntry.SetRange("Source Ref. No.", ProdOrderComp."Line No.");
                        ReservationEntry.SetRange("Source Subtype", Status);
                        ReservationEntry.SetRange("Source Batch Name", '');
                        ReservationEntry.SetRange("Source Prod. Order Line", ProdOrderComp."Prod. Order Line No.");
                        if ReservationEntry.FindSet() then
                            repeat
                                if ReservationEntry2.Get(ReservationEntry."Entry No.", not ReservationEntry.Positive) then
                                    if (ReservationEntry2."Source Type" = Database::"Prod. Order Line") and
                                       (ReservationEntry2."Source ID" = ProdOrderComp."Prod. Order No.")
                                    then
                                        RemainingQtyReserved += ReservationEntry2."Quantity (Base)";
                            until ReservationEntry.Next() = 0;

                        if ProdOrderComp."Remaining Qty. (Base)" <> RemainingQtyReserved then begin
                            TempProdOrderComponent.Reset();
                            TempProdOrderComponent.SetRange("Item No.", ProdOrderComp."Item No.");
                            if not TempProdOrderComponent.FindFirst() then begin
                                TempProdOrderComponent.Init();
                                TempProdOrderComponent := ProdOrderComp;
                                TempProdOrderComponent."Remaining Quantity" := 0;
                                TempProdOrderComponent."Remaining Qty. (Base)" := 0;
                                TempProdOrderComponent.Insert(false);
                            end;
                            TempProdOrderComponent."Remaining Quantity" += ProdOrderComp."Remaining Quantity";
                            TempProdOrderComponent."Remaining Qty. (Base)" += ProdOrderComp."Remaining Qty. (Base)";
                            TempProdOrderComponent.Modify(false);
                        end;
                    until ProdOrderComp.Next() = 0;
            end;
        }
        dataitem(Integer1; Integer)
        {
            column(TodayFormatted; Format(Today(), 0, 4))
            {
            }
            column(CompanyName; CompanyProperty.DisplayName())
            {
            }
            column(ProdOrderTableCaptionFilter; TableCaption() + ':' + ProdOrderFilter)
            {
            }
            column(Filter_ProdOrder; ProdOrderFilter)
            {
            }
            column(ProdOrderMaterialRqstnCapt; ProdOrderMaterialRqstnCaptLbl)
            {
            }
            column(CurrReportPageNoCapt; CurrReportPageNoCaptLbl)
            {
            }
            column(Number_Integer; Integer1.Number)
            {
            }
            column(ItemNo; ItemNo)
            {
            }
            column(ItemDesc; ItemDesc)
            {
            }
            column(Qtyper; Qtyper)
            {
            }
            column(UOMCode; UOMCode)
            {
            }
            column(RemainingQty; RemainingQty)
            {
            }
            column(Scrap; Scrap)
            {
            }
            column(DueDate; Format(DueDate))
            {
            }
            column(LocationCode; LocationCode)
            {
            }

            trigger OnPreDataItem()
            begin
                TempProdOrderComponent.Reset();
                Integer1.SetRange(Number, 1, TempProdOrderComponent.Count());
            end;

            trigger OnAfterGetRecord()
            begin
                if Integer1.Number = 1 then
                    TempProdOrderComponent.FindFirst()
                else
                    TempProdOrderComponent.Next();
                ItemNo := TempProdOrderComponent."Item No.";
                ItemDesc := TempProdOrderComponent.Description;
                Qtyper := TempProdOrderComponent."Quantity per";
                UOMCode := TempProdOrderComponent."Unit of Measure Code";
                RemainingQty := TempProdOrderComponent."Remaining Quantity";
                Scrap := TempProdOrderComponent."Scrap %";
                DueDate := TempProdOrderComponent."Due Date";
                LocationCode := TempProdOrderComponent."Location Code";
            end;
        }
    }

    requestpage
    {
        layout
        {
        }

        actions
        {
        }
    }

    labels
    {
        ProdOrderCompDueDateCapt = 'Due Date';
    }

    trigger OnPreReport()
    begin
        //"Production Order".SetRange(Status, "Production Order".Status::Released);
    end;

    var
        ProdOrderComp: Record "Prod. Order Component";
        TempProdOrderComponent: Record "Prod. Order Component" temporary;
        ReservationEntry: Record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";
        UOMCode: Code[10];
        ItemNo: Code[20];
        LocationCode: Code[20];
        DueDate: Date;
        Qtyper: Decimal;
        RemainingQty: Decimal;
        RemainingQtyReserved: Decimal;
        Scrap: Decimal;
        CurrReportPageNoCaptLbl: Label 'Page';
        ProdOrderMaterialRqstnCaptLbl: Label 'Prod. Order - Material Requisition';
        ProdOrderFilter: Text;
        ItemDesc: Text[100];
}
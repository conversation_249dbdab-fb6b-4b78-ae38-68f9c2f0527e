pageextension 60010 "Posted Sales Invoices Ext. FLX" extends "Posted Sales Invoices"
{
    actions
    {
        addafter(Print)
        {
            action("InvoiceFlex FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Invoice Flexati';
                Image = Print;
                ToolTip = 'Invoice Flexati.';

                trigger OnAction()
                var
                    SalesInvHd: Record "Sales Invoice Header";
                begin
                    SalesInvHd.SetRange("No.", Rec."No.");
                    Report.RunModal(Report::"PostedSalesInvoice FLX", true, false, SalesInvHd);
                end;
            }
            action("Sbuf Report FLX")
            {
                ApplicationArea = All;
                Promoted = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                Caption = 'Sbuf Report';
                Image = Print;
                ToolTip = 'Sbuf Report.';

                trigger OnAction()
                var
                    SalesInvHd: Record "Sales Invoice Header";
                begin
                    SalesInvHd.SetRange("No.", Rec."No.");
                    Report.RunModal(Report::"Posted Sbuf Report FLX", true, false, SalesInvHd);
                end;
            }
        }
    }
}
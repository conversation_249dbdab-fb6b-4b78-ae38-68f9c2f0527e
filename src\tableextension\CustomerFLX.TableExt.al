tableextension 60002 "Customer FLX" extends Customer
{
    fields
    {
        field(60000; "Company Bank Account Code FLX"; Code[20])
        {
            Caption = 'Company Bank Account Code';
            ToolTip = 'Specifies the value of the Bank Account No. field.';
        }
        field(60001; "Company Bank Account Name FLX"; Text[100])
        {
            Caption = 'Company Bank Account Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Bank Account".Name where("No." = field("Company Bank Account Code FLX")));
            ToolTip = 'Specifies the value of the Bank Account Name field.';
        }
        field(60002; "Note FLX"; Text[500])
        {
            Caption = 'Note';
            ToolTip = 'Specifies the value of the Note field.';
        }
        field(60003; "Supplier Number FLX"; Text[100])
        {
            Caption = 'Supplier Number';
            ToolTip = 'Specifies the value of Supplier Number field.';
        }
    }
}
tableextension 60010 "Prod. Order Routing Line FLX" extends "Prod. Order Routing Line"
{
    fields
    {
        field(60000; "Finished Quantity FLX"; Decimal)
        {
            Caption = 'Finished Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Finished Quantity" where("Prod. Order No." = field("Prod. Order No."),
                                                                              Status = field(Status),
                                                                              "Routing No." = field("Routing No."),
                                                                              "Routing Reference No." = field("Routing Reference No.")));
            ToolTip = 'Specifies the value of the Finished Quantity field.';
        }
        field(60001; "Remaining Quantity FLX"; Decimal)
        {
            Caption = 'Remaining Quantity';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Remaining Quantity" where("Prod. Order No." = field("Prod. Order No."),
                                                                              Status = field(Status),
                                                                              "Routing No." = field("Routing No."),
                                                                              "Routing Reference No." = field("Routing Reference No.")));
        }
        field(60002; "Hose Lenght FLX"; Decimal)
        {
            Caption = 'Hose Length';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Hose Length FLX" where("Prod. Order No." = field("Prod. Order No."),
                                                                              Status = field(Status),
                                                                              "Routing No." = field("Routing No."),
                                                                              "Routing Reference No." = field("Routing Reference No.")));
            ToolTip = 'Specifies the value of the Hose Lenght field.';
        }
        field(60003; "Line Lenght FLX"; Decimal)
        {
            Caption = 'Line Length';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Work Center"."Line Lenght (m) FLX" where("No." = field("Work Center No.")));
            ToolTip = 'Specifies the value of the Line Lenght field.';
        }
        field(60004; "Quantity to Plan FLX"; Decimal)
        {
            Caption = 'Quantity to Plan';
            ToolTip = 'Specifies the value of the Quantity to Plan field.';
        }
        field(60005; "Printed Head Label Lenght FLX"; Decimal)
        {
            Caption = 'Total Printed Head Label Length';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package No. Information"."Label Lenght FLX" where("Production Order No. FLX" = field("Prod. Order No."),
                                                                                    "Production Order Line No. FLX" = field("Routing Reference No."),
                                                                                    "Package Type FLX" = const(Head)));
            ToolTip = 'Specifies the value of the Total Printed Head Label Lenght field.';
        }
        // field(60006; "Line No FLX"; Integer)
        // {
        //     Caption = 'Line No';
        // }
        // field(60007; "Source Sales Order No. FLX"; Code[20])
        // {
        //     Caption = 'Source Sales Order No.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Production Order"."Source Sales Order No. FLX" where(Status = field(Status), "No." = field("Prod. Order No.")));
        // }
        // field(60008; "Customer Name FLX"; Text[100])
        // {
        //     Caption = 'Customer Name';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Prod. Order Line"."Customer Name FLX" where("Prod. Order No." = field("Prod. Order No."), "Line No." = field("Routing Reference No.")));
        // }
        // field(60009; "Prod. Source No. FLX"; Code[20])
        // {
        //     Caption = 'Prod. Source No.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Production Order"."Source No." where(Status = field(Status), "No." = field("Prod. Order No.")));
        // }
        // field(60010; "Prod. Source Description FLX"; Text[100])
        // {
        //     Caption = 'Prod. Source Description';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Production Order".Description where(Status = field(Status), "No." = field("Prod. Order No.")));
        // }
        // field(60011; "Prod. Quantity FLX"; Decimal)
        // {
        //     Caption = 'Prod. Quantity';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Prod. Order Line".Quantity where(Status = field(Status), "Prod. Order No." = field("Prod. Order No.")));
        // }
        field(60012; "Sales Order Line No. FLX"; Integer)
        {
            Caption = 'Sales Order Line No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Source Line No. FLX" where("Prod. Order No." = field("Prod. Order No."), "Line No." = field("Routing Reference No.")));
            ToolTip = 'Specifies the value of the Sales Order Line No. field.';
        }
        field(60006; "Prod. Order - Line No. FLX"; Code[50])
        {
            Caption = 'Prod. Order - Line No.';
            ToolTip = 'Specifies the value of the Prod. Order - Line No. FLX field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Prod. Order - Line No. FLX" where("Prod. Order No." = field("Prod. Order No."), "Line No." = field("Routing Reference No.")));
        }
        field(60007; "Item No. FLX"; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Item No." where("Prod. Order No." = field("Prod. Order No."), "Line No." = field("Routing Reference No.")));
        }
        /*field(60013; "FG Qty. FLX"; Decimal)
        {
            Caption = 'FG Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum("Item Ledger Entry"."Remaining Quantity" WHERE("Location Code" = CONST('FG')));
        }
        field(60014; "KALITE Qty. FLX"; Decimal)
        {
            Caption = 'KALITE Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum("Item Ledger Entry"."Remaining Quantity" WHERE("Location Code" = CONST('KALITE')));
        }
        field(60015; "UHD Qty. FLX"; Decimal)
        {
            Caption = 'UHD Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum("Item Ledger Entry"."Remaining Quantity" WHERE("Location Code" = CONST('UHD')));
        }
        field(60016; "YURUMEYEN Qty. FLX"; Decimal)
        {
            Caption = 'YURUMEYEN Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = Sum("Item Ledger Entry"."Remaining Quantity" WHERE("Location Code" = CONST('YURUMEYEN')));
        }
        */
    }
}
pageextension 60006 "Sales Lines FLX" extends "Sales Lines"
{
    layout
    {
        addafter("Sell-to Customer No.")
        {
            field("Available Inventory FLX"; Rec."Available Inventory FLX")
            {
                ApplicationArea = All;
            }
            field("Coil Weight FLX"; Rec."Unit Weight (Base) FLX")
            {
                ApplicationArea = All;
            }
            field("Hose Lenght FLX"; Rec."Hose Length FLX")
            {
                ApplicationArea = All;
            }
            field("Piece FLX"; Rec."Piece FLX")
            {
                ApplicationArea = All;
            }
            field("Production Order No. FLX"; Rec."Production Order No. FLX")
            {
                ApplicationArea = All;
            }
            field("Production Order Status FLX"; Rec."Production Order Status FLX")
            {
                ApplicationArea = All;
            }
            field("Sell-to Customer Name FLX"; Rec."Sell-to Customer Name FLX")
            {
                ApplicationArea = All;
            }
            field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
            {
                ApplicationArea = All;
            }
            field("Your Reference FLX"; Rec."Your Reference FLX")
            {
                ApplicationArea = All;
            }
            field("Outstanding Qty. (Base) FLX"; Rec."Outstanding Qty. (Base)")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the quantity that is not yet shipped or invoiced.';
            }
            field("Qty. Shipped (Base) FLX"; Rec."Qty. Shipped (Base)")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the quantity that has been shipped.';
            }

        }
    }
}
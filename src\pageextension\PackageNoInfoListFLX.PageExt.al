pageextension 60002 "Package No. Info. List FLX" extends "Package No. Information List"
{
    layout
    {
        addlast(Control1)
        {
            field("Sales Order - Line No. FLX"; Rec."Sales Order - Line No. FLX")
            {
                ApplicationArea = All;
            }
            field("Combined Shipment No. FLX"; Rec."Combined Shipment No. FLX")
            {
                ApplicationArea = All;
            }
            field("Parent Combined Shpmt. No. FLX"; Rec."Parent Combined Shpmt. No. FLX")
            {
                ApplicationArea = All;
            }
            field("Old Package No. FLX"; Rec."Old Package No. FLX")
            {
                ApplicationArea = All;
            }
            field("Hose Lenght FLX"; Rec."Hose Lenght FLX")
            {
                ApplicationArea = All;
            }
            field("Production Order No. FLX"; Rec."Production Order No. FLX")
            {
                ApplicationArea = All;
            }
            field("Work Center No. FLX"; Rec."Work Center No. FLX")
            {
                ApplicationArea = All;
            }
            field("Sales Order Line No. FLX"; Rec."Sales Order Line No. FLX")
            {
                ApplicationArea = All;
            }
            field("Sell-to Customer Name FLX"; Rec."Sell-to Customer Name FLX")
            {
                ApplicationArea = All;
            }
            field("Ship-to Code FLX"; Rec."Ship-to Code FLX")
            {
                ApplicationArea = All;
            }
            field("Your Reference FLX"; Rec."Your Reference FLX")
            {
                ApplicationArea = All;
            }
            field("Quality Control Date FLX"; Rec."Quality Control Date FLX")
            {
                ApplicationArea = All;
            }
            field("Quality Control Status FLX"; Rec."Quality Control Status FLX")
            {
                ApplicationArea = All;
            }
            field("Reject Reason Code FLX"; Rec."Reject Reason Code FLX")
            {
                ApplicationArea = All;
            }
            field("Location Code FLX"; Rec."Location Code FLX")
            {
                ApplicationArea = All;
            }
            field("Lot No. FLX"; Rec."Lot No. FLX")
            {
                ApplicationArea = All;
            }
            field("Package Type FLX"; Rec."Package Type FLX")
            {
                ApplicationArea = All;
            }
            field("Parent Package No. FLX"; Rec."Parent Package No. FLX")
            {
                ApplicationArea = All;
            }
            field("Sales Order No. FLX"; Rec."Sales Order No. FLX")
            {
                ApplicationArea = All;
            }
            field("Sell-to Customer No. FLX"; Rec."Sell-to Customer No. FLX")
            {
                ApplicationArea = All;
            }
            field("Produced At FLX"; Rec."Produced At FLX")
            {
                ApplicationArea = All;
            }
            field("ID (mm) FLX"; Rec."ID (mm) FLX")
            {
                ApplicationArea = All;
            }
            field("BP (bar) FLX"; Rec."BP (bar) FLX")
            {
                ApplicationArea = All;
            }
            field("Barcode Text FLX"; Rec."Barcode Text FLX")
            {
                ApplicationArea = All;
            }
            field("Certificate Number FLX"; Rec."Certificate Number")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Certificate Number field.';
            }
            field("Child Package Count FLX"; Rec."Child Package Count FLX")
            {
                ApplicationArea = All;
            }
            field("Expired Inventory FLX"; Rec."Expired Inventory")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Expired Inventory field.';
            }
            field("Label Lenght FLX"; Rec."Label Lenght FLX")
            {
                ApplicationArea = All;
            }
            field("OD (mm) FLX"; Rec."OD (mm) FLX")
            {
                ApplicationArea = All;
            }
            field("Palette Item No. FLX"; Rec."Palette Item No. FLX")
            {
                ApplicationArea = All;
            }
            field("Production Order Line No. FLX"; Rec."Production Order Line No. FLX")
            {
                ApplicationArea = All;
            }
            field("Quality Controller ID FLX"; Rec."Quality Controller ID FLX")
            {
                ApplicationArea = All;
            }
            field("Remove Package FLX"; Rec."Remove Package FLX")
            {
                ApplicationArea = All;
            }
            field("Scaled Weight FLX"; Rec."Scaled Weight FLX")
            {
                ApplicationArea = All;
            }
            field("Shipment No FLX"; Rec."Shipment No FLX")
            {
                ApplicationArea = All;
            }

            field("WP (bar) FLX"; Rec."WP (bar) FLX")
            {
                ApplicationArea = All;
            }
            // field("Calculated Weight FLX"; FlexSalesMng.CalculatePackageWeight(Rec))
            // {
            //     ApplicationArea = All;
            //     Caption = 'Calculated Weight';
            //     Editable = false;
            //     ToolTip = 'Specifies the value of the Calculated Weight field.';
            // }
            field("SystemCreatedAt FLX"; Rec.SystemCreatedAt)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the SystemCreatedAt field.';
            }
        }
        modify(Inventory)
        {
            Visible = true;
        }
    }
    actions
    {
        addlast("&Package")
        {
            action("AddSelectedPackagesToBulk FLX")
            {
                ApplicationArea = All;
                Caption = 'Add Selected Packages To Bulk';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostApplication;
                ToolTip = 'Executes the Add Selected Packages To New Bulk action.';
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    FlexatiSalesManagement.CreateNewBulkAndInsertSelectedPackages(PackageNoInformation);
                end;
            }
            action("CreateNewPalette FLX")
            {
                ApplicationArea = All;
                Caption = 'Create New Palette';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewChartOfAccounts;
                PromotedOnly = true;
                ToolTip = 'Executes the Create New Palette action.';
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    FlexatiSalesManagement.CreateNewParentPackage(PackageNoInformation, Enum::"Package Type FLX"::Palette);

                    Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                end;
            }
            action("SetQualityControlStatusAccept FLX")
            {
                ApplicationArea = All;
                Caption = 'Set Quality Control Status Accept for Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Approve;
                ToolTip = 'Executes the Set Quality Control Status Accept for Selected Lines action.';
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    QualityControlManagement.SetQualityControlStatusAcceptForSelectedLines(PackageNoInformation);
                end;
            }
            action("TransferToKALITE FLX")
            {
                ApplicationArea = All;
                Caption = 'Transfer to Kalite';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = TransferToGeneralJournal;
                ToolTip = 'Executes the Transfer to Kalite action.';
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PackageTransMgt.CreatePackageTransferOrderForSelectedPackageNoInformationRecords(PackageNoInformation, 'KALITE');
                end;
            }
            action("TransferToFG FLX")
            {
                ApplicationArea = All;
                Caption = 'Transfer to FG';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = TransferReceipt;
                ToolTip = 'Executes the Transfer to FG action.';
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PackageTransMgt.CreatePackageTransferOrderForSelectedPackageNoInformationRecords(PackageNoInformation, 'FG');
                end;
            }
            action("PrintLabel FLX")
            {
                ApplicationArea = All;
                Caption = 'Print Label';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                Image = Print;
                ToolTip = 'Executes the Print Label action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    Report.Run(Report::"Customer Label FLX", true, true, PackageNoInformation);
                end;
            }
            action("PrintLabel Head FLX")
            {
                ApplicationArea = All;
                Caption = 'Print Label Head';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                Image = Print;
                ToolTip = 'Executes the Print Label Head action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    Report.Run(Report::"QR Label FLX", true, true, PackageNoInformation);
                end;
            }
        }
    }
    var
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
        PackageTransMgt: Codeunit "Package Trans. Mgt. FLX";
        //FlexSalesMng: Codeunit "Flexati Sales Management FLX";
        QualityControlManagement: Codeunit "Quality Control Management FLX";

    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
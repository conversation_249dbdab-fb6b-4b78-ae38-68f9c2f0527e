page 60017 "Package Transfer Orders FLX"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Orders';
    PageType = List;
    SourceTable = "Package Transfer Header FLX";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Package Transfer Order FLX";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                }
                field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                // field("Production Order No."; Rec."Production Order No.")
                // {
                //     ToolTip = 'Specifies the value of the Production Order No. field.';
                // }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(Posted; Rec.Posted)
                {
                    Editable = false;
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}
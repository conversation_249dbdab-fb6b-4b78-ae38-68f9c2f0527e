page 60003 "Start Production FLX"
{
    ApplicationArea = All;
    Caption = 'Start Production';
    PageType = StandardDialog;
    SourceTable = "Production Operation FLX";
    SourceTableTemporary = true;
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            group(StartProduction)
            {
                Caption = 'Start Production';
                field("User ID"; Rec."User ID")
                {
                    TableRelation = "Item Journal Batch".Name;
                }
                field("Production Line No."; Rec."Production Line No.")
                {
                    TableRelation = "Work Center-Prod. Line Mapping"."Production Line No.";
                }
                field("Work Center No."; Rec."Work Center No.")
                {
                    TableRelation = "Work Center"."No.";
                    Editable = false;
                }
                field("Label"; Rec."Package No.")
                {
                    TableRelation = "Package No. Information"."Package No.";
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Init();
        //Rec."User ID" := 'USER01';
        //Rec.Validate("Production Line No.", 'HAT01');
        Rec.Insert(false);
        //CurrPage.SetRecord(Rec);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        FlexatiProductionMngt.StartProductionProcess(Rec);
    end;

    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
pageextension 60012 "Item Card FLX" extends "Item Card"
{
    layout
    {
        addafter("Description 2")
        {
            field("Search Description FLX"; Rec."Search Description")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of Search Description field.';
            }
        }
        addafter("No.")
        {
            field("No. 2 FLX"; Rec."No. 2")
            {
                ToolTip = 'Specifies the value of the No. 2 field.';
                ApplicationArea = All;
            }
        }

        moveafter("Description 2"; Description)

        modify("Description 2")
        {
            Visible = true;
        }

        addlast(content)
        {
            group("Flexati FLX")
            {
                Caption = 'Flexati';
                field("Customer No. FLX"; Rec."Customer No. FLX")
                {
                    ApplicationArea = All;
                }

                field("Production Output Location FLX"; Rec."Production Output Location FLX")
                {
                    ApplicationArea = All;
                }
                field("Production Output Bin Code FLX"; Rec."Production Output Bin Code FLX")
                {
                    ApplicationArea = All;
                }
                field("ID (mm) FLX"; Rec."ID (mm) FLX")
                {
                    ApplicationArea = All;
                }
                field("OD (mm) FLX"; Rec."OD (mm) FLX")
                {
                    ApplicationArea = All;
                }
                field("WP (bar) FLX"; Rec."WP (bar) FLX")
                {
                    ApplicationArea = All;
                }
                field("BP (bar) FLX"; Rec."BP (bar) FLX")
                {
                    ApplicationArea = All;
                }
                field("Cat FLX"; Rec."Cat FLX")
                {
                    ApplicationArea = All;
                }
                field("Col FLX"; Rec."Col FLX")
                {
                    ApplicationArea = All;
                }
                field("Cover FLX"; Rec."Cover FLX")
                {
                    ApplicationArea = All;
                }
                field("F FLX"; Rec."F FLX")
                {
                    ApplicationArea = All;
                }
                field("LD FLX"; Rec."LD FLX")
                {
                    ApplicationArea = All;
                }
                field("Tube FLX"; Rec."Tube FLX")
                {
                    ApplicationArea = All;
                }
                field("Type FLX"; Rec."Type FLX")
                {
                    ApplicationArea = All;
                }
                field("Width FLX"; Rec."Width FLX")
                {
                    ApplicationArea = All;
                }
                field("Info FLX"; Rec."Info FLX")
                {
                    ApplicationArea = All;
                }
                field("Notes FLX"; Rec."Notes FLX")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                }
            }
        }
    }
}
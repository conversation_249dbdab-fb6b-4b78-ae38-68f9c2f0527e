﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-11-14T12:14:57.0118264Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b23244e3-9790-4712-be0f-6041ac5f0e69</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>20.32192cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.8596cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle1">
                          <ReportItems>
                            <Tablix Name="LinesTable">
                              <TablixBody>
                                <TablixColumns>
                                  <TablixColumn>
                                    <Width>1.80813cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>6.4cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.59687cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.61218cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.00487cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.2cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>1.70367cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>2.0962cm</Width>
                                  </TablixColumn>
                                  <TablixColumn>
                                    <Width>0.9cm</Width>
                                  </TablixColumn>
                                </TablixColumns>
                                <TablixRows>
                                  <TablixRow>
                                    <Height>1.07625cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="ItemNo_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Article Number</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>ItemNo_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Description_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Article Description</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Description_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>Hose Length</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox1</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Quantity_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Total Quantity</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Quantity_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitOfMeasure_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Unit</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitOfMeasure_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox4">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>Coils</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox4</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox7">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>Planned Shipment Date</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox7</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitPrice_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value EvaluationMode="Constant">Unit Price</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitPrice_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="LineAmount_Line_Lbl">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>Net Amount</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LineAmount_Line_Lbl</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox58">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <FontWeight>Bold</FontWeight>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox58</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <BottomBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </BottomBorder>
                                              <VerticalAlign>Bottom</VerticalAlign>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.38806cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="ItemNo_Line">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!ItemNo_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>ItemNo_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Description_Line_2">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Description_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Description_Line_2</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox3">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Hoselength_line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox2</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Quantity_Line">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Quantity_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Quantity_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitOfMeasure">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitOfMeasure.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitOfMeasure</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox5">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!Piece_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox5</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox9">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!PlannedShipmentDate_Line_Text.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox9</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="UnitPrice">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!UnitPrice.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n4</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>UnitPrice</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="LineAmount_Line">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!LineAmount_Line.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                      <Format>n2</Format>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Right</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>LineAmount_Line</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox60">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!CurrencyCode.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>7pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Left</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox60</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <TopBorder>
                                                <Color>Black</Color>
                                                <Style>Solid</Style>
                                                <Width>1pt</Width>
                                              </TopBorder>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                    </TablixCells>
                                  </TablixRow>
                                  <TablixRow>
                                    <Height>0.38806cm</Height>
                                    <TablixCells>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox24">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox24</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell>
                                        <CellContents>
                                          <Textbox Name="Textbox25">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value>=Fields!marka.Value</Value>
                                                    <Style>
                                                      <FontFamily>Segoe UI</FontFamily>
                                                      <FontSize>8pt</FontSize>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style />
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox25</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Color>LightGrey</Color>
                                                <Style>None</Style>
                                              </Border>
                                              <PaddingLeft>5pt</PaddingLeft>
                                              <PaddingRight>5pt</PaddingRight>
                                            </Style>
                                          </Textbox>
                                          <ColSpan>9</ColSpan>
                                        </CellContents>
                                      </TablixCell>
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                      <TablixCell />
                                    </TablixCells>
                                  </TablixRow>
                                </TablixRows>
                              </TablixBody>
                              <TablixColumnHierarchy>
                                <TablixMembers>
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                  <TablixMember />
                                </TablixMembers>
                              </TablixColumnHierarchy>
                              <TablixRowHierarchy>
                                <TablixMembers>
                                  <TablixMember>
                                    <KeepWithGroup>After</KeepWithGroup>
                                    <RepeatOnNewPage>true</RepeatOnNewPage>
                                  </TablixMember>
                                  <TablixMember>
                                    <Group Name="LineNo" />
                                    <SortExpressions>
                                      <SortExpression>
                                        <Value>=Fields!LineNo_Line.Value</Value>
                                      </SortExpression>
                                    </SortExpressions>
                                    <TablixMembers>
                                      <TablixMember>
                                        <Visibility>
                                          <Hidden>=Fields!Quantity_Line.Value=0</Hidden>
                                        </Visibility>
                                      </TablixMember>
                                      <TablixMember>
                                        <Visibility>
                                          <Hidden>=Fields!marka.Value = ""</Hidden>
                                        </Visibility>
                                      </TablixMember>
                                    </TablixMembers>
                                  </TablixMember>
                                </TablixMembers>
                              </TablixRowHierarchy>
                              <RepeatRowHeaders>true</RepeatRowHeaders>
                              <DataSetName>DataSet_Result</DataSetName>
                              <Top>0.00362cm</Top>
                              <Height>1.85237cm</Height>
                              <Width>20.32192cm</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                              </Style>
                            </Tablix>
                          </ReportItems>
                          <PageBreak>
                            <BreakLocation>End</BreakLocation>
                          </PageBreak>
                          <KeepTogether>true</KeepTogether>
                          <OmitBorderOnPageBreak>true</OmitBorderOnPageBreak>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>5.89167cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Tablix Name="LinesTableTotals">
                          <TablixBody>
                            <TablixColumns>
                              <TablixColumn>
                                <Width>9.8382cm</Width>
                              </TablixColumn>
                              <TablixColumn>
                                <Width>1.82672cm</Width>
                              </TablixColumn>
                              <TablixColumn>
                                <Width>3.80129cm</Width>
                              </TablixColumn>
                              <TablixColumn>
                                <Width>0.37088cm</Width>
                              </TablixColumn>
                              <TablixColumn>
                                <Width>0.78445cm</Width>
                              </TablixColumn>
                              <TablixColumn>
                                <Width>2.80038cm</Width>
                              </TablixColumn>
                              <TablixColumn>
                                <Width>0.9cm</Width>
                              </TablixColumn>
                            </TablixColumns>
                            <TablixRows>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox75">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox75</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox76">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox76</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox90">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox90</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox91">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox91</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox92">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox92</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox93">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox93</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox94">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox94</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Subtotal_Lbl2">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value EvaluationMode="Constant">Total Quantity</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Subtotal_Lbl</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox8">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalQuantity.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                  <Format>n2</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox7</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Subtotal_Lbl">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value EvaluationMode="Constant">Total Amount</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Subtotal_Lbl</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalSubTotal">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalSubTotal.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                  <Format>=Last(Fields!TotalSubTotalFormat.Value)</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalSubTotal</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox71">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=First(Fields!CurrencyCode.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>7pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Left</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <rd:Selected>true</rd:Selected>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox16">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox16</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox10">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox8</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="InvoiceDiscountAmount_Lbl">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!InvoiceDiscountAmount_Lbl.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>InvoiceDiscountAmount_Lbl</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalInvoiceDiscountAmount">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalInvoiceDiscountAmount.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <Format>=Last(Fields!TotalInvoiceDiscountAmountFormat.Value)</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalInvoiceDiscountAmount</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox62">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!CurrencyCode.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox17">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox17</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox11">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox9</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalExcludingVATText">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalExcludingVATText.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalExcludingVATText</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalSubTotalMinusInvoiceDiscount">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalSubTotalMinusInvoiceDiscount.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                  <Format>=Last(Fields!TotalNetAmountFormat.Value)</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalSubTotalMinusInvoiceDiscount</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox63">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!CurrencyCode.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox18">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox18</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox12">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox10</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="VATAmount_Lbl_2">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!VATAmount_Lbl.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>VATAmount_Lbl_2</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalVATAmount">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalVATAmount.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <Format>=Last(Fields!TotalVATAmountFormat.Value)</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalVATAmount</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox64">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!CurrencyCode.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox19">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox19</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox13">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox11</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalIncludingVATText">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalIncludingVATText.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalIncludingVATText</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalAmountIncludingVAT">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalAmountIncludingVAT.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalAmountIncludingVAT</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <TopBorder>
                                            <Style>Solid</Style>
                                          </TopBorder>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox69">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!CurrencyCode.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.27367cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox20">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox20</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox14">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox12</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox50">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox50</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox51">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox51</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox52">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox52</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox53">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox53</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <TopBorder>
                                            <Style>Solid</Style>
                                          </TopBorder>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox72">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox67</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <TopBorder>
                                            <Style>Solid</Style>
                                          </TopBorder>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox21">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox21</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox22">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox13</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="VATAmount_Lbl_3">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!VATAmount_Lbl.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>VATAmount_Lbl_3</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalVATAmount_2">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalVATAmount.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <Format>=Last(Fields!TotalVATAmountFormat.Value)</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalVATAmount_2</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox73">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!CurrencyCode.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox23">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox22</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox29">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox14</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalExcludingVATText_2">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalExcludingVATText.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalExcludingVATText_2</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="TotalNetAmount">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Last(Fields!TotalNetAmount.Value)</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                  <Format>=Last(Fields!TotalNetAmountFormat.Value)</Format>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>TotalNetAmount</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox74">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value>=Fields!CurrencyCode.Value</Value>
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox60</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.16757cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox96">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox95</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox97">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox96</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox98">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox97</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox99">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox98</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox100">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox99</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox101">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox100</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox102">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox101</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.48507cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox30">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value EvaluationMode="Constant">LATEST CANCELLATION / AMENDMENT DATE:</Value>
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox15</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox103">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox103</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox104">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox104</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox105">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox105</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox106">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox106</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox107">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox107</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox108">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox108</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.64382cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox32">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value EvaluationMode="Constant">*** Please check the order confirmation carefully for possible discrepancies to avoid any mistake during production !!</Value>
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox26</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                      <ColSpan>3</ColSpan>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell />
                                  <TablixCell />
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox113">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox113</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox114">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox114</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox115">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox115</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox116">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox116</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox33">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value EvaluationMode="Constant">Catalogue link:</Value>
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox27</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox119">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox119</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox120">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox120</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox121">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox121</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox122">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox122</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox123">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox123</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox124">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox124</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                              <TablixRow>
                                <Height>0.43215cm</Height>
                                <TablixCells>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox36">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value EvaluationMode="Constant">Data Sheet link:</Value>
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox28</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox127">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox127</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox129">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox128</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox130">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox129</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox131">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style />
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox130</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox132">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                  <FontWeight>Bold</FontWeight>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox131</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                  <TablixCell>
                                    <CellContents>
                                      <Textbox Name="Textbox133">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontFamily>Segoe UI</FontFamily>
                                                  <FontSize>8pt</FontSize>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Right</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox132</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Color>LightGrey</Color>
                                            <Style>None</Style>
                                          </Border>
                                          <PaddingLeft>5pt</PaddingLeft>
                                          <PaddingRight>5pt</PaddingRight>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixCell>
                                </TablixCells>
                              </TablixRow>
                            </TablixRows>
                          </TablixBody>
                          <TablixColumnHierarchy>
                            <TablixMembers>
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                            </TablixMembers>
                          </TablixColumnHierarchy>
                          <TablixRowHierarchy>
                            <TablixMembers>
                              <TablixMember />
                              <TablixMember />
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember>
                                <Visibility>
                                  <Hidden>true</Hidden>
                                </Visibility>
                              </TablixMember>
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                              <TablixMember />
                            </TablixMembers>
                          </TablixRowHierarchy>
                          <RepeatRowHeaders>true</RepeatRowHeaders>
                          <KeepTogether>true</KeepTogether>
                          <DataSetName>DataSet_Result</DataSetName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Tablix>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Header">
                    <GroupExpressions>
                      <GroupExpression>=Fields!DocumentNo.Value</GroupExpression>
                    </GroupExpressions>
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                      <ResetPageNumber>true</ResetPageNumber>
                    </PageBreak>
                  </Group>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.22049cm</Top>
            <Left>0.00001cm</Left>
            <Height>7.75127cm</Height>
            <Width>20.32192cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>7.97176cm</Height>
        <Style />
      </Body>
      <Width>20.32193cm</Width>
      <Page>
        <PageHeader>
          <Height>10.11824cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Rectangle Name="Rectangle2">
              <ReportItems>
                <Image Name="Image2">
                  <Source>Database</Source>
                  <Value>=System.Convert.ToBase64String(Fields!CompanyPicture.Value)</Value>
                  <MIMEType>image/bmp</MIMEType>
                  <Sizing>FitProportional</Sizing>
                  <Height>20.08542mm</Height>
                  <Width>60mm</Width>
                  <Visibility>
                    <Hidden>=iif(First(Fields!CompanyLogoPosition.Value, "DataSet_Result")=1,false,true)</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                  </Style>
                </Image>
                <Line Name="Line1">
                  <Height>0cm</Height>
                  <Width>6cm</Width>
                  <ZIndex>1</ZIndex>
                  <Visibility>
                    <Hidden>true</Hidden>
                  </Visibility>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                  </Style>
                </Line>
              </ReportItems>
              <KeepTogether>true</KeepTogether>
              <Left>0.07215cm</Left>
              <Height>2.00854cm</Height>
              <Width>6cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Rectangle>
            <Textbox Name="Page_Lbl">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!Page_Lbl.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value xml:space="preserve"> </Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Globals!PageNumber</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> / </Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Globals!TotalPages</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Page_Lbl</rd:DefaultName>
              <Left>15.02008cm</Left>
              <Height>11pt</Height>
              <Width>4.9cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress9">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">To</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>4.32033cm</Top>
              <Left>0.097cm</Left>
              <Height>0.38806cm</Height>
              <Width>1.01488cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CustomerAddress1.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>4.32033cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>7.51943cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress11">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CustomerAddress2.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress2</rd:DefaultName>
              <Top>4.77894cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>7.51943cm</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress12">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CustomerAddress3.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress3</rd:DefaultName>
              <Top>5.20228cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>7.51943cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress13">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CustomerAddress4.Value+" "+Fields!CustomerAddress5.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress4</rd:DefaultName>
              <Top>5.59034cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>7.51943cm</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress14">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">From</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>2.18493cm</Top>
              <Left>0.03947cm</Left>
              <Height>0.38806cm</Height>
              <Width>1.19588cm</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CompanyAddress7">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyAddress1.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyAddress1</rd:DefaultName>
              <Top>2.18493cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>8.7408cm</Width>
              <ZIndex>8</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CompanyAddress8">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyAddress2.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyAddress2</rd:DefaultName>
              <Top>2.64355cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>8.7408cm</Width>
              <ZIndex>9</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CompanyAddress9">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyAddress3.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyAddress3</rd:DefaultName>
              <Top>3.10216cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>8.7408cm</Width>
              <ZIndex>10</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CompanyAddress10">
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyAddress4.Value+" "+Fields!CompanyAddress5.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyAddress4</rd:DefaultName>
              <Top>3.56078cm</Top>
              <Left>1.28827cm</Left>
              <Height>0.38806cm</Height>
              <Width>8.7408cm</Width>
              <ZIndex>11</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress15">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Ship To</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>4.32033cm</Top>
              <Left>12.08251cm</Left>
              <Height>0.38806cm</Height>
              <Width>1.74111cm</Width>
              <ZIndex>12</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress16">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!ShipToAddress1.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>4.32033cm</Top>
              <Left>14.00001cm</Left>
              <Height>0.38806cm</Height>
              <Width>5.92007cm</Width>
              <ZIndex>13</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress17">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!ShipToAddress2.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>4.77894cm</Top>
              <Left>14.00001cm</Left>
              <Height>0.38806cm</Height>
              <Width>5.92007cm</Width>
              <ZIndex>14</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress18">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!ShipToAddress3.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>5.20228cm</Top>
              <Left>14.00001cm</Left>
              <Height>0.38806cm</Height>
              <Width>5.92007cm</Width>
              <ZIndex>15</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress19">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!ShipToAddress4.Value, "DataSet_Result")+" "+First(Fields!ShipToAddress5.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>5.59034cm</Top>
              <Left>14.00001cm</Left>
              <Height>0.38806cm</Height>
              <Width>5.92007cm</Width>
              <ZIndex>16</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="Invoice_Lbl2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Order Confirmation</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>14pt</FontSize>
                        <FontWeight>SemiBold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Invoice_Lbl</rd:DefaultName>
              <Top>6.15479cm</Top>
              <Left>3.69335cm</Left>
              <Height>0.76298cm</Height>
              <Width>11.50666cm</Width>
              <ZIndex>17</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!DocumentNo.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>7.54825cm</Top>
              <Left>3.87391cm</Left>
              <Height>0.38806cm</Height>
              <Width>4.93379cm</Width>
              <ZIndex>18</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress20">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Our Order Number</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>7.54825cm</Top>
              <Left>0.07215cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.73121cm</Width>
              <ZIndex>19</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!YourReference.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>8.00686cm</Top>
              <Left>3.87391cm</Left>
              <Height>0.38806cm</Height>
              <Width>4.93379cm</Width>
              <ZIndex>20</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress21">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Your Order Number</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>8.00686cm</Top>
              <Left>0.07215cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.73121cm</Width>
              <ZIndex>21</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo4">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!SelltoCustomerNo.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>7.16019cm</Top>
              <Left>3.87392cm</Left>
              <Height>0.38806cm</Height>
              <Width>4.93378cm</Width>
              <ZIndex>22</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress22">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Company Code</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>7.16019cm</Top>
              <Left>0.07216cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.7312cm</Width>
              <ZIndex>23</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo5">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!OrderDate.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>8.4302cm</Top>
              <Left>3.87391cm</Left>
              <Height>0.38806cm</Height>
              <Width>4.93379cm</Width>
              <ZIndex>24</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress23">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Order Date</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>8.4302cm</Top>
              <Left>0.07215cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.73121cm</Width>
              <ZIndex>25</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo6">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!PaymentTermsDescription.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>7.53121cm</Top>
              <Left>14.54434cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.82609cm</Width>
              <ZIndex>26</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress24">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Payment Terms</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>7.53121cm</Top>
              <Left>11.20001cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.30905cm</Width>
              <ZIndex>27</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo7">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant" />
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>7.98982cm</Top>
              <Left>14.54434cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.82609cm</Width>
              <ZIndex>28</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress25">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Transit Time</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>7.98982cm</Top>
              <Left>11.20001cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.30905cm</Width>
              <ZIndex>29</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo8">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!ShipmentMethodDescription.Value</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>7.14315cm</Top>
              <Left>14.54435cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.82609cm</Width>
              <ZIndex>30</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress26">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Delivery Terms</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>7.14315cm</Top>
              <Left>11.20002cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.30905cm</Width>
              <ZIndex>31</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="DocumentNo9">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PrintDate.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>DocumentNo</rd:DefaultName>
              <Top>8.41316cm</Top>
              <Left>14.54434cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.82609cm</Width>
              <ZIndex>32</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress27">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Print Date</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>8.41316cm</Top>
              <Left>11.20001cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.30905cm</Width>
              <ZIndex>33</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress28">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value EvaluationMode="Constant">Supplier Number:</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>2.18493cm</Top>
              <Left>12.08251cm</Left>
              <Height>0.38806cm</Height>
              <Width>3.1175cm</Width>
              <ZIndex>34</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
            <Textbox Name="CustomerAddress29">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!SupplierNumber.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontFamily>Segoe UI</FontFamily>
                        <FontSize>9pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CustomerAddress1</rd:DefaultName>
              <Top>2.18493cm</Top>
              <Left>15.20001cm</Left>
              <Height>0.38806cm</Height>
              <Width>4.72007cm</Width>
              <ZIndex>35</ZIndex>
              <Style>
                <Border>
                  <Color>LightGrey</Color>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>5pt</PaddingLeft>
                <PaddingRight>5pt</PaddingRight>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0in</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="SupplierNumber">
          <DataField>SupplierNumber</DataField>
        </Field>
        <Field Name="CompanyAddress1">
          <DataField>CompanyAddress1</DataField>
        </Field>
        <Field Name="CompanyAddress2">
          <DataField>CompanyAddress2</DataField>
        </Field>
        <Field Name="CompanyAddress3">
          <DataField>CompanyAddress3</DataField>
        </Field>
        <Field Name="CompanyAddress4">
          <DataField>CompanyAddress4</DataField>
        </Field>
        <Field Name="CompanyAddress5">
          <DataField>CompanyAddress5</DataField>
        </Field>
        <Field Name="CompanyAddress6">
          <DataField>CompanyAddress6</DataField>
        </Field>
        <Field Name="CompanyAddress7">
          <DataField>CompanyAddress7</DataField>
        </Field>
        <Field Name="CompanyAddress8">
          <DataField>CompanyAddress8</DataField>
        </Field>
        <Field Name="CompanyHomePage">
          <DataField>CompanyHomePage</DataField>
        </Field>
        <Field Name="CompanyEMail">
          <DataField>CompanyEMail</DataField>
        </Field>
        <Field Name="CompanyPicture">
          <DataField>CompanyPicture</DataField>
        </Field>
        <Field Name="CompanyPhoneNo">
          <DataField>CompanyPhoneNo</DataField>
        </Field>
        <Field Name="CompanyPhoneNo_Lbl">
          <DataField>CompanyPhoneNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyGiroNo">
          <DataField>CompanyGiroNo</DataField>
        </Field>
        <Field Name="CompanyGiroNo_Lbl">
          <DataField>CompanyGiroNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyBankName">
          <DataField>CompanyBankName</DataField>
        </Field>
        <Field Name="CompanyBankName_Lbl">
          <DataField>CompanyBankName_Lbl</DataField>
        </Field>
        <Field Name="CompanyBankBranchNo">
          <DataField>CompanyBankBranchNo</DataField>
        </Field>
        <Field Name="CompanyBankBranchNo_Lbl">
          <DataField>CompanyBankBranchNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyBankAccountNo">
          <DataField>CompanyBankAccountNo</DataField>
        </Field>
        <Field Name="CompanyBankAccountNo_Lbl">
          <DataField>CompanyBankAccountNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyIBAN">
          <DataField>CompanyIBAN</DataField>
        </Field>
        <Field Name="CompanyIBAN_Lbl">
          <DataField>CompanyIBAN_Lbl</DataField>
        </Field>
        <Field Name="CompanySWIFT">
          <DataField>CompanySWIFT</DataField>
        </Field>
        <Field Name="CompanySWIFT_Lbl">
          <DataField>CompanySWIFT_Lbl</DataField>
        </Field>
        <Field Name="CompanyLogoPosition">
          <DataField>CompanyLogoPosition</DataField>
        </Field>
        <Field Name="CompanyRegistrationNumber">
          <DataField>CompanyRegistrationNumber</DataField>
        </Field>
        <Field Name="CompanyRegistrationNumber_Lbl">
          <DataField>CompanyRegistrationNumber_Lbl</DataField>
        </Field>
        <Field Name="CompanyVATRegNo">
          <DataField>CompanyVATRegNo</DataField>
        </Field>
        <Field Name="CompanyVATRegNo_Lbl">
          <DataField>CompanyVATRegNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyVATRegistrationNo">
          <DataField>CompanyVATRegistrationNo</DataField>
        </Field>
        <Field Name="CompanyVATRegistrationNo_Lbl">
          <DataField>CompanyVATRegistrationNo_Lbl</DataField>
        </Field>
        <Field Name="CompanyLegalOffice">
          <DataField>CompanyLegalOffice</DataField>
        </Field>
        <Field Name="CompanyLegalOffice_Lbl">
          <DataField>CompanyLegalOffice_Lbl</DataField>
        </Field>
        <Field Name="CompanyCustomGiro">
          <DataField>CompanyCustomGiro</DataField>
        </Field>
        <Field Name="CompanyCustomGiro_Lbl">
          <DataField>CompanyCustomGiro_Lbl</DataField>
        </Field>
        <Field Name="CompanyLegalStatement">
          <DataField>CompanyLegalStatement</DataField>
        </Field>
        <Field Name="CustomerAddress1">
          <DataField>CustomerAddress1</DataField>
        </Field>
        <Field Name="CustomerAddress2">
          <DataField>CustomerAddress2</DataField>
        </Field>
        <Field Name="CustomerAddress3">
          <DataField>CustomerAddress3</DataField>
        </Field>
        <Field Name="CustomerAddress4">
          <DataField>CustomerAddress4</DataField>
        </Field>
        <Field Name="CustomerAddress5">
          <DataField>CustomerAddress5</DataField>
        </Field>
        <Field Name="CustomerAddress6">
          <DataField>CustomerAddress6</DataField>
        </Field>
        <Field Name="CustomerAddress7">
          <DataField>CustomerAddress7</DataField>
        </Field>
        <Field Name="CustomerAddress8">
          <DataField>CustomerAddress8</DataField>
        </Field>
        <Field Name="SellToContactPhoneNoLbl">
          <DataField>SellToContactPhoneNoLbl</DataField>
        </Field>
        <Field Name="SellToContactMobilePhoneNoLbl">
          <DataField>SellToContactMobilePhoneNoLbl</DataField>
        </Field>
        <Field Name="SellToContactEmailLbl">
          <DataField>SellToContactEmailLbl</DataField>
        </Field>
        <Field Name="BillToContactPhoneNoLbl">
          <DataField>BillToContactPhoneNoLbl</DataField>
        </Field>
        <Field Name="BillToContactMobilePhoneNoLbl">
          <DataField>BillToContactMobilePhoneNoLbl</DataField>
        </Field>
        <Field Name="BillToContactEmailLbl">
          <DataField>BillToContactEmailLbl</DataField>
        </Field>
        <Field Name="SellToContactPhoneNo">
          <DataField>SellToContactPhoneNo</DataField>
        </Field>
        <Field Name="SellToContactMobilePhoneNo">
          <DataField>SellToContactMobilePhoneNo</DataField>
        </Field>
        <Field Name="SellToContactEmail">
          <DataField>SellToContactEmail</DataField>
        </Field>
        <Field Name="BillToContactPhoneNo">
          <DataField>BillToContactPhoneNo</DataField>
        </Field>
        <Field Name="BillToContactMobilePhoneNo">
          <DataField>BillToContactMobilePhoneNo</DataField>
        </Field>
        <Field Name="BillToContactEmail">
          <DataField>BillToContactEmail</DataField>
        </Field>
        <Field Name="CustomerPostalBarCode">
          <DataField>CustomerPostalBarCode</DataField>
        </Field>
        <Field Name="YourReference">
          <DataField>YourReference</DataField>
        </Field>
        <Field Name="YourReference_Lbl">
          <DataField>YourReference_Lbl</DataField>
        </Field>
        <Field Name="CurrencyCode">
          <DataField>CurrencyCode</DataField>
        </Field>
        <Field Name="CurrencySymbol">
          <DataField>CurrencySymbol</DataField>
        </Field>
        <Field Name="ShipmentMethodDescription">
          <DataField>ShipmentMethodDescription</DataField>
        </Field>
        <Field Name="ShipmentMethodDescription_Lbl">
          <DataField>ShipmentMethodDescription_Lbl</DataField>
        </Field>
        <Field Name="Shipment_Lbl">
          <DataField>Shipment_Lbl</DataField>
        </Field>
        <Field Name="ShipmentDate">
          <DataField>ShipmentDate</DataField>
        </Field>
        <Field Name="ShipmentDate_Lbl">
          <DataField>ShipmentDate_Lbl</DataField>
        </Field>
        <Field Name="ShowShippingAddress">
          <DataField>ShowShippingAddress</DataField>
        </Field>
        <Field Name="ShipToAddress_Lbl">
          <DataField>ShipToAddress_Lbl</DataField>
        </Field>
        <Field Name="ShipToAddress1">
          <DataField>ShipToAddress1</DataField>
        </Field>
        <Field Name="ShipToAddress2">
          <DataField>ShipToAddress2</DataField>
        </Field>
        <Field Name="ShipToAddress3">
          <DataField>ShipToAddress3</DataField>
        </Field>
        <Field Name="ShipToAddress4">
          <DataField>ShipToAddress4</DataField>
        </Field>
        <Field Name="ShipToAddress5">
          <DataField>ShipToAddress5</DataField>
        </Field>
        <Field Name="ShipToAddress6">
          <DataField>ShipToAddress6</DataField>
        </Field>
        <Field Name="ShipToAddress7">
          <DataField>ShipToAddress7</DataField>
        </Field>
        <Field Name="ShipToAddress8">
          <DataField>ShipToAddress8</DataField>
        </Field>
        <Field Name="PaymentTermsDescription">
          <DataField>PaymentTermsDescription</DataField>
        </Field>
        <Field Name="PaymentTermsDescription_Lbl">
          <DataField>PaymentTermsDescription_Lbl</DataField>
        </Field>
        <Field Name="PaymentMethodDescription">
          <DataField>PaymentMethodDescription</DataField>
        </Field>
        <Field Name="PaymentMethodDescription_Lbl">
          <DataField>PaymentMethodDescription_Lbl</DataField>
        </Field>
        <Field Name="DocumentCopyText">
          <DataField>DocumentCopyText</DataField>
        </Field>
        <Field Name="BilltoCustumerNo">
          <DataField>BilltoCustumerNo</DataField>
        </Field>
        <Field Name="BilltoCustomerNo_Lbl">
          <DataField>BilltoCustomerNo_Lbl</DataField>
        </Field>
        <Field Name="DocumentDate">
          <DataField>DocumentDate</DataField>
        </Field>
        <Field Name="OrderDate">
          <DataField>OrderDate</DataField>
        </Field>
        <Field Name="PrintDate">
          <DataField>PrintDate</DataField>
        </Field>
        <Field Name="DocumentDate_Lbl">
          <DataField>DocumentDate_Lbl</DataField>
        </Field>
        <Field Name="DueDate">
          <DataField>DueDate</DataField>
        </Field>
        <Field Name="DueDate_Lbl">
          <DataField>DueDate_Lbl</DataField>
        </Field>
        <Field Name="DocumentNo">
          <DataField>DocumentNo</DataField>
        </Field>
        <Field Name="DocumentNo_Lbl">
          <DataField>DocumentNo_Lbl</DataField>
        </Field>
        <Field Name="QuoteNo">
          <DataField>QuoteNo</DataField>
        </Field>
        <Field Name="QuoteNo_Lbl">
          <DataField>QuoteNo_Lbl</DataField>
        </Field>
        <Field Name="PricesIncludingVAT">
          <DataField>PricesIncludingVAT</DataField>
        </Field>
        <Field Name="PricesIncludingVAT_Lbl">
          <DataField>PricesIncludingVAT_Lbl</DataField>
        </Field>
        <Field Name="PricesIncludingVATYesNo">
          <DataField>PricesIncludingVATYesNo</DataField>
        </Field>
        <Field Name="SalesPerson_Lbl">
          <DataField>SalesPerson_Lbl</DataField>
        </Field>
        <Field Name="SalesPersonText_Lbl">
          <DataField>SalesPersonText_Lbl</DataField>
        </Field>
        <Field Name="SalesPersonName">
          <DataField>SalesPersonName</DataField>
        </Field>
        <Field Name="SelltoCustomerNo">
          <DataField>SelltoCustomerNo</DataField>
        </Field>
        <Field Name="SelltoCustomerNo_Lbl">
          <DataField>SelltoCustomerNo_Lbl</DataField>
        </Field>
        <Field Name="VATRegistrationNo">
          <DataField>VATRegistrationNo</DataField>
        </Field>
        <Field Name="VATRegistrationNo_Lbl">
          <DataField>VATRegistrationNo_Lbl</DataField>
        </Field>
        <Field Name="SellToFaxNo">
          <DataField>SellToFaxNo</DataField>
        </Field>
        <Field Name="SellToPhoneNo">
          <DataField>SellToPhoneNo</DataField>
        </Field>
        <Field Name="LegalEntityType">
          <DataField>LegalEntityType</DataField>
        </Field>
        <Field Name="LegalEntityType_Lbl">
          <DataField>LegalEntityType_Lbl</DataField>
        </Field>
        <Field Name="Copy_Lbl">
          <DataField>Copy_Lbl</DataField>
        </Field>
        <Field Name="EMail_Lbl">
          <DataField>EMail_Lbl</DataField>
        </Field>
        <Field Name="HomePage_Lbl">
          <DataField>HomePage_Lbl</DataField>
        </Field>
        <Field Name="InvoiceDiscountBaseAmount_Lbl">
          <DataField>InvoiceDiscountBaseAmount_Lbl</DataField>
        </Field>
        <Field Name="InvoiceDiscountAmount_Lbl">
          <DataField>InvoiceDiscountAmount_Lbl</DataField>
        </Field>
        <Field Name="LineAmountAfterInvoiceDiscount_Lbl">
          <DataField>LineAmountAfterInvoiceDiscount_Lbl</DataField>
        </Field>
        <Field Name="LocalCurrency_Lbl">
          <DataField>LocalCurrency_Lbl</DataField>
        </Field>
        <Field Name="ExchangeRateAsText">
          <DataField>ExchangeRateAsText</DataField>
        </Field>
        <Field Name="Page_Lbl">
          <DataField>Page_Lbl</DataField>
        </Field>
        <Field Name="SalesInvoiceLineDiscount_Lbl">
          <DataField>SalesInvoiceLineDiscount_Lbl</DataField>
        </Field>
        <Field Name="Invoice_Lbl">
          <DataField>Invoice_Lbl</DataField>
        </Field>
        <Field Name="Subtotal_Lbl">
          <DataField>Subtotal_Lbl</DataField>
        </Field>
        <Field Name="Total_Lbl">
          <DataField>Total_Lbl</DataField>
        </Field>
        <Field Name="VATAmount_Lbl">
          <DataField>VATAmount_Lbl</DataField>
        </Field>
        <Field Name="VATBase_Lbl">
          <DataField>VATBase_Lbl</DataField>
        </Field>
        <Field Name="VATAmountSpecification_Lbl">
          <DataField>VATAmountSpecification_Lbl</DataField>
        </Field>
        <Field Name="VATClauses_Lbl">
          <DataField>VATClauses_Lbl</DataField>
        </Field>
        <Field Name="VATIdentifier_Lbl">
          <DataField>VATIdentifier_Lbl</DataField>
        </Field>
        <Field Name="VATPercentage_Lbl">
          <DataField>VATPercentage_Lbl</DataField>
        </Field>
        <Field Name="VATClause_Lbl">
          <DataField>VATClause_Lbl</DataField>
        </Field>
        <Field Name="ExtDocNo_SalesHeader">
          <DataField>ExtDocNo_SalesHeader</DataField>
        </Field>
        <Field Name="ExtDocNo_SalesHeader_Lbl">
          <DataField>ExtDocNo_SalesHeader_Lbl</DataField>
        </Field>
        <Field Name="ShowWorkDescription">
          <DataField>ShowWorkDescription</DataField>
        </Field>
        <Field Name="LineNo_Line">
          <DataField>LineNo_Line</DataField>
        </Field>
        <Field Name="Hoselength_line">
          <DataField>Hoselength_line</DataField>
        </Field>
        <Field Name="Hoselength_lineFormat">
          <DataField>Hoselength_lineFormat</DataField>
        </Field>
        <Field Name="Piece_Line">
          <DataField>Piece_Line</DataField>
        </Field>
        <Field Name="marka">
          <DataField>marka</DataField>
        </Field>
        <Field Name="AmountExcludingVAT_Line">
          <DataField>AmountExcludingVAT_Line</DataField>
        </Field>
        <Field Name="AmountExcludingVAT_LineFormat">
          <DataField>AmountExcludingVAT_LineFormat</DataField>
        </Field>
        <Field Name="AmountExcludingVAT_Line_Lbl">
          <DataField>AmountExcludingVAT_Line_Lbl</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_Line">
          <DataField>AmountIncludingVAT_Line</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_LineFormat">
          <DataField>AmountIncludingVAT_LineFormat</DataField>
        </Field>
        <Field Name="AmountIncludingVAT_Line_Lbl">
          <DataField>AmountIncludingVAT_Line_Lbl</DataField>
        </Field>
        <Field Name="Description_Line">
          <DataField>Description_Line</DataField>
        </Field>
        <Field Name="Description_Line_Lbl">
          <DataField>Description_Line_Lbl</DataField>
        </Field>
        <Field Name="LineDiscountPercent_Line">
          <DataField>LineDiscountPercent_Line</DataField>
        </Field>
        <Field Name="LineDiscountPercent_LineFormat">
          <DataField>LineDiscountPercent_LineFormat</DataField>
        </Field>
        <Field Name="LineDiscountPercentText_Line">
          <DataField>LineDiscountPercentText_Line</DataField>
        </Field>
        <Field Name="LineAmount_Line">
          <DataField>LineAmount_Line</DataField>
        </Field>
        <Field Name="LineAmount_Line_Lbl">
          <DataField>LineAmount_Line_Lbl</DataField>
        </Field>
        <Field Name="ItemNo_Line">
          <DataField>ItemNo_Line</DataField>
        </Field>
        <Field Name="ItemNo_Line_Lbl">
          <DataField>ItemNo_Line_Lbl</DataField>
        </Field>
        <Field Name="ShipmentDate_Line">
          <DataField>ShipmentDate_Line</DataField>
        </Field>
        <Field Name="ShipmentDate_Line_Lbl">
          <DataField>ShipmentDate_Line_Lbl</DataField>
        </Field>
        <Field Name="PlannedShipmentDate_Line">
          <DataField>PlannedShipmentDate_Line</DataField>
        </Field>
        <Field Name="PlannedShipmentDate_Line_Lbl">
          <DataField>PlannedShipmentDate_Line_Lbl</DataField>
        </Field>
        <Field Name="PlannedShipmentDate_Line_Text">
          <DataField>PlannedShipmentDate_Line_Text</DataField>
        </Field>
        <Field Name="Quantity_Line">
          <DataField>Quantity_Line</DataField>
        </Field>
        <Field Name="Quantity_Line_Lbl">
          <DataField>Quantity_Line_Lbl</DataField>
        </Field>
        <Field Name="Type_Line">
          <DataField>Type_Line</DataField>
        </Field>
        <Field Name="UnitPrice">
          <DataField>UnitPrice</DataField>
        </Field>
        <Field Name="UnitPrice_Lbl">
          <DataField>UnitPrice_Lbl</DataField>
        </Field>
        <Field Name="UnitOfMeasure">
          <DataField>UnitOfMeasure</DataField>
        </Field>
        <Field Name="UnitOfMeasure_Lbl">
          <DataField>UnitOfMeasure_Lbl</DataField>
        </Field>
        <Field Name="VATIdentifier_Line">
          <DataField>VATIdentifier_Line</DataField>
        </Field>
        <Field Name="VATIdentifier_Line_Lbl">
          <DataField>VATIdentifier_Line_Lbl</DataField>
        </Field>
        <Field Name="VATPct_Line">
          <DataField>VATPct_Line</DataField>
        </Field>
        <Field Name="VATPct_Line_Lbl">
          <DataField>VATPct_Line_Lbl</DataField>
        </Field>
        <Field Name="TransHeaderAmount">
          <DataField>TransHeaderAmount</DataField>
        </Field>
        <Field Name="TransHeaderAmountFormat">
          <DataField>TransHeaderAmountFormat</DataField>
        </Field>
        <Field Name="ItemReferenceNo">
          <DataField>ItemReferenceNo</DataField>
        </Field>
        <Field Name="ItemReferenceNo_Lbl">
          <DataField>ItemReferenceNo_Lbl</DataField>
        </Field>
        <Field Name="LineNo_AssemblyLine">
          <DataField>LineNo_AssemblyLine</DataField>
        </Field>
        <Field Name="Description_AssemblyLine">
          <DataField>Description_AssemblyLine</DataField>
        </Field>
        <Field Name="Quantity_AssemblyLine">
          <DataField>Quantity_AssemblyLine</DataField>
        </Field>
        <Field Name="Quantity_AssemblyLineFormat">
          <DataField>Quantity_AssemblyLineFormat</DataField>
        </Field>
        <Field Name="UnitOfMeasure_AssemblyLine">
          <DataField>UnitOfMeasure_AssemblyLine</DataField>
        </Field>
        <Field Name="VariantCode_AssemblyLine">
          <DataField>VariantCode_AssemblyLine</DataField>
        </Field>
        <Field Name="WorkDescriptionLineNumber">
          <DataField>WorkDescriptionLineNumber</DataField>
        </Field>
        <Field Name="WorkDescriptionLine">
          <DataField>WorkDescriptionLine</DataField>
        </Field>
        <Field Name="Description_ReportTotalsLine">
          <DataField>Description_ReportTotalsLine</DataField>
        </Field>
        <Field Name="Amount_ReportTotalsLine">
          <DataField>Amount_ReportTotalsLine</DataField>
        </Field>
        <Field Name="Amount_ReportTotalsLineFormat">
          <DataField>Amount_ReportTotalsLineFormat</DataField>
        </Field>
        <Field Name="AmountFormatted_ReportTotalsLine">
          <DataField>AmountFormatted_ReportTotalsLine</DataField>
        </Field>
        <Field Name="FontBold_ReportTotalsLine">
          <DataField>FontBold_ReportTotalsLine</DataField>
        </Field>
        <Field Name="FontUnderline_ReportTotalsLine">
          <DataField>FontUnderline_ReportTotalsLine</DataField>
        </Field>
        <Field Name="GreetingText">
          <DataField>GreetingText</DataField>
        </Field>
        <Field Name="BodyText">
          <DataField>BodyText</DataField>
        </Field>
        <Field Name="ClosingText">
          <DataField>ClosingText</DataField>
        </Field>
        <Field Name="PmtDiscText">
          <DataField>PmtDiscText</DataField>
        </Field>
        <Field Name="TotalNetAmount">
          <DataField>TotalNetAmount</DataField>
        </Field>
        <Field Name="TotalNetAmountFormat">
          <DataField>TotalNetAmountFormat</DataField>
        </Field>
        <Field Name="TotalVATBaseLCY">
          <DataField>TotalVATBaseLCY</DataField>
        </Field>
        <Field Name="TotalVATBaseLCYFormat">
          <DataField>TotalVATBaseLCYFormat</DataField>
        </Field>
        <Field Name="TotalAmountIncludingVAT">
          <DataField>TotalAmountIncludingVAT</DataField>
        </Field>
        <Field Name="TotalVATAmount">
          <DataField>TotalVATAmount</DataField>
        </Field>
        <Field Name="TotalVATAmountFormat">
          <DataField>TotalVATAmountFormat</DataField>
        </Field>
        <Field Name="TotalVATAmountLCY">
          <DataField>TotalVATAmountLCY</DataField>
        </Field>
        <Field Name="TotalVATAmountLCYFormat">
          <DataField>TotalVATAmountLCYFormat</DataField>
        </Field>
        <Field Name="TotalInvoiceDiscountAmount">
          <DataField>TotalInvoiceDiscountAmount</DataField>
        </Field>
        <Field Name="TotalInvoiceDiscountAmountFormat">
          <DataField>TotalInvoiceDiscountAmountFormat</DataField>
        </Field>
        <Field Name="TotalPaymentDiscountOnVAT">
          <DataField>TotalPaymentDiscountOnVAT</DataField>
        </Field>
        <Field Name="TotalPaymentDiscountOnVATFormat">
          <DataField>TotalPaymentDiscountOnVATFormat</DataField>
        </Field>
        <Field Name="TotalExcludingVATText">
          <DataField>TotalExcludingVATText</DataField>
        </Field>
        <Field Name="TotalIncludingVATText">
          <DataField>TotalIncludingVATText</DataField>
        </Field>
        <Field Name="TotalSubTotal">
          <DataField>TotalSubTotal</DataField>
        </Field>
        <Field Name="TotalSubTotalFormat">
          <DataField>TotalSubTotalFormat</DataField>
        </Field>
        <Field Name="TotalQuantity">
          <DataField>TotalQuantity</DataField>
        </Field>
        <Field Name="TotalQuantityFormat">
          <DataField>TotalQuantityFormat</DataField>
        </Field>
        <Field Name="TotalSubTotalMinusInvoiceDiscount">
          <DataField>TotalSubTotalMinusInvoiceDiscount</DataField>
        </Field>
        <Field Name="TotalSubTotalMinusInvoiceDiscountFormat">
          <DataField>TotalSubTotalMinusInvoiceDiscountFormat</DataField>
        </Field>
        <Field Name="TotalText">
          <DataField>TotalText</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>
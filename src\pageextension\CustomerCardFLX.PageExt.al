pageextension 60005 "Customer Card FLX" extends "Customer Card"
{
    layout
    {
        addafter("Name 2")
        {
            field("Name 2x"; Rec."Name 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of Name2 field.';
            }
            field("Search Name FLX"; Rec."Search Name")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of Search Name field.';
            }
            field("Supplier Number FLX"; Rec."Supplier Number FLX")
            {
                ApplicationArea = All;
            }
        }
        addlast(content)
        {
            group("Flexati FLX")
            {
                Caption = 'Flexati';
                field("Note FLX"; Rec."Note FLX")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                }
                field("Bank Account No. FLX"; Rec."Company Bank Account Code FLX")
                {
                    ApplicationArea = All;
                }
                field("Bank Account Name FLX"; Rec."Company Bank Account Name FLX")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}
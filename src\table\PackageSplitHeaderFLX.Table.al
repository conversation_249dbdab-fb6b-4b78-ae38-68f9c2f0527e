/// <summary>
/// Table Package Split Header FLX (ID 60003).
/// </summary>
table 60003 "Package Split Header FLX"
{
    DataClassification = CustomerContent;
    Caption = 'Package Split Header';
    DrillDownPageId = "Package Split FLX";
    LookupPageId = "Package Split FLX";
    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            Editable = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                FlexatiSetup: Record "Flexati Setup FLX";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    FlexatiSetup.Get();
                    NoSeries.TestManual(FlexatiSetup."Package Split No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(3; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
            trigger OnValidate()
            begin
                PackageSplitManagement.ProcessPackageNo(Rec);
            end;
        }
        field(4; Completed; Boolean)
        {
            Caption = 'Completed';
            Editable = false;
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(5; "Total Quantity to Split"; Decimal)
        {
            Caption = 'Total Quantity to Split';
            Editable = false;
            ToolTip = 'Specifies the value of the Total Quantity to Split field.';
        }
        field(6; "Total Split Quantity"; Decimal)
        {
            Caption = 'Total Split Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package Split Line FLX".Quantity where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Split Quantity field.';
        }
        field(7; "Requested Hose Lenght"; Decimal)
        {
            Caption = 'Requested Hose Lenght';
            Editable = false;
            ToolTip = 'Specifies the value of the Requested Hose Lenght field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        FlexatiSetup: Record "Flexati Setup FLX";
        //NoSeriesManagement: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            FlexatiSetup.Get();
            FlexatiSetup.TestField("Package Split No. Series");
            //NoSeriesManagement.InitSeries(FlexatiSetup."Package Split No. Series", xRec."No. Series", 0D, "No.", "No. Series");
            "No." := NoSeries.GetNextNo(FlexatiSetup."Package Split No. Series");
        end;

        "Posting Date" := WorkDate();
    end;

    trigger OnDelete()
    var
        PackageSplitLine: Record "Package Split Line FLX";
    begin
        TestField(Completed, false);

        PackageSplitLine.SetRange("Document No.", Rec."No.");
        PackageSplitLine.DeleteAll(true);
    end;

    var
        PackageSplitManagement: Codeunit "Package Split Management FLX";
}
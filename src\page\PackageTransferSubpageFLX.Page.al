page 60019 "Package Transfer Subpage FLX"
{
    ApplicationArea = All;
    Caption = 'Lines';
    PageType = ListPart;
    SourceTable = "Package Transfer Line FLX";
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field(Description; Rec.Description)
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Transfer-from Bin Code"; Rec."Transfer-from Bin Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    QuickEntry = false;
                }
                field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                {
                    QuickEntry = false;
                }
            }
        }
    }
}
page 60022 "Shortage List - Cumulative Qty"
{
    ApplicationArea = All;
    Caption = 'Shortage List - Cumulative Qty';
    PageType = List;
    Editable = false;
    SourceTable = "Shortage List - Cumulative Qty";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Positive Qty."; Rec."Positive Qty.")
                {
                }
                field("Negative Qty."; Rec."Negative Qty.")
                {
                }
                field("Net Qty."; Rec."Net Qty.")
                {
                }
                field("Cumulative Net Qty."; Rec."Cumulative Net Qty.")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
    end;
}

table 60018 "Flexati Cue FLX"
{
    Caption = 'Flexati Cue';
    DataClassification = CustomerContent;

    fields
    {
        field(1; PK; Code[250])
        {
            Caption = 'PK';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Quality Controls - Open"; Integer)
        {
            Caption = 'Quality Controls - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Quality Control Header FLX" where(Posted = const(false)));
            ToolTip = 'Specifies the value of the Quality Controls - Open field.';
        }
        field(3; "Package Splits - Open"; Integer)
        {
            Caption = 'Package Splits - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Split Header FLX" where(Completed = const(false)));
            ToolTip = 'Specifies the value of the Package Splits - Open field.';
        }
        field(4; "Combined Shipments - Open"; Integer)
        {
            Caption = 'Combined Shipments - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Combined Shipment Header FLX" where(Status = filter(<> 'Shipped' | <> 'Invoiced')));
            ToolTip = 'Specifies the value of the Combined Shipments - Open field.';
        }
        field(5; "Package Transfers - Open"; Integer)
        {
            Caption = 'Package Transfers - Open';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Transfer Header FLX" where(Posted = const(false)));
            ToolTip = 'Specifies the value of the Package Transfers - Open field.';
        }
    }

    keys
    {
        key(PK; PK)
        {
            Clustered = true;
        }
    }
}
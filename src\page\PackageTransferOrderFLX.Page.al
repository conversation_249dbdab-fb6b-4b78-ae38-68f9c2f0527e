page 60018 "Package Transfer Order FLX"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Order';
    PageType = Document;
    SourceTable = "Package Transfer Header FLX";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus FLX")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                    QuickEntry = false;
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    QuickEntry = false;
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                {
                    QuickEntry = false;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    QuickEntry = false;
                }
                field(Posted; Rec.Posted)
                {
                    QuickEntry = false;
                    Editable = false;
                }
                field("Total Package Count"; Rec."Total Package Count")
                {
                }
            }
            group(BarcodeReadingArea)
            {
                Caption = 'Barcode Reading';
                field(Barcode; Rec.Barcode)
                {
                    QuickEntry = true;

                    trigger OnValidate()
                    begin

                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;
                }
            }
            part(Lines; "Package Transfer Subpage FLX")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = not Rec.Posted;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Post)
            {
                ApplicationArea = All;
                Caption = 'Post';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                ToolTip = 'Executes the Post action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    FlexatiPackageTransMgt.CreateItemReclassRecords(Rec);
                    CurrPage.Close();
                end;
            }
        }
    }
    var
        FlexatiPackageTransMgt: Codeunit "Package Trans. Mgt. FLX";
}
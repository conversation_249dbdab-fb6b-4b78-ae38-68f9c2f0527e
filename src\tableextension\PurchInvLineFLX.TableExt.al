tableextension 60022 "Purch. Inv. Line FLX" extends "Purch. Inv. Line"
{
    fields
    {
        field(60001; "Buy-from Vendor Name FLX"; Text[100])
        {
            Caption = 'Buy-from Vendor Name';
            ToolTip = 'Specifies the name of the vendor from whom the item was purchased.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Inv. Header"."Buy-from Vendor Name" where("No." = field("Document No.")));
        }
        field(60002; "Currency Code FLX"; Code[10])
        {
            Caption = 'Currency Code';
            ToolTip = 'Specifies the currency code used for the transaction.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Inv. Header"."Currency Code" where("No." = field("Document No.")));
        }
        field(60000; "Vendor Invoice No. FLX"; Code[35])
        {
            Caption = 'Vendor Invoice No.';
            ToolTip = 'Specifies the vendor invoice number.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Inv. Header"."Vendor Invoice No." where("No." = field("Document No.")));
        }
    }
}
page 60042 "Consumption Production FLX"
{
    ApplicationArea = All;
    Caption = 'Consumption Production';
    PageType = StandardDialog;
    SourceTable = "Production Operation FLX";
    SourceTableTemporary = true;
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            group(ConsumptionProduction)
            {
                Caption = 'Consumption Production';
                field("User ID"; Rec."User ID")
                {
                    TableRelation = "Item Journal Batch".Name;
                }
                field("Production Line No."; Rec."Production Line No.")
                {
                    TableRelation = "Work Center-Prod. Line Mapping"."Production Line No.";
                }
                field("Work Center No."; Rec."Work Center No.")
                {
                    TableRelation = "Work Center"."No.";
                    Editable = false;
                }
                field("Label"; Rec."Package No.")
                {
                    TableRelation = "Package No. Information"."Package No.";
                }
                field("Consumption Package No."; Rec."Consumption Package No.")
                {
                    TableRelation = "Package No. Information"."Package No.";
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Init();
        Rec.Insert(false);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        FlexatiProductionMngt.ConsumptionProductionProcess(Rec);
    end;

    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}

pageextension 60025 "Item List FLX" extends "Item List"
{
    layout
    {
        addafter("No.")
        {

            field("No. 2 FLX"; Rec."No. 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the No. 2 field.';
            }
            field("Customer No. FLX"; Rec."Customer No. FLX")
            {
                ApplicationArea = All;
            }
        }
        addafter(InventoryField)
        {
            field("Net Change FLX"; Rec."Net Change")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Net Change field.';
            }
            field("Net Weight FLX"; Rec."Net Weight")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Net Weight field.';
            }
            field("Rolled-up Material Cost FLX"; Rec."Rolled-up Material Cost")
            {
                ToolTip = 'Specifies the value of the Rolled-up Material Cost field.';
                ApplicationArea = All;
            }
            field("BP (bar) FLX"; Rec."BP (bar) FLX")
            {
                ApplicationArea = All;
            }
            field("Cat FLX"; Rec."Cat FLX")
            {
                ApplicationArea = All;
            }
            field("Col FLX"; Rec."Col FLX")
            {
                ApplicationArea = All;
            }
            field("Cover FLX"; Rec."Cover FLX")
            {
                ApplicationArea = All;
            }
            field("F FLX"; Rec."F FLX")
            {
                ApplicationArea = All;
            }
            field("ID (mm) FLX"; Rec."ID (mm) FLX")
            {
                ApplicationArea = All;
            }
            field("Info FLX"; Rec."Info FLX")
            {
                ApplicationArea = All;
            }
            field("LD FLX"; Rec."LD FLX")
            {
                ApplicationArea = All;
            }
            field("Notes FLX"; Rec."Notes FLX")
            {
                ApplicationArea = All;
            }
            field("OD (mm) FLX"; Rec."OD (mm) FLX")
            {
                ApplicationArea = All;
            }
            field("Tube FLX"; Rec."Tube FLX")
            {
                ApplicationArea = All;
            }
            field("Type FLX"; Rec."Type FLX")
            {
                ApplicationArea = All;
            }
            field("Width FLX"; Rec."Width FLX")
            {
                ApplicationArea = All;
            }
            field("WP (bar) FLX"; Rec."WP (bar) FLX")
            {
                ApplicationArea = All;
            }
        }
        addafter("Description 2")
        {
            field("Search Description FLX"; Rec."Search Description")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of Search Description field.';
            }
        }
        modify("Last Direct Cost")
        {
            Visible = true;

        }
    }
    actions
    {
        addafter(Production)
        {
            action("CalculateStandartCostForReplenishmentSystemProdOrder FLX")
            {
                AccessByPermission = tabledata "Production BOM Header" = R;
                ApplicationArea = Manufacturing;
                Caption = 'Calc. Production Std. Cost for Finished Goods';
                Image = CalculateCost;
                ToolTip = 'Executes the Calc. Production Std. Cost for Finished Goods action.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                trigger OnAction()
                begin
                    FlexatiProductionMngt.UpdateStandardCostForReplenishmentSystemProduction();
                end;
            }
            action("UpdateNetWeights FLX")
            {
                //AccessByPermission = tabledata "BOM Component" = R;
                ApplicationArea = All;
                Caption = 'Update Net Weights';
                Image = SuggestFinancialCharge;
                ToolTip = 'Executes the Update Net Weights action.';
                trigger OnAction()
                var
                    Item: Record Item;
                    SuccesMsg: Label '%1 items net weight updated.', Comment = '%1=Item.Count';
                begin
                    //Item.SetRange("No.", Rec."No.");
                    Item.SetFilter("Production BOM No.", '<>%1', '');
                    if not Item.FindSet(true) then
                        exit;

                    repeat
                        Item."Net Weight" := FlexatiSalesManagement.CalculateCoilWeightFromItemNo(Item."No.");
                        Item.Modify(true);
                    until Item.Next() = 0;

                    Message(SuccesMsg, Item.Count());
                end;
            }
        }
    }
    var
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
}
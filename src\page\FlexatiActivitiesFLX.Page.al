page 60031 "Flexati Activities FLX"
{
    ApplicationArea = All;
    Caption = 'Flexati Activities';
    PageType = CardPart;
    SourceTable = "Flexati Cue FLX";

    layout
    {
        area(Content)
        {
            cuegroup(Production)
            {
                Caption = 'Production';
                actions
                {
                    action(StartProduction)
                    {
                        Caption = 'Start Production';
                        RunObject = page "Start Production FLX";
                        Image = TileGreen;
                        ToolTip = 'Executes the Start Production action.';
                    }
                    action(FinishProduction)
                    {
                        Caption = 'Finish Production';
                        RunObject = page "Finish Production FLX";
                        Image = TileOrange;
                        ToolTip = 'Executes the Finish Production action.';
                    }
                }
            }
            cuegroup(FlexatiActivities)
            {
                Caption = 'Flexati Activities';
                Visible = false;
                field("Package Splits - Open"; Rec."Package Splits - Open")
                {
                    DrillDownPageId = "Package Split List FLX";
                }
                field("Package Transfers - Open"; Rec."Package Transfers - Open")
                {
                    DrillDownPageId = "Package Transfer Orders FLX";
                }
                field("Quality Controls - Open"; Rec."Quality Controls - Open")
                {
                    DrillDownPageId = "Quality Control List FLX";
                }
                field("Combined Shipments - Open"; Rec."Combined Shipments - Open")
                {
                    DrillDownPageId = "Combined Shipment List FLX";
                }
            }
            cuegroup(FlexatiActions)
            {
                Caption = 'New Document';

                actions
                {
                    action(NewPackageSplit)
                    {
                        Caption = 'New Package Split';
                        ToolTip = 'Executes the New Package Split action.';
                        Image = TileBrickNew;
                        trigger OnAction()
                        var
                            PackageSplitHeader: Record "Package Split Header FLX";
                        begin
                            PackageSplitHeader.Init();
                            PackageSplitHeader.Insert(true);

                            PageManagement.PageRun(PackageSplitHeader);
                        end;
                    }
                    action(NewPackageTransfer)
                    {
                        Caption = 'New Package Transfer';
                        ToolTip = 'Executes the New Package Transfer action.';
                        Image = TileBrickNew;

                        trigger OnAction()
                        var
                            PackageTransferHeader: Record "Package Transfer Header FLX";
                        begin
                            PackageTransferHeader.Init();
                            PackageTransferHeader.Insert(true);

                            PageManagement.PageRun(PackageTransferHeader);
                        end;
                    }
                    action(NewQualityControl)
                    {
                        Caption = 'New Quality Control';
                        ToolTip = 'Executes the New Quality Control action.';
                        Image = TileBrickNew;
                        trigger OnAction()
                        var
                            QualityControlHeader: Record "Quality Control Header FLX";
                        begin
                            QualityControlHeader.Init();
                            QualityControlHeader.Insert(true);

                            PageManagement.PageRun(QualityControlHeader);
                        end;
                    }
                    action(NewCombinedShipment)
                    {
                        Caption = 'New Combined Shipment';
                        ToolTip = 'Executes the New Combined Shipment action.';
                        Image = TileBrickNew;
                        trigger OnAction()
                        var
                            CombinedShipmentHeader: Record "Combined Shipment Header FLX";
                        begin
                            CombinedShipmentHeader.Init();
                            CombinedShipmentHeader.Insert(true);

                            PageManagement.PageRun(CombinedShipmentHeader);
                        end;
                    }
                    action(NewPalette)
                    {
                        Caption = 'New Palette';
                        ToolTip = 'Executes the New Palette action.';
                        Image = TileBrickNew;
                        trigger OnAction()
                        var
                            PackageNoInformation: Record "Package No. Information";
                        begin
                            FlexatiSalesManagement.CreateNewParentPackage(PackageNoInformation, Enum::"Package Type FLX"::Palette);

                            Page.Run(Page::"Tablet Package No. Information", PackageNoInformation);
                        end;
                    }
                    action(NewBulk)
                    {
                        Caption = 'New Bulk';
                        ToolTip = 'Executes the New Bulk action.';
                        Image = TileBrickNew;
                        trigger OnAction()
                        var
                            PackageNoInformation: Record "Package No. Information";
                        begin
                            FlexatiSalesManagement.CreateNewParentPackage(PackageNoInformation, Enum::"Package Type FLX"::Bulk);

                            Page.Run(Page::"Tablet Package No. Information", PackageNoInformation);
                        end;
                    }
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(false);
        end;
    end;

    var
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
        PageManagement: Codeunit "Page Management";
}
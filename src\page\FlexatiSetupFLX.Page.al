page 60002 "Flexati Setup FLX"
{
    PageType = Card;
    SourceTable = "Flexati Setup FLX";
    Caption = 'Flexati Setup';
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("Consumption Jnl. Template Name"; Rec."Consumption Jnl. Template Name")
                {
                }
                field("Output Jnl. Template Name"; Rec."Output Jnl. Template Name")
                {
                }
                field("Work Center Group for Cost"; Rec."Work Center Group for Cost")
                {
                }
                field("Package Split Jnl. Tmpl. Name"; Rec."Package Split Jnl. Tmpl. Name")
                {
                }
                field("Package Split Jnl. Batch Name"; Rec."Package Split Jnl. Batch Name")
                {
                }
                field("Package Transfer Template Name"; Rec."Package Transfer Template Name")
                {
                }
                // field("Package Transfer Batch Name"; Rec."Package Transfer Batch Name")
                // {
                //     ToolTip = 'Specifies the value of the Package Transfer Template Name field.';
                // }
                field("Shipment Location Code"; Rec."Shipment Location Code")
                {
                }
                field("Shipment Bin Code"; Rec."Shipment Bin Code")
                {
                }
            }
            group(Numbering)
            {
                Caption = 'Numbering';
                field("Package Split No. Series"; Rec."Package Split No. Series")
                {
                }
                field("Head No. Series"; Rec."Head Label No. Series")
                {
                }
                field("Palette No. Series"; Rec."Palette Label No. Series")
                {
                }
                field("Combined Shipment No. Series"; Rec."Combined Shipment No. Series")
                {
                }
                field("Quality Control No. Series"; Rec."Quality Control No. Series")
                {
                }
                field("Package Transfer No. Series"; Rec."Package Transfer No. Series")
                {
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        Rec.InsertIfNotExists();
    end;
}

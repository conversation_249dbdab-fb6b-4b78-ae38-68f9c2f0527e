table 60012 "Pallets FLX"
{
    Caption = 'Pallets';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Pallet Code"; Code[20])
        {
            AllowInCustomizations = Always;
            Caption = 'Pallet Code';
            NotBlank = false;
        }
        field(2; Description; Text[50])
        {
            Caption = 'Description';
        }
        field(3; "Tare Weight"; Decimal)
        {
            Caption = 'Tare Weight';
        }
        field(4; "Coil Qty"; Decimal)
        {
            Caption = 'Coil Qty';
        }
        field(5; "Pallet Qty"; Decimal)
        {
            Caption = 'Pallet Qty';
        }
        field(6; "Pallet Meter Text"; Text[1024])
        {
            Caption = 'Pallet Meter Text';
        }
        field(7; "Pallet Feet Text"; Text[1024])
        {
            Caption = 'Pallet Feet Text';
        }
    }
    keys
    {
        key(PK; "Pallet Code")
        {
            Clustered = true;
        }
    }
}
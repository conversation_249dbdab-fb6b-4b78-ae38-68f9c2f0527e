/// <summary>
/// Report Customer Label FLX (ID 60012).
/// </summary>
report 60012 "Customer Label FLX"
{
    ApplicationArea = All;
    Caption = 'Customer Label';
    UsageCategory = ReportsAndAnalysis;
    //DefaultLayout = RDLc;
    //RDLCLayout = './src/ReportLayouts/CustomerLabelToplu.rdlc';
    dataset
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            CalcFields = Inventory;
            column(IDmmFLX_PackageNoInformation; "ID (mm) FLX")
            {
            }
            column(ODmmFLX_PackageNoInformation; "OD (mm) FLX")
            {
            }
            column(ProductionOrderNoFLX; "Production Order No. FLX")
            {
            }
            column(SalesOrderNoFLX_SubPalet; "Sales Order No. FLX")
            {
            }
            column(BPbarFLX_PackageNoInformation; "BP (bar) FLX")
            {
            }
            column(WPbarFLX_PackageNoInformation; "WP (bar) FLX")
            {
            }
            column(SelltoCustomerNameFLX; "Sell-to Customer Name FLX")
            {
            }
            column(SelltoCustomerRegisterName; CustRegisterName)
            {
            }
            column(Description; Description)
            {
            }
            column(Description2; ItemDesc2)
            {
            }
            column(CustLabelDesc; CustLabelDesc)
            {

            }
            column(CustLabelDesc2; CustLabelDesc2)
            {

            }
            column(CustLabelDesc3; CustLabelDesc3)
            {

            }
            column(HoseLenghtFLX; "Hose Lenght FLX")
            {
            }
            column(QtyFit; QtyFit)
            {
            }
            column(HoseLenghtFLX_PackageNoInformation; "Hose Lenght FLX")
            {
            }
            column(Weight_PackageNoInformation; Weight)
            {
            }
            column(LabelLenghtFLX; "Label Lenght FLX")
            {
            }
            column(PackageNo; "Package No.")
            {
            }
            column(YourReferenceFLX_PackageNoInformation; "Your Reference FLX")
            {
            }
            column(YourReferenceFLX_PalettePackageNoInformation; "Your Reference FLX")
            {
            }
            column(ItemNo_PackageNoInformation; "Item No.")
            {
            }
            column(ItemSearchDesc_PackageNoInformation; ItemSearchDesc)
            {
            }
            column(BarcodeItemSearchDesc_PackageNoInformation; BarcodeItemSearchDesc)
            {
            }
            column(BarcodeItemSearchDescCode39_PackageNoInformation; BarcodeItemSearchDescCode39)
            {
            }
            column(Inventory_PackageNoInformation; Inventory)
            {
            }
            column(SalesOrderLineNoFLX_PackageNoInformation; "Sales Order Line No. FLX")
            {
            }
            column(QrCode; QrCode)
            {
            }
            column(ProductionOrderLineNoFLX_PackageNoInformation; "Production Order Line No. FLX")
            {
            }
            column(Ship_to_Code_FLX; "Ship-to Code FLX")
            {
            }
            column(BarcodePackageNo_PackageNoInformation; BarcodePackageNo)
            {
            }
            column(BarcodePO_PackageNoInformation; BarcodePO)
            {
            }
            column(BarcodeDeliveryNo_PackageNoInformation; BarcodeDeliveryNo)
            {
            }
            column(DeliveryNo_PackageNoInformation; DeliveryNo)
            {
            }
            column(BarcodeItemCrossRef_PackageNoInformation; BarcodeItemCrossRef)
            {
            }
            column(BarcodeQty_PackageNoInformation; BarcodeQty)
            {
            }
            column(BarcodeQtyLabel_PackageNoInformation; BarcodeQtyLabel)
            {
            }
            column(BarcodeMadeinturkey; BarcodeMadeinturkey)
            {
            }
            column(LotNo_PackageNoInformation; "Lot No. FLX")
            {
            }
            column(PackageNo_PalettePackageNoInformation; "Package No.")
            {
            }
            column(ProductionOrderNoFLX_PalettePackageNoInformation; "Production Order No. FLX")
            {
            }
            column(SalesOrderNoFLX_PalettePackageNoInformation; PackageNoInformation."Sales Order No. FLX")
            {
            }
            column(SelltoCustomerNameFLX_PalettePackageNoInformation; "Sell-to Customer Name FLX")
            {
            }
            column(Today_PalettePackageNoInformation; Format(Today()))
            {
            }
            dataitem(Customer; Customer)
            {
                DataItemLink = "No." = field("Sell-to Customer No. FLX");

                column(Registration_Name_INF; "Registration Name INF")
                {
                }
            }
            dataitem(Item; Item)
            {
                DataItemLink = "No." = field("Item No.");

                column(NotesFLX_Item; "Notes FLX")
                {
                }
                trigger OnAfterGetRecord()
                begin
                    //Message("Notes FLX");
                end;
            }
            trigger OnAfterGetRecord()
            begin
                GenerateQRCode();
                GetCustSubLabelInfo();
                GenerateCustSubLabelQRCode();
            end;
        }
    }

    local procedure GenerateQRCode()
    var
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
    begin
        BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
        QrCode := BarcodeFontProvider2D.EncodeFont(PackageNoInformation."Package No.", BarcodeSymbology2D);
    end;

    local procedure GenerateCustSubLabelQRCode()
    var
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeFontProvider: Interface "Barcode Font Provider";
    begin
        QtyFit := Round(PackageNoInformation."Hose Lenght FLX" / 0.305, 0.01);
        BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
        BarcodeSymbology := Enum::"Barcode Symbology"::Code39;
        BarcodeQty := BarcodeFontProvider.EncodeFont(Format(PackageNoInformation."Hose Lenght FLX"), BarcodeSymbology);
        //BarcodeQtyLabel := 'Qty / Qte ' + Format(PackageNoInformation."Hose Lenght FLX") + ' FT';
        BarcodeQtyLabel := 'Qty / Qte ' + Format(QtyFit) + ' FT';
        BarcodePO := BarcodeFontProvider.EncodeFont(PackageNoInformation."Your Reference FLX", BarcodeSymbology);
        BarcodeDeliveryNo := BarcodeFontProvider.EncodeFont(DeliveryNo, BarcodeSymbology);
        BarcodePackageNo := BarcodeFontProvider.EncodeFont(PackageNoInformation."Package No.", BarcodeSymbology);
        BarcodeItemCrossRef := BarcodeFontProvider.EncodeFont(ItemCrossRef, BarcodeSymbology);
        BarcodeMadeinturkey := BarcodeFontProvider.EncodeFont('MADE IN TURKEY', BarcodeSymbology);
        BarcodeItemSearchDescCode39 := BarcodeFontProvider.EncodeFont(ItemSearchDesc, BarcodeSymbology);
        BarcodeSymbology := Enum::"Barcode Symbology"::"EAN-13";
        BarcodeItemSearchDesc := BarcodeFontProvider.EncodeFont(ItemSearchDesc, BarcodeSymbology);
    end;

    local procedure GetCustSubLabelInfo()
    var
        Cust: Record Customer;
        Item: Record Item;
        ItemCrossReference: Record "Item Reference";
        SalesShipmentHd: Record "Sales Shipment Header";
        SalesShipmentLine: Record "Sales Shipment Line";
        SalesLine: Record "Sales Line";
        FlexSalesMng: Codeunit "Flexati Sales Management FLX";
    begin
        Item.Get(PackageNoInformation."Item No.");
        ItemSearchDesc := Item."Search Description";
        ItemDesc2 := Item."Description 2";
        //Weight := Item."Net Weight";
        CustLabelDesc := PackageNoInformation.Description;//CopyStr(PackageNoInformation.Description, 1, 30);
        CustLabelDesc2 := CopyStr(PackageNoInformation.Description, 31, 30);
        CustLabelDesc3 := CopyStr(PackageNoInformation.Description, 61, 30);
        if (PackageNoInformation."Sales Order No. FLX" <> '') and (PackageNoInformation."Sales Order Line No. FLX" > 0) then begin
            SalesLine.Reset();
            SalesLine.SetRange("Document No.", PackageNoInformation."Sales Order No. FLX");
            SalesLine.SetRange("Line No.", PackageNoInformation."Sales Order Line No. FLX");
            if SalesLine.FindFirst() then begin
                CustLabelDesc := SalesLine.Description;//CopyStr(SalesLine.Description, 1, 30);
                CustLabelDesc2 := CopyStr(SalesLine.Description, 31, 30);
                CustLabelDesc3 := CopyStr(SalesLine.Description, 61, 30);
            end;
        end;
        if PackageNoInformation."Scaled Weight FLX" > 0 then
            Weight := PackageNoInformation."Scaled Weight FLX"
        else
            Weight := FlexSalesMng.CalculatePackageWeight(PackageNoInformation);
        //TotalHoseLength += PackageNoInformation."Hose Lenght FLX";
        Cust.Get(PackageNoInformation."Sell-to Customer No. FLX");
        CustRegisterName := Cust."Registration Name INF";
        if CustRegisterName = '' then
            CustRegisterName := PackageNoInformation."Sell-to Customer Name FLX";

        DeliveryNo := PackageNoInformation."Shipment No FLX";
        if DeliveryNo = '' then begin
            SalesShipmentLine.Reset();
            SalesShipmentLine.SetRange("Order No.", PackageNoInformation."Sales Order No. FLX");
            SalesShipmentLine.SetRange("Order Line No.", PackageNoInformation."Sales Order Line No. FLX");
            if SalesShipmentLine.FindLast() then begin
                SalesShipmentHd.Get(SalesShipmentLine."Document No.");
                DeliveryNo := SalesShipmentHd."External Document No.";
            end;
        end;

        ItemCrossRef := '';
        ItemCrossReference.Reset();
        ItemCrossReference.SetRange("Item No.", PackageNoInformation."Item No.");
        ItemCrossReference.SetRange("Reference Type", ItemCrossReference."Reference Type"::"Bar Code");
        if ItemCrossReference.FindFirst() then
            ItemCrossRef := ItemCrossReference."Reference No.";
    end;

    var
        QtyFit: Decimal;
        //TotalHoseLength: Decimal;
        Weight: Decimal;
        BarcodeDeliveryNo: Text;
        BarcodeItemCrossRef: Text;
        BarcodeItemSearchDesc: Text;
        BarcodeItemSearchDescCode39: Text;
        BarcodeMadeinturkey: Text;
        BarcodePackageNo: Text;
        BarcodePO: Text;
        BarcodeQty: Text;
        BarcodeQtyLabel: Text;
        QrCode: Text;
        CustRegisterName: Text[100];
        DeliveryNo: Text[100];
        ItemCrossRef: Text[100];
        ItemDesc2: Text[100];
        ItemSearchDesc: Text[100];
        CustLabelDesc: Text[100];
        CustLabelDesc2: Text[100];
        CustLabelDesc3: Text[100];
}

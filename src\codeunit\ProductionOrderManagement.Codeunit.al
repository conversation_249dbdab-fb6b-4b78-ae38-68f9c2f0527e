codeunit 60006 "Production Order Management"
{
    SingleInstance = true;
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order Lines", OnCheckMakeOrderLineBeforeIf, '', false, false)]
    local procedure "Create Prod. Order Lines_OnCheckMakeOrderLineBeforeIf"(var ProductionOrder: Record "Production Order"; var ProdOrderLine: Record "Prod. Order Line"; var ProdOrderComponent: Record "Prod. Order Component"; var MakeProdOrder: Boolean)
    var
        LocalProductionOrder: Record "Production Order";
        LocalCreateProdOrderLines: Codeunit "Create Prod. Order Lines";
        Suffix: Integer;
        BaseNo: Code[20];
        UniqueNoFound: Boolean;
    begin
        MakeProdOrder := false;

        LocalProductionOrder.Init();
        LocalProductionOrder.TransferFields(ProductionOrder, false);
        LocalProductionOrder.Status := LocalProductionOrder.Status::"Firm Planned";
        Suffix := 1;
        BaseNo := ProductionOrder."No.";
        UniqueNoFound := false;
        repeat
            LocalProductionOrder."No." := CopyStr(BaseNo + '-' + Format(Suffix), 1, MaxStrLen(LocalProductionOrder."No."));
            if not LocalProductionOrder.Get(LocalProductionOrder.Status, LocalProductionOrder."No.") then
                UniqueNoFound := true
            else
                Suffix := Suffix + 1;
        until UniqueNoFound;
        LocalProductionOrder.Insert(true);
        LocalProductionOrder.Validate("Source Type", LocalProductionOrder."Source Type"::Item);
        LocalProductionOrder.Validate("Source No.", ProdOrderComponent."Item No.");
        LocalProductionOrder.Validate(Quantity, ProdOrderComponent."Expected Quantity");
        LocalProductionOrder.Modify(true);

        GlobalOldProdOrderLine := ProdOrderLine;

        LocalCreateProdOrderLines.Copy(LocalProductionOrder, 1, '', false);

        Clear(GlobalOldProdOrderLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order Lines", OnAfterProdOrderLineInsert, '', false, false)]
    local procedure "Create Prod. Order Lines_OnAfterProdOrderLineInsert"(var Sender: Codeunit "Create Prod. Order Lines"; var ProdOrder: Record "Production Order"; var ProdOrderLine: Record "Prod. Order Line"; var NextProdOrderLineNo: Integer)
    var
        //Item: Record Item;
        SalesLine: Record "Sales Line";
    begin
        if GlobalOldProdOrderLine."Prod. Order No." = '' then
            exit;

        SalesLine.Get(SalesLine."Document Type"::Order, GlobalOldProdOrderLine."Prod. Order No.", GlobalOldProdOrderLine."Source Line No. FLX");
        FlexatiProductionMngt.AssignLotNoToProdOrderLine(ProdOrderLine, SalesLine);
        ProdOrderLine.Modify(true)
        // Item.Get(ProdOrderLine."Item No.");
        // Item.TestField("Lot Nos.");

        // ProdOrderLine."Sales Order - Line No. FLX" := GlobalOldProdOrderLine."Sales Order - Line No. FLX";
        // ProdOrderLine."Prod. Order - Line No. FLX" := GlobalOldProdOrderLine."Prod. Order - Line No. FLX";
        // ProdOrderLine."Source Line No. FLX" := GlobalOldProdOrderLine."Source Line No. FLX";
        // ProdOrderLine."Customer No. FLX" := GlobalOldProdOrderLine."Customer No. FLX";
        // ProdOrderLine."Customer Name FLX" := GlobalOldProdOrderLine."Customer Name FLX";
        // ProdOrderLine."Flexati Shipment Date FLX" := GlobalOldProdOrderLine."Flexati Shipment Date FLX";
    end;

    var
        GlobalOldProdOrderLine: Record "Prod. Order Line";
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
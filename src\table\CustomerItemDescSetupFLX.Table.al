table 60020 "Customer-Item Desc. Setup FLX"
{
    Caption = 'Customer - Item Desc. Setup';
    LookupPageId = "Customer-Item Desc. Setup FLX";
    DrillDownPageId = "Customer-Item Desc. Setup FLX";
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            ToolTip = 'Specifies the value of the Customer No. field.';
            TableRelation = "Customer"."No.";
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
            TableRelation = Item."No.";
        }
        field(3; "Hose Lenght"; Decimal)
        {
            Caption = 'Hose Lenght';
            ToolTip = 'Specifies the value of the Hose Lenght field.';
        }
        field(4; "UoM Code"; Code[10])
        {
            Caption = 'UoM Code';
            ToolTip = 'Specifies the value of the UoM Code field.';
            TableRelation = "Item Unit of Measure".Code where("Item No." = field("Item No."));
        }
        field(5; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(6; "Custom Code"; Code[20])
        {
            Caption = 'Custom Code';
            ToolTip = 'Specifies the value of the Custom Code field.';
        }

    }
    keys
    {
        key(PK; "Customer No.", "Item No.", "Hose Lenght", "UoM Code", "Custom Code")
        {
            Clustered = true;
        }
    }
}
pageextension 60019 "Output Journal FLX" extends "Output Journal"
{
    layout
    {
        addlast(Control1)
        {
            field("Lot No. FLX"; Rec."Lot No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the lot number of the item.';
            }
            field("Package No. FLX"; Rec."Package No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the package number of the item.';
            }
        }
        // modify("Starting Time")
        // {
        //     Visible = true;
        // }
        // modify("Ending Time")
        // {
        //     Visible = true;
        // }
        addbefore("Run Time")
        {
            field("Starting DateTime FLX"; Rec."Starting DateTime FLX")
            {
                ApplicationArea = All;
            }
            field("Ending DateTime FLX"; Rec."Ending DateTime FLX")
            {
                ApplicationArea = All;
            }
        }
    }
}
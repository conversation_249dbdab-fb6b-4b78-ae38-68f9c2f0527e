table 60009 "Quality Control Line FLX"
{
    Caption = 'Quality Control Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            AllowInCustomizations = Always;
            Caption = 'Document No.';
            Editable = false;
        }
        field(2; "Line No."; Integer)
        {
            AllowInCustomizations = Always;
            Caption = 'Line No.';
            Editable = false;
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(4; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(5; Quantity; Decimal)
        {
            Caption = 'Quantity';
            Editable = false;
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(6; "Quality Control Status"; Enum "Quality Control Status FLX")
        {
            Caption = 'Quality Control Status';
            ToolTip = 'Specifies the value of the Quality Control Status field.';
            trigger OnValidate()
            begin
                "Reject Reason Code" := '';
            end;
        }
        field(7; "Reject Reason Code"; Code[10])
        {
            Caption = 'Reject Reason Code';
            TableRelation = "Reason Code".Code;
            ToolTip = 'Specifies the value of the Reject Reason Code field.';
        }
        field(8; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            TableRelation = "Package No. Information"."Package No." where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Package No. field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        QualityControlLine: Record "Quality Control Line FLX";
    begin
        QualityControlLine.SetRange("Document No.", Rec."Document No.");
        if QualityControlLine.FindLast() then
            Rec."Line No." := QualityControlLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}
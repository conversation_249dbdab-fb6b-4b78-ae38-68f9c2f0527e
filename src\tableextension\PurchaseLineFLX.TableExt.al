tableextension 60020 "Purchase Line FLX" extends "Purchase Line"
{
    fields
    {
        field(60000; "Loading Date FLX"; Date)
        {
            Caption = 'Loading Date';
            ToolTip = 'Specifies the date when the item was loaded.';
        }
        field(60001; "Buy-from Vendor Name FLX"; Text[100])
        {
            Caption = 'Buy-from Vendor Name';
            ToolTip = 'Specifies the name of the vendor from whom the item was purchased.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purchase Header"."Buy-from Vendor Name" where("No." = field("Document No."), "Document Type" = field("Document Type")));
        }

    }
}
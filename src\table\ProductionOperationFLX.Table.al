table 60001 "Production Operation FLX"
{
    Caption = 'Production Operation';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            AllowInCustomizations = Always;
            Caption = 'Primary Key';
            NotBlank = false;
        }
        field(2; "User ID"; Code[10])
        {
            Caption = 'User ID';
            ToolTip = 'Specifies the value of the User ID field.';
        }
        field(3; "Production Line No."; Code[10])
        {
            Caption = 'Production Line No.';
            ToolTip = 'Specifies the value of the Production Line No. field.';
            trigger OnValidate()
            var
                WorkCenterProdLineMapping: Record "Work Center-Prod. Line Mapping";
            begin
                WorkCenterProdLineMapping.Get("Production Line No.");
                "Work Center No." := WorkCenterProdLineMapping."Work Center Code";
            end;
        }
        field(4; "Work Center No."; Code[10])
        {
            Caption = 'Work Center No.';
            ToolTip = 'Specifies the value of the Work Center No. field.';
        }
        field(5; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Label field.';
        }
        field(6; "Consumption Package No."; Code[50])
        {
            Caption = 'Consumption Package No.';
            ToolTip = 'Specifies the value of the Consumption Package No. field.';
        }
    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
}
codeunit 60002 "Package Split Management FLX"
{
    procedure ProcessPackageNo(var PackageSplitHeader: Record "Package Split Header FLX")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageSplitHeader."Package No." = '' then
            exit;

        PackageNoInformation.SetRange("Package No.", PackageSplitHeader."Package No.");
        PackageNoInformation.FindFirst();

        PackageSplitChecks(PackageNoInformation, PackageSplitHeader);

        PackageNoInformation.CalcFields(Inventory);
        PackageSplitHeader."Total Quantity to Split" := PackageNoInformation.Inventory;
        PackageSplitHeader."Requested Hose Lenght" := PackageNoInformation."Hose Lenght FLX";

        if PackageNoInformation."Package Type FLX" = PackageNoInformation."Package Type FLX"::Head then
            CreatePackageSplitLinesForRequestedHoseLenght(PackageSplitHeader, PackageNoInformation);
    end;

    local procedure PackageSplitChecks(var PackageNoInformation: Record "Package No. Information"; var PackageSplitHeader: Record "Package Split Header FLX")
    var
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl FLX";
        PackageSplitLine: Record "Package Split Line FLX";
        CanNotSplitPalettesErr: Label 'You can not split palettes.';
        CombinedShipmentErr: Label 'You can not split a package that is read in Combined Shipment.';
        ConfirmQst: Label 'There are already split lines for this document. Do you want to delete them and continue?';
        ProcessAbortedErr: Label 'Process canceled by user.';
    begin
        PackageSplitLine.SetRange("Document No.", PackageSplitHeader."No.");
        if not PackageSplitLine.IsEmpty() then
            if not ConfirmManagement.GetResponseOrDefault(ConfirmQst) then
                Error(ProcessAbortedErr)
            else
                PackageSplitLine.DeleteAll(true);

        PackageNoInformation.TestField("Parent Package No. FLX", '');

        CombinedShipmentLineDtl.SetRange("Package No.", PackageNoInformation."Package No.");
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(CombinedShipmentErr);

        PackageNoInformation.CalcFields(Inventory);
        PackageNoInformation.TestField(Inventory);

        if PackageNoInformation."Package Type FLX" = PackageNoInformation."Package Type FLX"::Palette then
            Error(CanNotSplitPalettesErr);
    end;

    procedure CreatePackageSplitLinesForRequestedHoseLenght(PackageSplitHeader: Record "Package Split Header FLX"; PackageNoInformation: Record "Package No. Information")
    var
        PackageSplitLine: Record "Package Split Line FLX";
        NoSeries: Codeunit "No. Series";
        FullPackages: Integer;
        Remainder: Decimal;
        i: Integer;
    begin
        InventorySetup.Get();
        InventorySetup.TestField("Package Nos.");

        FullPackages := Round(PackageSplitHeader."Total Quantity to Split" / PackageSplitHeader."Requested Hose Lenght", 1, '<');
        Remainder := PackageSplitHeader."Total Quantity to Split" - (FullPackages * PackageSplitHeader."Requested Hose Lenght");

        PackageNoInformation.CalcFields("Location Code FLX");

        for i := 1 to FullPackages do begin
            PackageSplitLine.Init();
            PackageSplitLine."Document No." := PackageSplitHeader."No.";
            PackageSplitLine."New Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.", WorkDate(), true);
            PackageSplitLine."Item No." := PackageNoInformation."Item No.";
            PackageSplitLine."Item Description" := PackageNoInformation.Description;
            PackageSplitLine.Quantity := PackageSplitHeader."Requested Hose Lenght";
            PackageSplitLine."Lot No." := PackageNoInformation."Lot No. FLX";
            PackageSplitLine."Location Code" := PackageNoInformation."Location Code FLX";
            PackageSplitLine.Insert(true);
        end;

        if Remainder > 0 then begin
            PackageSplitLine.Init();
            PackageSplitLine."Document No." := PackageSplitHeader."No.";
            PackageSplitLine."New Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.", WorkDate(), true);
            PackageSplitLine."Item No." := PackageNoInformation."Item No.";
            PackageSplitLine."Item Description" := PackageNoInformation.Description;
            PackageSplitLine.Quantity := Remainder;
            PackageSplitLine."Lot No." := PackageNoInformation."Lot No. FLX";
            PackageSplitLine."Location Code" := PackageNoInformation."Location Code FLX";
            PackageSplitLine.Insert(true);
        end;
    end;

    procedure CreateItemReclassificationJournalLinesFromPackageSplitHeader(var PackageSplitHeader: Record "Package Split Header FLX")
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        PackageSplitLine: Record "Package Split Line FLX";
        ItemJournalLineLineNo: Integer;
        NoSplitLineErr: Label 'No Package Split Line found.';
        QtyErr: Label 'Total Split Quantity must be equal to Total Quantity to Split';
    begin
        PackageSplitHeader.TestField(Completed, false);

        FlexatiSetup.GetRecordOnce();

        PackageSplitHeader.CalcFields("Total Split Quantity");

        if PackageSplitHeader."Total Quantity to Split" <> PackageSplitHeader."Total Split Quantity" then
            Error(QtyErr);

        LastItemJournalLine.SetRange("Journal Template Name", FlexatiSetup."Package Split Jnl. Tmpl. Name");
        LastItemJournalLine.SetRange("Journal Batch Name", FlexatiSetup."Package Split Jnl. Batch Name");
        if LastItemJournalLine.FindLast() then
            ItemJournalLineLineNo := LastItemJournalLine."Line No." + 10000
        else
            ItemJournalLineLineNo := 10000;

        PackageSplitLine.SetRange("Document No.", PackageSplitHeader."No.");
        if not PackageSplitLine.FindSet(false) then
            Error(NoSplitLineErr);
        repeat
            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := FlexatiSetup."Package Split Jnl. Tmpl. Name";
            ItemJournalLine."Journal Batch Name" := FlexatiSetup."Package Split Jnl. Batch Name";
            ItemJournalLine."Line No." := ItemJournalLineLineNo;
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine."Entry Type" := ItemJournalLine."Entry Type"::Transfer;
            ItemJournalLine.Validate("Item No.", PackageSplitLine."Item No.");
            ItemJournalLine.Validate("Location Code", PackageSplitLine."Location Code");
            ItemJournalLine.Validate("Bin Code", FlexatiProductionMngt.GetBinCodeFromPackageNo(PackageSplitHeader."Package No."));
            ItemJournalLine.Validate(Quantity, PackageSplitLine.Quantity);
            ItemJournalLine.Validate("Lot No.", PackageSplitLine."Lot No.");
            ItemJournalLine.Validate("New Lot No.", PackageSplitLine."Lot No.");
            ItemJournalLine.Validate("Package No.", PackageSplitLine."New Package No.");
            ItemJournalLine.Validate("New Package No.", PackageSplitLine."New Package No.");
            ItemJournalLine.Insert(true);
            LastItemJournalLine := ItemJournalLine;
            AssignLotAndPackageNoToItemJournalLine(ItemJournalLine, PackageSplitLine);
            ItemJournalLineLineNo += 10000;
            CreatePackageNoInformationFromOldPackageNoInformation(PackageSplitLine);
        until PackageSplitLine.Next() = 0;

        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

        PackageSplitHeader.Completed := true;
        PackageSplitHeader.Modify(true);
    end;

    local procedure AssignLotAndPackageNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; PackageSplitLine: Record "Package Split Line FLX")
    var
        TempReservEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservStatus: Enum "Reservation Status";
    begin
        PackageSplitLine.CalcFields("Source Package No.");

        TempReservEntry.Init();
        TempReservEntry."Entry No." := 1;
        TempReservEntry."Lot No." := PackageSplitLine."Lot No."; //use Serial No. for SN
        TempReservEntry."Package No." := PackageSplitLine."Source Package No.";
        TempReservEntry.Quantity := ItemJournalLine.Quantity;

        // TempReservEntry."New Lot No." := TempReservEntry."Lot No.";
        // TempReservEntry."New Package No." := PackageSplitLine."New Package No.";

        //TempReservEntry."Expiration Date" := Today();
        TempReservEntry.Insert(false);

        //CreateReservEntry.SetDates(0D, TempReservEntry."Expiration Date");

        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer then //movement
            CreateReservEntry.SetNewTrackingFromItemJnlLine(ItemJournalLine);

        ItemJournalLine."Lot No." := '';
        ItemJournalLine."Package No." := '';
        ItemJournalLine.Modify(false);

        CreateReservEntry.CreateReservEntryFor(
          Database::"Item Journal Line", ItemJournalLine."Entry Type".AsInteger(),
          ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name", 0, ItemJournalLine."Line No.", ItemJournalLine."Qty. per Unit of Measure",
          TempReservEntry.Quantity, TempReservEntry.Quantity * ItemJournalLine."Qty. per Unit of Measure", TempReservEntry);

        CreateReservEntry.CreateEntry(
          ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Location Code", '', 0D, ItemJournalLine."Posting Date", 0, ReservStatus::Prospect);
    end;

    local procedure CreatePackageNoInformationFromOldPackageNoInformation(PackageSplitLine: Record "Package Split Line FLX")
    var
        NewPackageNoInformation: Record "Package No. Information";
        OldPackageNoInformation: Record "Package No. Information";
    begin
        PackageSplitLine.CalcFields("Source Package No.");

        OldPackageNoInformation.Get(PackageSplitLine."Item No.", '', PackageSplitLine."Source Package No.");

        NewPackageNoInformation.Init();
        NewPackageNoInformation.TransferFields(OldPackageNoInformation);
        NewPackageNoInformation."Package No." := PackageSplitLine."New Package No.";
        NewPackageNoInformation."Old Package No. FLX" := OldPackageNoInformation."Package No.";
        NewPackageNoInformation."Package Type FLX" := NewPackageNoInformation."Package Type FLX"::Coil;
        NewPackageNoInformation.Insert(true);

        NewPackageNoInformation.SetRecFilter();

        //Report.Run(Report::"QR Label FLX", false, true, NewPackageNoInformation);
    end;

    procedure CreateNewPackageSplitLineFromHeader(PackageSplitHeader: Record "Package Split Header FLX")
    var
        PackageNoInformation: Record "Package No. Information";
        PackageSplitLine: Record "Package Split Line FLX";
        NoSeries: Codeunit "No. Series";
    begin
        InventorySetup.Get();
        InventorySetup.TestField("Package Nos.");

        PackageNoInformation.SetRange("Package No.", PackageSplitHeader."Package No.");
        PackageNoInformation.FindFirst();

        PackageNoInformation.CalcFields("Location Code FLX");

        PackageSplitLine.Init();
        PackageSplitLine."Document No." := PackageSplitHeader."No.";
        PackageSplitLine.Insert(true);
        PackageSplitLine."New Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.", WorkDate(), true);
        PackageSplitLine."Item No." := PackageNoInformation."Item No.";
        PackageSplitLine."Item Description" := PackageNoInformation.Description;
        PackageSplitLine."Lot No." := PackageNoInformation."Lot No. FLX";
        PackageSplitLine."Location Code" := PackageNoInformation."Location Code FLX";
        PackageSplitLine.Modify(true);
    end;

    var
        FlexatiSetup: Record "Flexati Setup FLX";
        InventorySetup: Record "Inventory Setup";
        ConfirmManagement: Codeunit "Confirm Management";
        FlexatiProductionMngt: Codeunit "Flexati Production Mngt. FLX";
}
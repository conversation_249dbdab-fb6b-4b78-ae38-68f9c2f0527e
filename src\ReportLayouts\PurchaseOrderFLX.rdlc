﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>MSRB</am:Name>
      <am:Version>15.0.20283.0</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2024-11-08T07:23:14.4411331Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>d3c37077-f9e7-4ce9-811b-06b569b0bbc1</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.32542cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>4.67456cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.32542cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.32542cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.955cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.92855cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.82271cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.32542cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox7">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value EvaluationMode="Constant">No</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox7</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DescriptionTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!VendorCrossRefTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DueDateTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!QtyTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!UoMTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!UnitCostTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LineTotalTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="No_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!No_PurchLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>No_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Description_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Desc_PurchLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Description_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CrossReferenceNo_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CrossReferenceNo_PurchaseLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CrossReferenceNo_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DueDate_PurchaseHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ExpectedReceiptDate_PurchaseLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>d</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DueDate_PurchaseHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Quantity_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Qty_PurchLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>#,0;(#,0)</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Quantity_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="UnitofMeasureCode_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!UOM_PurchLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>UnitofMeasureCode_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="UnitCost_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DirUnitCost_PurchLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>UnitCost_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LineAmount_PurchaseLine">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!LineAmt_PurchLine.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <Format>n2</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LineAmount_PurchaseLine</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox21</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox23</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox25">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox26">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox25</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox29">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox26</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalTxt">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TotalTxt</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="LineAmount_PurchaseLine1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!LineAmt_PurchLine.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Format>n2</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>LineAmount_PurchaseLine1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox30">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox29</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox31">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox30</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox32">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox31</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox33">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox32</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox34">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox33</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox35">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox34</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CurrencyTxt">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CurrencyTxt.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CurrencyTxt</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CurrencyCode_PurchaseHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(Fields!CurrencyCode_PurchaseHeader.Value = "", Fields!LCYCode.Value, Fields!CurrencyCode_PurchaseHeader.Value)</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CurrencyCode_PurchaseHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                  <Visibility>
                    <Hidden>=Fields!Qty_PurchLine.Value=0</Hidden>
                  </Visibility>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.12281cm</Top>
            <Left>0.02646cm</Left>
            <Height>2.4cm</Height>
            <Width>19.6825cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>2.725cm</Height>
        <Style />
      </Body>
      <Width>19.70896cm</Width>
      <Page>
        <PageHeader>
          <Height>10.4583cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Image Name="Image2">
              <Source>Database</Source>
              <Value>=Fields!Picture.Value</Value>
              <MIMEType>image/png</MIMEType>
              <Sizing>FitProportional</Sizing>
              <Height>3.42012cm</Height>
              <Width>6.94285cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
            <Textbox Name="PrintDate">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PrintDate.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <Format>d</Format>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>PrintDate</rd:DefaultName>
              <Top>1.2cm</Top>
              <Left>17.1825cm</Left>
              <Height>0.6cm</Height>
              <Width>2.5cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!PurchaseOrderTxt.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox2</rd:DefaultName>
              <Left>15.42333cm</Left>
              <Height>0.6cm</Height>
              <Width>4.25917cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="VendorOrderNo_PurchaseHeader">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!DocumentNo.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>VendorOrderNo_PurchaseHeader</rd:DefaultName>
              <Top>0.6cm</Top>
              <Left>17.1825cm</Left>
              <Height>0.6cm</Height>
              <Width>2.5cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="PaymentTermsCode_PurchaseHeader">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PaymentTermsDesc.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>PaymentTermsCode_PurchaseHeader</rd:DefaultName>
              <Top>8.88788cm</Top>
              <Left>2.95861cm</Left>
              <Height>0.6cm</Height>
              <Width>6.54139cm</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="ShipmentMethodCode_PurchaseHeader">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!ShipmentMethodDesc.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>ShipmentMethodCode_PurchaseHeader</rd:DefaultName>
              <Top>9.48788cm</Top>
              <Left>2.95861cm</Left>
              <Height>0.6cm</Height>
              <Width>6.54139cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="OrderNoTxt">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!OrderNoTxt.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>OrderNoTxt</rd:DefaultName>
              <Top>0.6cm</Top>
              <Left>15.42333cm</Left>
              <Height>0.6cm</Height>
              <Width>1.75917cm</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="PrintDateTxt">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PrintDateTxt.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>PrintDateTxt</rd:DefaultName>
              <Top>1.2cm</Top>
              <Left>15.42333cm</Left>
              <Height>0.6cm</Height>
              <Width>1.75917cm</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="PaymentTermTxt">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!PaymentTermsDescCaption.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>PaymentTermTxt</rd:DefaultName>
              <Top>8.88788cm</Top>
              <Height>0.6cm</Height>
              <Width>2.8175cm</Width>
              <ZIndex>8</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="IncotermTxt">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!ShipmentMethodDescCaption.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>IncotermTxt</rd:DefaultName>
              <Top>9.48788cm</Top>
              <Height>0.6cm</Height>
              <Width>2.8175cm</Width>
              <ZIndex>9</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox17">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!ShipToTxt.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>4.54963cm</Top>
              <Left>10.35566cm</Left>
              <Height>0.6cm</Height>
              <Width>9.32684cm</Width>
              <ZIndex>10</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox19">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyEMail.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>7.54964cm</Top>
              <Left>10.35566cm</Left>
              <Height>0.6cm</Height>
              <Width>9.32684cm</Width>
              <ZIndex>11</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox20">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyPhone.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>6.94964cm</Top>
              <Left>10.35566cm</Left>
              <Height>0.6cm</Height>
              <Width>9.32684cm</Width>
              <ZIndex>12</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox27">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyAddress.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>5.74964cm</Top>
              <Left>10.35566cm</Left>
              <Height>1.2cm</Height>
              <Width>9.32684cm</Width>
              <ZIndex>13</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox28">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!CompanyNameVar.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>5.14963cm</Top>
              <Left>10.35566cm</Left>
              <Height>0.6cm</Height>
              <Width>9.32684cm</Width>
              <ZIndex>14</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox1">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!SupplierTxt.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>4.54964cm</Top>
              <Height>0.6cm</Height>
              <Width>9.5cm</Width>
              <ZIndex>15</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <VerticalAlign>Top</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox8">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!VendorEmail.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>7.54964cm</Top>
              <Height>0.6cm</Height>
              <Width>9.5cm</Width>
              <ZIndex>16</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox15">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!VendorPhoneNo.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>6.94964cm</Top>
              <Height>0.6cm</Height>
              <Width>9.5cm</Width>
              <ZIndex>17</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox16">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!BuyfromAddress_PurchaseHeader.Value + " " + First(Fields!BuyfromAddress2_PurchaseHeader.Value, "DataSet_Result") + " " + First(Fields!BuyfromCity_PurchaseHeader.Value, "DataSet_Result") + " " + First(Fields!BuyfromPostCode_PurchaseHeader.Value, "DataSet_Result") + " " + First(Fields!BuyfromCounty_PurchaseHeader.Value, "DataSet_Result") + " " + First(Fields!BuyfromCountryRegionCode_PurchaseHeader.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>5.74964cm</Top>
              <Height>1.2cm</Height>
              <Width>9.5cm</Width>
              <ZIndex>18</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox21">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Fields!BuyfromVendorName1_PurchaseHeader.Value</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>5.14964cm</Top>
              <Height>0.6cm</Height>
              <Width>9.5cm</Width>
              <ZIndex>19</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style />
        </PageHeader>
        <PageFooter>
          <Height>2.77812cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="BuyerApprovalTxt2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>S.03.F.02</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>BuyerApprovalTxt</rd:DefaultName>
              <Top>0.6525cm</Top>
              <Left>0.7341cm</Left>
              <Height>0.6cm</Height>
              <Width>2.5cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="BuyerApprovalTxt">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!BuyerApprovalTxt.Value, "DataSet_Result")</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>BuyerApprovalTxt</rd:DefaultName>
              <Top>0.6525cm</Top>
              <Left>16.47485cm</Left>
              <Height>0.6cm</Height>
              <Width>2.5cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <InteractiveHeight>11in</InteractiveHeight>
        <InteractiveWidth>8.5in</InteractiveWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <TopMargin>0.5cm</TopMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function

Shared offset as Integer
Shared newPage as Object
Shared currentgroup1 as Object
Shared currentgroup2 as Object
Shared currentgroup3 as Object
Public Function GetGroupPageNumber(NewPage as Boolean, pagenumber as Integer) as Object
  If NewPage
    offset = pagenumber - 1
  End If
  Return pagenumber - offset
End Function

Public Function IsNewPage(group1 as Object, group2 as Object, group3 as Object) As Boolean
newPage = FALSE
If Not (group1 = currentgroup1)
    newPage = TRUE
    currentgroup1 = group1
    currentgroup2 = group2
    currentgroup3 = group3
     ELSE 
      If Not (group2 = currentgroup2)
        newPage = TRUE 
        currentgroup2 = group2
        currentgroup3 = group3
        ELSE
          If Not (group3 = currentgroup3)
          newPage = TRUE 
          currentgroup3 = group3
         End If
     End If
  End If
Return newPage
End Function

Shared Data1 as Object

Public Function GetData(Num as Integer, Group as integer) as Object
if Group = 1 then
   Return Cstr(Choose(Num, Split(Cstr(Data1),Chr(177))))
End If
End Function

Public Function SetData(NewData as Object,Group as integer)
  If Group = 1 and NewData &lt;&gt; "" Then
      Data1 = NewData
  End If
End Function

Shared TotalPage as Integer
Public Function SetTotalPage(Value as Integer)
        TotalPage = Value
End Function

Public Function GetTotalPage()as Integer
        Return TotalPage
End Function

Shared NewData as Object
Shared Function SetNewData(Value as Object)
        NewData = Value
End Function

Shared Function GetNewData() as Object
        Return NewData
End Function</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>8ddb97f5-9975-4099-a7e3-746e9cb53ba2</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="DocType_PurchHeader">
          <DataField>DocType_PurchHeader</DataField>
        </Field>
        <Field Name="CurrencyCode_PurchaseHeader">
          <DataField>CurrencyCode_PurchaseHeader</DataField>
        </Field>
        <Field Name="LCYCode">
          <DataField>LCYCode</DataField>
        </Field>
        <Field Name="DocumentNo">
          <DataField>DocumentNo</DataField>
        </Field>
        <Field Name="PurchaseOrderTxt">
          <DataField>PurchaseOrderTxt</DataField>
        </Field>
        <Field Name="OrderNoTxt">
          <DataField>OrderNoTxt</DataField>
        </Field>
        <Field Name="PrintDateTxt">
          <DataField>PrintDateTxt</DataField>
        </Field>
        <Field Name="PrintDate">
          <DataField>PrintDate</DataField>
        </Field>
        <Field Name="Picture">
          <DataField>Picture</DataField>
        </Field>
        <Field Name="SupplierTxt">
          <DataField>SupplierTxt</DataField>
        </Field>
        <Field Name="BuyfromVendorName1_PurchaseHeader">
          <DataField>BuyfromVendorName1_PurchaseHeader</DataField>
        </Field>
        <Field Name="BuyfromAddress_PurchaseHeader">
          <DataField>BuyfromAddress_PurchaseHeader</DataField>
        </Field>
        <Field Name="BuyfromAddress2_PurchaseHeader">
          <DataField>BuyfromAddress2_PurchaseHeader</DataField>
        </Field>
        <Field Name="BuyfromCity_PurchaseHeader">
          <DataField>BuyfromCity_PurchaseHeader</DataField>
        </Field>
        <Field Name="BuyfromPostCode_PurchaseHeader">
          <DataField>BuyfromPostCode_PurchaseHeader</DataField>
        </Field>
        <Field Name="BuyfromCounty_PurchaseHeader">
          <DataField>BuyfromCounty_PurchaseHeader</DataField>
        </Field>
        <Field Name="BuyfromCountryRegionCode_PurchaseHeader">
          <DataField>BuyfromCountryRegionCode_PurchaseHeader</DataField>
        </Field>
        <Field Name="VendorPhoneNo">
          <DataField>VendorPhoneNo</DataField>
        </Field>
        <Field Name="VendorEmail">
          <DataField>VendorEmail</DataField>
        </Field>
        <Field Name="ShipToTxt">
          <DataField>ShipToTxt</DataField>
        </Field>
        <Field Name="DueDateTxt">
          <DataField>DueDateTxt</DataField>
        </Field>
        <Field Name="QtyTxt">
          <DataField>QtyTxt</DataField>
        </Field>
        <Field Name="UoMTxt">
          <DataField>UoMTxt</DataField>
        </Field>
        <Field Name="LineTotalTxt">
          <DataField>LineTotalTxt</DataField>
        </Field>
        <Field Name="TotalTxt">
          <DataField>TotalTxt</DataField>
        </Field>
        <Field Name="CurrencyTxt">
          <DataField>CurrencyTxt</DataField>
        </Field>
        <Field Name="BuyerApprovalTxt">
          <DataField>BuyerApprovalTxt</DataField>
        </Field>
        <Field Name="SpecTxt">
          <DataField>SpecTxt</DataField>
        </Field>
        <Field Name="UnitCostTxt">
          <DataField>UnitCostTxt</DataField>
        </Field>
        <Field Name="CompanyNameVar">
          <DataField>CompanyNameVar</DataField>
        </Field>
        <Field Name="CompanyAddress">
          <DataField>CompanyAddress</DataField>
        </Field>
        <Field Name="CompanyPhone">
          <DataField>CompanyPhone</DataField>
        </Field>
        <Field Name="CompanyEMail">
          <DataField>CompanyEMail</DataField>
        </Field>
        <Field Name="DescriptionTxt">
          <DataField>DescriptionTxt</DataField>
        </Field>
        <Field Name="VendorCrossRefTxt">
          <DataField>VendorCrossRefTxt</DataField>
        </Field>
        <Field Name="AmountCaption">
          <DataField>AmountCaption</DataField>
        </Field>
        <Field Name="PurchLineInvDiscAmtCaption">
          <DataField>PurchLineInvDiscAmtCaption</DataField>
        </Field>
        <Field Name="SubtotalCaption">
          <DataField>SubtotalCaption</DataField>
        </Field>
        <Field Name="VATAmtLineVATCaption">
          <DataField>VATAmtLineVATCaption</DataField>
        </Field>
        <Field Name="VATAmtLineVATAmtCaption">
          <DataField>VATAmtLineVATAmtCaption</DataField>
        </Field>
        <Field Name="VATAmtSpecCaption">
          <DataField>VATAmtSpecCaption</DataField>
        </Field>
        <Field Name="VATIdentifierCaption">
          <DataField>VATIdentifierCaption</DataField>
        </Field>
        <Field Name="VATAmtLineInvDiscBaseAmtCaption">
          <DataField>VATAmtLineInvDiscBaseAmtCaption</DataField>
        </Field>
        <Field Name="VATAmtLineLineAmtCaption">
          <DataField>VATAmtLineLineAmtCaption</DataField>
        </Field>
        <Field Name="VALVATBaseLCYCaption">
          <DataField>VALVATBaseLCYCaption</DataField>
        </Field>
        <Field Name="TotalCaption">
          <DataField>TotalCaption</DataField>
        </Field>
        <Field Name="PaymentTermsDescCaption">
          <DataField>PaymentTermsDescCaption</DataField>
        </Field>
        <Field Name="ShipmentMethodDescCaption">
          <DataField>ShipmentMethodDescCaption</DataField>
        </Field>
        <Field Name="PrepymtTermsDescCaption">
          <DataField>PrepymtTermsDescCaption</DataField>
        </Field>
        <Field Name="HomePageCaption">
          <DataField>HomePageCaption</DataField>
        </Field>
        <Field Name="EmailIDCaption">
          <DataField>EmailIDCaption</DataField>
        </Field>
        <Field Name="AllowInvoiceDiscCaption">
          <DataField>AllowInvoiceDiscCaption</DataField>
        </Field>
        <Field Name="BuyFromContactPhoneNoLbl">
          <DataField>BuyFromContactPhoneNoLbl</DataField>
        </Field>
        <Field Name="BuyFromContactMobilePhoneNoLbl">
          <DataField>BuyFromContactMobilePhoneNoLbl</DataField>
        </Field>
        <Field Name="BuyFromContactEmailLbl">
          <DataField>BuyFromContactEmailLbl</DataField>
        </Field>
        <Field Name="PayToContactPhoneNoLbl">
          <DataField>PayToContactPhoneNoLbl</DataField>
        </Field>
        <Field Name="PayToContactMobilePhoneNoLbl">
          <DataField>PayToContactMobilePhoneNoLbl</DataField>
        </Field>
        <Field Name="PayToContactEmailLbl">
          <DataField>PayToContactEmailLbl</DataField>
        </Field>
        <Field Name="BuyFromContactPhoneNo">
          <DataField>BuyFromContactPhoneNo</DataField>
        </Field>
        <Field Name="BuyFromContactMobilePhoneNo">
          <DataField>BuyFromContactMobilePhoneNo</DataField>
        </Field>
        <Field Name="BuyFromContactEmail">
          <DataField>BuyFromContactEmail</DataField>
        </Field>
        <Field Name="PayToContactPhoneNo">
          <DataField>PayToContactPhoneNo</DataField>
        </Field>
        <Field Name="PayToContactMobilePhoneNo">
          <DataField>PayToContactMobilePhoneNo</DataField>
        </Field>
        <Field Name="PayToContactEmail">
          <DataField>PayToContactEmail</DataField>
        </Field>
        <Field Name="ReportTitleCopyText">
          <DataField>ReportTitleCopyText</DataField>
        </Field>
        <Field Name="CompanyAddr1">
          <DataField>CompanyAddr1</DataField>
        </Field>
        <Field Name="CompanyAddr2">
          <DataField>CompanyAddr2</DataField>
        </Field>
        <Field Name="CompanyAddr3">
          <DataField>CompanyAddr3</DataField>
        </Field>
        <Field Name="CompanyAddr4">
          <DataField>CompanyAddr4</DataField>
        </Field>
        <Field Name="CompanyInfoPhoneNo">
          <DataField>CompanyInfoPhoneNo</DataField>
        </Field>
        <Field Name="CompanyInfoFaxNo">
          <DataField>CompanyInfoFaxNo</DataField>
        </Field>
        <Field Name="CompanyInfoVATRegNo">
          <DataField>CompanyInfoVATRegNo</DataField>
        </Field>
        <Field Name="CompanyInfoGiroNo">
          <DataField>CompanyInfoGiroNo</DataField>
        </Field>
        <Field Name="CompanyInfoBankName">
          <DataField>CompanyInfoBankName</DataField>
        </Field>
        <Field Name="CompanyInfoBankAccNo">
          <DataField>CompanyInfoBankAccNo</DataField>
        </Field>
        <Field Name="DocDate_PurchHeader">
          <DataField>DocDate_PurchHeader</DataField>
        </Field>
        <Field Name="VATNoText">
          <DataField>VATNoText</DataField>
        </Field>
        <Field Name="VATRegNo_PurchHeader">
          <DataField>VATRegNo_PurchHeader</DataField>
        </Field>
        <Field Name="PurchaserText">
          <DataField>PurchaserText</DataField>
        </Field>
        <Field Name="SalesPurchPersonName">
          <DataField>SalesPurchPersonName</DataField>
        </Field>
        <Field Name="ReferenceText">
          <DataField>ReferenceText</DataField>
        </Field>
        <Field Name="YourRef_PurchHeader">
          <DataField>YourRef_PurchHeader</DataField>
        </Field>
        <Field Name="CompanyAddr5">
          <DataField>CompanyAddr5</DataField>
        </Field>
        <Field Name="CompanyAddr6">
          <DataField>CompanyAddr6</DataField>
        </Field>
        <Field Name="CompanyAddr7">
          <DataField>CompanyAddr7</DataField>
        </Field>
        <Field Name="CompanyAddr8">
          <DataField>CompanyAddr8</DataField>
        </Field>
        <Field Name="BuyFrmVendNo_PurchHeader">
          <DataField>BuyFrmVendNo_PurchHeader</DataField>
        </Field>
        <Field Name="BuyFromAddr1">
          <DataField>BuyFromAddr1</DataField>
        </Field>
        <Field Name="BuyFromAddr2">
          <DataField>BuyFromAddr2</DataField>
        </Field>
        <Field Name="BuyFromAddr3">
          <DataField>BuyFromAddr3</DataField>
        </Field>
        <Field Name="BuyFromAddr4">
          <DataField>BuyFromAddr4</DataField>
        </Field>
        <Field Name="BuyFromAddr5">
          <DataField>BuyFromAddr5</DataField>
        </Field>
        <Field Name="BuyFromAddr6">
          <DataField>BuyFromAddr6</DataField>
        </Field>
        <Field Name="BuyFromAddr7">
          <DataField>BuyFromAddr7</DataField>
        </Field>
        <Field Name="BuyFromAddr8">
          <DataField>BuyFromAddr8</DataField>
        </Field>
        <Field Name="PricesInclVAT_PurchHeader">
          <DataField>PricesInclVAT_PurchHeader</DataField>
        </Field>
        <Field Name="OutputNo">
          <DataField>OutputNo</DataField>
        </Field>
        <Field Name="VATBaseDisc_PurchHeader">
          <DataField>VATBaseDisc_PurchHeader</DataField>
        </Field>
        <Field Name="VATBaseDisc_PurchHeaderFormat">
          <DataField>VATBaseDisc_PurchHeaderFormat</DataField>
        </Field>
        <Field Name="PricesInclVATtxt">
          <DataField>PricesInclVATtxt</DataField>
        </Field>
        <Field Name="PaymentTermsDesc">
          <DataField>PaymentTermsDesc</DataField>
        </Field>
        <Field Name="ShipmentMethodDesc">
          <DataField>ShipmentMethodDesc</DataField>
        </Field>
        <Field Name="PrepmtPaymentTermsDesc">
          <DataField>PrepmtPaymentTermsDesc</DataField>
        </Field>
        <Field Name="ShowInternalInfo">
          <DataField>ShowInternalInfo</DataField>
        </Field>
        <Field Name="TotalText">
          <DataField>TotalText</DataField>
        </Field>
        <Field Name="DimText">
          <DataField>DimText</DataField>
        </Field>
        <Field Name="CompanyInfoPhoneNoCaption">
          <DataField>CompanyInfoPhoneNoCaption</DataField>
        </Field>
        <Field Name="CompanyInfoVATRegNoCaption">
          <DataField>CompanyInfoVATRegNoCaption</DataField>
        </Field>
        <Field Name="CompanyInfoGiroNoCaption">
          <DataField>CompanyInfoGiroNoCaption</DataField>
        </Field>
        <Field Name="CompanyInfoBankNameCaption">
          <DataField>CompanyInfoBankNameCaption</DataField>
        </Field>
        <Field Name="CompanyInfoBankAccNoCaption">
          <DataField>CompanyInfoBankAccNoCaption</DataField>
        </Field>
        <Field Name="OrderNoCaption">
          <DataField>OrderNoCaption</DataField>
        </Field>
        <Field Name="PageCaption">
          <DataField>PageCaption</DataField>
        </Field>
        <Field Name="DocumentDateCaption">
          <DataField>DocumentDateCaption</DataField>
        </Field>
        <Field Name="BuyFrmVendNo_PurchHeaderCaption">
          <DataField>BuyFrmVendNo_PurchHeaderCaption</DataField>
        </Field>
        <Field Name="PricesInclVAT_PurchHeaderCaption">
          <DataField>PricesInclVAT_PurchHeaderCaption</DataField>
        </Field>
        <Field Name="HdrDimCaption">
          <DataField>HdrDimCaption</DataField>
        </Field>
        <Field Name="LineAmt_PurchLine">
          <DataField>LineAmt_PurchLine</DataField>
        </Field>
        <Field Name="LineAmt_PurchLineFormat">
          <DataField>LineAmt_PurchLineFormat</DataField>
        </Field>
        <Field Name="LineNo_PurchLine">
          <DataField>LineNo_PurchLine</DataField>
        </Field>
        <Field Name="AllowInvDisctxt">
          <DataField>AllowInvDisctxt</DataField>
        </Field>
        <Field Name="Type_PurchLine">
          <DataField>Type_PurchLine</DataField>
        </Field>
        <Field Name="No_PurchLine">
          <DataField>No_PurchLine</DataField>
        </Field>
        <Field Name="CrossReferenceNo_PurchaseLine">
          <DataField>CrossReferenceNo_PurchaseLine</DataField>
        </Field>
        <Field Name="Desc_PurchLine">
          <DataField>Desc_PurchLine</DataField>
        </Field>
        <Field Name="ExpectedReceiptDate_PurchaseLine">
          <DataField>ExpectedReceiptDate_PurchaseLine</DataField>
        </Field>
        <Field Name="Qty_PurchLine">
          <DataField>Qty_PurchLine</DataField>
        </Field>
        <Field Name="Qty_PurchLineFormat">
          <DataField>Qty_PurchLineFormat</DataField>
        </Field>
        <Field Name="UOM_PurchLine">
          <DataField>UOM_PurchLine</DataField>
        </Field>
        <Field Name="DirUnitCost_PurchLine">
          <DataField>DirUnitCost_PurchLine</DataField>
        </Field>
        <Field Name="DirUnitCost_PurchLineFormat">
          <DataField>DirUnitCost_PurchLineFormat</DataField>
        </Field>
        <Field Name="LineDisc_PurchLine">
          <DataField>LineDisc_PurchLine</DataField>
        </Field>
        <Field Name="LineDisc_PurchLineFormat">
          <DataField>LineDisc_PurchLineFormat</DataField>
        </Field>
        <Field Name="LineAmt2_PurchLine">
          <DataField>LineAmt2_PurchLine</DataField>
        </Field>
        <Field Name="LineAmt2_PurchLineFormat">
          <DataField>LineAmt2_PurchLineFormat</DataField>
        </Field>
        <Field Name="AllowInvDisc_PurchLine">
          <DataField>AllowInvDisc_PurchLine</DataField>
        </Field>
        <Field Name="HomePage">
          <DataField>HomePage</DataField>
        </Field>
        <Field Name="EMail">
          <DataField>EMail</DataField>
        </Field>
        <Field Name="VATIdentifier_PurchLine">
          <DataField>VATIdentifier_PurchLine</DataField>
        </Field>
        <Field Name="InvDiscAmt_PurchLine">
          <DataField>InvDiscAmt_PurchLine</DataField>
        </Field>
        <Field Name="InvDiscAmt_PurchLineFormat">
          <DataField>InvDiscAmt_PurchLineFormat</DataField>
        </Field>
        <Field Name="TotalInclVAT">
          <DataField>TotalInclVAT</DataField>
        </Field>
        <Field Name="TotalInclVATFormat">
          <DataField>TotalInclVATFormat</DataField>
        </Field>
        <Field Name="TotalInclVATText">
          <DataField>TotalInclVATText</DataField>
        </Field>
        <Field Name="VATAmount">
          <DataField>VATAmount</DataField>
        </Field>
        <Field Name="VATAmountFormat">
          <DataField>VATAmountFormat</DataField>
        </Field>
        <Field Name="TotalExclVATText">
          <DataField>TotalExclVATText</DataField>
        </Field>
        <Field Name="VATDiscountAmount">
          <DataField>VATDiscountAmount</DataField>
        </Field>
        <Field Name="VATDiscountAmountFormat">
          <DataField>VATDiscountAmountFormat</DataField>
        </Field>
        <Field Name="VATBaseAmount">
          <DataField>VATBaseAmount</DataField>
        </Field>
        <Field Name="VATBaseAmountFormat">
          <DataField>VATBaseAmountFormat</DataField>
        </Field>
        <Field Name="TotalAmountInclVAT">
          <DataField>TotalAmountInclVAT</DataField>
        </Field>
        <Field Name="TotalAmountInclVATFormat">
          <DataField>TotalAmountInclVATFormat</DataField>
        </Field>
        <Field Name="TotalSubTotal">
          <DataField>TotalSubTotal</DataField>
        </Field>
        <Field Name="TotalSubTotalFormat">
          <DataField>TotalSubTotalFormat</DataField>
        </Field>
        <Field Name="TotalInvoiceDiscountAmount">
          <DataField>TotalInvoiceDiscountAmount</DataField>
        </Field>
        <Field Name="TotalInvoiceDiscountAmountFormat">
          <DataField>TotalInvoiceDiscountAmountFormat</DataField>
        </Field>
        <Field Name="TotalAmount">
          <DataField>TotalAmount</DataField>
        </Field>
        <Field Name="TotalAmountFormat">
          <DataField>TotalAmountFormat</DataField>
        </Field>
        <Field Name="DirectUniCostCaption">
          <DataField>DirectUniCostCaption</DataField>
        </Field>
        <Field Name="PurchLineLineDiscCaption">
          <DataField>PurchLineLineDiscCaption</DataField>
        </Field>
        <Field Name="VATDiscountAmountCaption">
          <DataField>VATDiscountAmountCaption</DataField>
        </Field>
        <Field Name="No_PurchLineCaption">
          <DataField>No_PurchLineCaption</DataField>
        </Field>
        <Field Name="Desc_PurchLineCaption">
          <DataField>Desc_PurchLineCaption</DataField>
        </Field>
        <Field Name="Qty_PurchLineCaption">
          <DataField>Qty_PurchLineCaption</DataField>
        </Field>
        <Field Name="UOM_PurchLineCaption">
          <DataField>UOM_PurchLineCaption</DataField>
        </Field>
        <Field Name="VATIdentifier_PurchLineCaption">
          <DataField>VATIdentifier_PurchLineCaption</DataField>
        </Field>
        <Field Name="LineDimCaption">
          <DataField>LineDimCaption</DataField>
        </Field>
        <Field Name="PayToVendNo_PurchHeader">
          <DataField>PayToVendNo_PurchHeader</DataField>
        </Field>
        <Field Name="VendAddr8">
          <DataField>VendAddr8</DataField>
        </Field>
        <Field Name="VendAddr7">
          <DataField>VendAddr7</DataField>
        </Field>
        <Field Name="VendAddr6">
          <DataField>VendAddr6</DataField>
        </Field>
        <Field Name="VendAddr5">
          <DataField>VendAddr5</DataField>
        </Field>
        <Field Name="VendAddr4">
          <DataField>VendAddr4</DataField>
        </Field>
        <Field Name="VendAddr3">
          <DataField>VendAddr3</DataField>
        </Field>
        <Field Name="VendAddr2">
          <DataField>VendAddr2</DataField>
        </Field>
        <Field Name="VendAddr1">
          <DataField>VendAddr1</DataField>
        </Field>
        <Field Name="PaymentDetailsCaption">
          <DataField>PaymentDetailsCaption</DataField>
        </Field>
        <Field Name="VendNoCaption">
          <DataField>VendNoCaption</DataField>
        </Field>
        <Field Name="SellToCustNo_PurchHeader">
          <DataField>SellToCustNo_PurchHeader</DataField>
        </Field>
        <Field Name="ShipToAddr1">
          <DataField>ShipToAddr1</DataField>
        </Field>
        <Field Name="ShipToAddr2">
          <DataField>ShipToAddr2</DataField>
        </Field>
        <Field Name="ShipToAddr3">
          <DataField>ShipToAddr3</DataField>
        </Field>
        <Field Name="ShipToAddr4">
          <DataField>ShipToAddr4</DataField>
        </Field>
        <Field Name="ShipToAddr5">
          <DataField>ShipToAddr5</DataField>
        </Field>
        <Field Name="ShipToAddr6">
          <DataField>ShipToAddr6</DataField>
        </Field>
        <Field Name="ShipToAddr7">
          <DataField>ShipToAddr7</DataField>
        </Field>
        <Field Name="ShipToAddr8">
          <DataField>ShipToAddr8</DataField>
        </Field>
        <Field Name="ShiptoAddressCaption">
          <DataField>ShiptoAddressCaption</DataField>
        </Field>
        <Field Name="SellToCustNo_PurchHeaderCaption">
          <DataField>SellToCustNo_PurchHeaderCaption</DataField>
        </Field>
        <Field Name="PrepmtLineAmount">
          <DataField>PrepmtLineAmount</DataField>
        </Field>
        <Field Name="PrepmtLineAmountFormat">
          <DataField>PrepmtLineAmountFormat</DataField>
        </Field>
        <Field Name="TotalPrepmtLineAmount">
          <DataField>TotalPrepmtLineAmount</DataField>
        </Field>
        <Field Name="TotalPrepmtLineAmountFormat">
          <DataField>TotalPrepmtLineAmountFormat</DataField>
        </Field>
        <Field Name="PrepmtInvBufGLAccNo">
          <DataField>PrepmtInvBufGLAccNo</DataField>
        </Field>
        <Field Name="PrepmtInvBufDesc">
          <DataField>PrepmtInvBufDesc</DataField>
        </Field>
        <Field Name="TotalInclVATText2">
          <DataField>TotalInclVATText2</DataField>
        </Field>
        <Field Name="TotalExclVATText2">
          <DataField>TotalExclVATText2</DataField>
        </Field>
        <Field Name="PrepmtInvBufAmt">
          <DataField>PrepmtInvBufAmt</DataField>
        </Field>
        <Field Name="PrepmtInvBufAmtFormat">
          <DataField>PrepmtInvBufAmtFormat</DataField>
        </Field>
        <Field Name="PrepmtVATAmount">
          <DataField>PrepmtVATAmount</DataField>
        </Field>
        <Field Name="PrepmtVATAmountFormat">
          <DataField>PrepmtVATAmountFormat</DataField>
        </Field>
        <Field Name="PrepmtTotalAmountInclVAT">
          <DataField>PrepmtTotalAmountInclVAT</DataField>
        </Field>
        <Field Name="PrepmtTotalAmountInclVATFormat">
          <DataField>PrepmtTotalAmountInclVATFormat</DataField>
        </Field>
        <Field Name="PrepmtVATBaseAmount">
          <DataField>PrepmtVATBaseAmount</DataField>
        </Field>
        <Field Name="PrepmtVATBaseAmountFormat">
          <DataField>PrepmtVATBaseAmountFormat</DataField>
        </Field>
        <Field Name="PrepmtInvBuDescCaption">
          <DataField>PrepmtInvBuDescCaption</DataField>
        </Field>
        <Field Name="PrepmtInvBufGLAccNoCaption">
          <DataField>PrepmtInvBufGLAccNoCaption</DataField>
        </Field>
        <Field Name="PrepaymentSpecCaption">
          <DataField>PrepaymentSpecCaption</DataField>
        </Field>
        <Field Name="PrepmtLoopLineNo">
          <DataField>PrepmtLoopLineNo</DataField>
        </Field>
        <Field Name="DummyColumn">
          <DataField>DummyColumn</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>
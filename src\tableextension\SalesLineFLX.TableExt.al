tableextension 60003 "Sales Line FLX" extends "Sales Line"
{
    fields
    {
        field(60000; "Piece FLX"; Integer)
        {
            Caption = 'Piece';
            ToolTip = 'Specifies the value of the Piece field.';
            trigger OnValidate()
            begin
                FlexatiSalesManagement.UpdateSalesLineQuantity(Rec);
            end;
        }
        field(60001; "Hose Length FLX"; Decimal)
        {
            Caption = 'Hose Length';
            ToolTip = 'Specifies the value of the Hose Lenght field.';
            trigger OnValidate()
            begin
                FlexatiSalesManagement.UpdateSalesLineQuantity(Rec);

                // CustomerItemDescSetup.SetRange("Customer No.", Rec."Sell-to Customer No.");
                // CustomerItemDescSetup.SetRange("Item No.", Rec."No.");
                // CustomerItemDescSetup.SetRange("Hose Lenght", Rec."Hose Length FLX");
                // CustomerItemDescSetup.SetRange("UoM Code", Rec."Unit of Measure Code");
                // if CustomerItemDescSetup.FindFirst() then
                //     Rec.Validate(Description, CustomerItemDescSetup.Description);
            end;
        }
        field(60002; "Unit Weight (Base) FLX"; Decimal)
        {
            Caption = 'Unit Weight (Base)';
            ToolTip = 'Specifies the value of the Coil Weight field.';
        }
        // field(60003; "Production Order Status FLX"; Enum "Production Order Status")
        // {
        //     Caption = 'Production Order Status';
        // }
        field(60003; "Production Order Status FLX"; Enum "Production Order Status")
        {
            Caption = 'Production Order Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Production Order".Status where("No." = field("Production Order No. FLX")));
            ToolTip = 'Specifies the value of the Production Order Status field.';
        }
        field(60004; "Production Order No. FLX"; Code[20])
        {
            Caption = 'Production Order No.';
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(60005; "Sell-to Customer Name FLX"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Sell-to Customer Name" where("Document Type" = field("Document Type"), "No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        // field(60006; "Your Reference FF FLX"; Text[35])
        // {
        //     Caption = 'Your Reference FlowField';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Sales Header"."Your Reference" where("Document Type" = field("Document Type"), "No." = field("Document No.")));
        // }
        field(60007; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Ship-to Code" where("Document Type" = field("Document Type"), "No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(60008; "Flexati Shipment Date FLX"; Date)
        {
            Caption = 'Flexati Shipment Date';
            ToolTip = 'Specifies the value of the Flexati Shipment Date field.';
        }
        field(60009; "Production Order Line No. FLX"; Integer)
        {
            Caption = 'Production Order Line No.';
            AllowInCustomizations = Always;
        }
        field(60010; "Your Reference FLX"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
            trigger OnValidate()
            var
                LotNoInformation: Record "Lot No. Information";
                PackageNoInformation: Record "Package No. Information";
                ProdOrderLine: Record "Prod. Order Line";
            begin
                LotNoInformation.SetRange("Sales Order No. FLX", Rec."Document No.");
                LotNoInformation.SetRange("Sales Order Line No. FLX", Rec."Line No.");
                LotNoInformation.ModifyAll("Your Reference FLX", Rec."Your Reference FLX", true);

                PackageNoInformation.SetRange("Sales Order No. FLX", Rec."Document No.");
                PackageNoInformation.SetRange("Sales Order Line No. FLX", Rec."Line No.");
                PackageNoInformation.ModifyAll("Your Reference FLX", Rec."Your Reference FLX", true);

                ProdOrderLine.SetRange("Prod. Order No.", Rec."Production Order No. FLX");
                ProdOrderLine.SetRange("Line No.", Rec."Production Order Line No. FLX");
                ProdOrderLine.ModifyAll("Your Reference FLX", Rec."Your Reference FLX", true);
            end;
        }
        field(60006; "Custom Code FLX"; Code[20])
        {
            Caption = 'Custom Code';
            TableRelation = "Customer-Item Desc. Setup FLX"."Custom Code" where("Customer No." = field("Sell-to Customer No."), "Item No." = field("No."));
            ToolTip = 'Specifies the value of the Custom Code field.';
            trigger OnValidate()
            var
                CustomerItemDescSetup: Record "Customer-Item Desc. Setup FLX";
            begin
                CustomerItemDescSetup.SetRange("Customer No.", Rec."Sell-to Customer No.");
                CustomerItemDescSetup.SetRange("Item No.", Rec."No.");
                CustomerItemDescSetup.SetRange("Custom Code", Rec."Custom Code FLX");
                if CustomerItemDescSetup.FindFirst() then
                    Rec.Validate(Description, CustomerItemDescSetup.Description);
            end;
        }
        field(60011; "Available Inventory FLX"; Decimal)
        {
            Caption = 'Available Inventory';
            ToolTip = 'Specifies the value of the Available Inventory field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("No."), "Include In Calcualtion FLX" = const(true)));
        }
    }
    var
        FlexatiSalesManagement: Codeunit "Flexati Sales Management FLX";
}
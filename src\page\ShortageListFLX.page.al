page 60027 "Shortage List FLX"
{
    ApplicationArea = All;
    Caption = 'Shortage List';
    PageType = List;
    Editable = false;
    SourceTable = "Shortage List Detail FLX";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Register Type"; Rec."Register Type")
                {
                }
                field(Date; Rec.Date)
                {
                }
                field("Document No."; Rec."Document No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Quantity On Reject Location"; Rec."Quantity On Reject Location")
                {
                }
                field("Quantity On Subcontractors"; Rec."Quantity On Subcontractors")
                {
                }
                field("Quantity On UHD Location"; Rec."Quantity On UHD Location")
                {
                }
                field("Quantity On WHSE Location"; Rec."Quantity On WHSE Location")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name/Description"; Rec."Source Name/Description")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.SetRange("User Id", UserId()); //BRST.006.07 - Added Line
    end;
}
